<?php
/**
 * Rychlá oprava problému s vytvářením inventury
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🚀 Rychlá oprava problému s vytvářením inventury</h1>";

try {
    session_start();
    
    if (!isLoggedIn()) {
        logMessage("❌ Uživatel není přihlášen", true);
        echo "<p><a href='index.html'>Přihlásit se</a></p>";
        exit;
    }
    
    $currentUser = getCurrentUser();
    logMessage("✅ Uživatel je přihlášen: " . $currentUser['username']);
    
    $pdo = getDbConnection();
    logMessage("✅ Databázové připojení úspěšné");
    
    // Kontrola a vytvoření tabulky inventory_sessions
    echo "<h2>🔧 Kontrola tabulky inventory_sessions:</h2>";
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_sessions'");
    if ($stmt->rowCount() == 0) {
        logMessage("❌ Tabulka inventory_sessions neexistuje, vytvářím...");
        
        $createTableSQL = "
            CREATE TABLE `inventory_sessions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `end_time` timestamp NULL DEFAULT NULL,
                `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
                `session_name` varchar(255) DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ";
        
        $pdo->exec($createTableSQL);
        logMessage("✅ Tabulka inventory_sessions vytvořena");
    } else {
        logMessage("✅ Tabulka inventory_sessions existuje");
    }
    
    // Test přímého vytvoření inventury
    echo "<h2>🧪 Test vytvoření inventury:</h2>";
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO inventory_sessions (user_id, start_time, status)
            VALUES (:user_id, NOW(), 'active')
        ");
        
        $result = $stmt->execute(['user_id' => $currentUser['id']]);
        
        if ($result) {
            $sessionId = $pdo->lastInsertId();
            logMessage("✅ Inventura úspěšně vytvořena s ID: $sessionId");
            
            // Zobrazení detailů
            $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
            $stmt->execute(['id' => $sessionId]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo "<h3>📋 Detaily vytvořené inventury:</h3>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            foreach ($session as $key => $value) {
                echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
            }
            echo "</table>";
            
        } else {
            logMessage("❌ Nepodařilo se vytvořit inventuru", true);
        }
        
    } catch (Exception $e) {
        logMessage("❌ Chyba při vytváření inventury: " . $e->getMessage(), true);
    }
    
    // Vytvoření zjednodušeného API endpointu
    echo "<h2>🛠️ Vytvoření zjednodušeného API:</h2>";
    
    $simpleApiContent = '<?php
session_start();
header("Content-Type: application/json");

require_once __DIR__ . "/../utils/database.php";
require_once __DIR__ . "/../utils/auth.php";

try {
    if (!isLoggedIn()) {
        http_response_code(401);
        echo json_encode(["error" => "Not logged in"]);
        exit;
    }
    
    $user = getCurrentUser();
    $pdo = getDbConnection();
    
    // Kontrola existence tabulky
    $stmt = $pdo->query("SHOW TABLES LIKE \'inventory_sessions\'");
    if ($stmt->rowCount() == 0) {
        // Vytvoření tabulky
        $createTableSQL = "
            CREATE TABLE `inventory_sessions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `end_time` timestamp NULL DEFAULT NULL,
                `status` enum(\'active\',\'completed\',\'cancelled\') NOT NULL DEFAULT \'active\',
                `session_name` varchar(255) DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ";
        $pdo->exec($createTableSQL);
    }
    
    // Vytvoření inventury
    $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (:user_id)");
    $stmt->execute(["user_id" => $user["id"]]);
    
    $sessionId = $pdo->lastInsertId();
    
    echo json_encode([
        "success" => true,
        "session_id" => $sessionId,
        "message" => "Inventura byla úspěšně vytvořena"
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage()]);
}
?>';
    
    file_put_contents(__DIR__ . '/api/simple_create_session.php', $simpleApiContent);
    logMessage("✅ Zjednodušené API vytvořeno: api/simple_create_session.php");
    
    // Test zjednodušeného API
    echo "<h2>🧪 Test zjednodušeného API:</h2>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_create_session.php');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, '{}');
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_COOKIE, session_name() . '=' . session_id());
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>HTTP kód:</strong> $httpCode</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    if ($httpCode == 200) {
        $jsonResponse = json_decode($response, true);
        if ($jsonResponse && isset($jsonResponse['success'])) {
            logMessage("✅ Zjednodušené API funguje!");
        }
    }
    
    // JavaScript test
    echo "<h2>🖥️ JavaScript test:</h2>";
    echo "<button onclick='testSimpleAPI()' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test vytvoření inventury</button>";
    echo "<div id='result' style='margin-top: 10px;'></div>";
    
    echo "<script>
    function testSimpleAPI() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '<p style=\"color: blue;\">Testování...</p>';
        
        fetch('api/simple_create_session.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = '<p style=\"color: green;\">✅ Inventura úspěšně vytvořena! ID: ' + data.session_id + '</p>';
            } else {
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ Chyba: ' + (data.error || 'Neznámá chyba') + '</p>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ Chyba: ' + error.message + '</p>';
        });
    }
    </script>";
    
    // Doporučení
    echo "<h2>💡 Doporučení:</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>Pokud zjednodušené API funguje:</h3>";
    echo "<ol style='color: #155724;'>";
    echo "<li>Upravte JavaScript v aplikaci, aby používal <code>api/simple_create_session.php</code> místo <code>api/inventory.php?action=sessions</code></li>";
    echo "<li>Nebo opravte původní API podle fungujícího kódu</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    logMessage("❌ Chyba: " . $e->getMessage(), true);
    echo "<pre style='background: #f8d7da; padding: 10px;'>";
    echo "Soubor: " . $e->getFile() . "\n";
    echo "Řádek: " . $e->getLine() . "\n";
    echo "Chyba: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
    echo "</pre>";
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='index.html' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Hlavní aplikace</a>";
echo "</p>";
?>
