<?php
/**
 * Skript pro resetování hesel všech uživatelů
 * 
 * Tento skript nastaví hesla všech uživatelů na jejich uživatelská j<PERSON>na.
 * Pro uživatele admin nastaví heslo na admin123.
 */

require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
$pdo = getDbConnection();

// Získání seznamu uživatelů
$stmt = $pdo->prepare("SELECT id, username, password, role, active FROM inventory_users");
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h1>Seznam uživatelů před resetováním hesel</h1>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Password</th><th>Role</th><th>Active</th></tr>";
foreach ($users as $user) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($user['id']) . "</td>";
    echo "<td>" . htmlspecialchars($user['username']) . "</td>";
    echo "<td>" . htmlspecialchars($user['password']) . "</td>";
    echo "<td>" . htmlspecialchars($user['role']) . "</td>";
    echo "<td>" . ($user['active'] ? 'Ano' : 'Ne') . "</td>";
    echo "</tr>";
}
echo "</table>";

// Resetování hesel
$updatedCount = 0;
$errors = [];

foreach ($users as $user) {
    try {
        // Nastavení hesla na stejnou hodnotu jako uživatelské jméno
        $newPassword = $user['username'];
        
        // Pro admin uživatele nastavíme heslo na admin123
        if (strtolower($user['username']) === 'admin') {
            $newPassword = 'admin123';
        }
        
        // Aktualizace hesla
        $updateStmt = $pdo->prepare("UPDATE inventory_users SET password = :password WHERE id = :id");
        $updateStmt->execute([
            'id' => $user['id'],
            'password' => $newPassword
        ]);
        
        $updatedCount++;
        
        echo "<p>Heslo pro uživatele " . htmlspecialchars($user['username']) . " bylo nastaveno na: " . htmlspecialchars($newPassword) . "</p>";
    } catch (Exception $e) {
        $errors[] = "Chyba při aktualizaci hesla pro uživatele {$user['username']}: " . $e->getMessage();
    }
}

echo "<h2>Výsledek resetování hesel</h2>";
echo "<p>Počet aktualizovaných uživatelů: $updatedCount</p>";

if (!empty($errors)) {
    echo "<h3>Chyby při aktualizaci</h3>";
    echo "<ul>";
    foreach ($errors as $error) {
        echo "<li>" . htmlspecialchars($error) . "</li>";
    }
    echo "</ul>";
}

// Získání seznamu uživatelů po resetování
$stmt = $pdo->prepare("SELECT id, username, password, role, active FROM inventory_users");
$stmt->execute();
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h1>Seznam uživatelů po resetování hesel</h1>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Username</th><th>Password</th><th>Role</th><th>Active</th></tr>";
foreach ($users as $user) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($user['id']) . "</td>";
    echo "<td>" . htmlspecialchars($user['username']) . "</td>";
    echo "<td>" . htmlspecialchars($user['password']) . "</td>";
    echo "<td>" . htmlspecialchars($user['role']) . "</td>";
    echo "<td>" . ($user['active'] ? 'Ano' : 'Ne') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h1>Přihlašovací údaje</h1>";
echo "<p>Nyní se můžete přihlásit pomocí následujících údajů:</p>";
echo "<ul>";
foreach ($users as $user) {
    $password = (strtolower($user['username']) === 'admin') ? 'admin123' : $user['username'];
    echo "<li><strong>Uživatelské jméno:</strong> {$user['username']}, <strong>Heslo:</strong> $password</li>";
}
echo "</ul>";

echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
