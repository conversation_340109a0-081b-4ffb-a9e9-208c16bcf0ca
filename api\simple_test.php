<?php
/**
 * Jednoduchý API endpoint pro testování bez autentifikace
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../utils/database.php';

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Získání akce z query stringu
$action = $_GET['action'] ?? '';

try {
    $pdo = getDbConnection();
    
    switch ($action) {
        case 'active-sessions':
            // Získání aktivních relací
            $stmt = $pdo->query("
                SELECT 
                    s.id,
                    s.user_id,
                    s.start_time,
                    s.status,
                    u.username
                FROM inventory_sessions s
                LEFT JOIN inventory_users u ON s.user_id = u.id
                WHERE s.status = 'active'
                ORDER BY s.id DESC
            ");
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['sessions' => $sessions]);
            break;
            
        case 'total-entries':
            // Získání celkových záznamů
            $sessionId = $_GET['session_id'] ?? null;
            
            if (!$sessionId) {
                http_response_code(400);
                echo json_encode(['error' => 'session_id je povinné']);
                exit;
            }
            
            $stmt = $pdo->prepare("
                SELECT 
                    it.id,
                    it.product_id,
                    p.code AS ean_code,
                    p.name AS product_name,
                    c.name AS category,
                    p.pricebuy,
                    COALESCE(t.rate, 0) AS tax_rate,
                    p.pricesell,
                    COALESCE(s.units, 0) AS current_stock,
                    it.total_zadane_mnozstvi AS zadane_mnozstvi,
                    (it.total_zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
                    it.last_updated
                FROM inventory_totals it
                LEFT JOIN products p ON it.product_id = p.id
                LEFT JOIN categories c ON p.category = c.id
                LEFT JOIN taxes t ON p.taxcat = t.id
                LEFT JOIN stockcurrent s ON p.id = s.product
                WHERE it.session_id = ?
                ORDER BY p.name
            ");
            
            $stmt->execute([$sessionId]);
            $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Příprava dat pro odpověď
            $responseData = [];
            foreach ($entries as $entry) {
                $responseData[] = [
                    'id' => $entry['id'],
                    'product_id' => $entry['product_id'],
                    'ean_code' => $entry['ean_code'],
                    'product_name' => $entry['product_name'],
                    'category' => $entry['category'],
                    'pricebuy' => $entry['pricebuy'],
                    'tax_rate' => $entry['tax_rate'],
                    'pricesell' => $entry['pricesell'],
                    'price_with_tax' => $entry['pricesell'] * (1 + $entry['tax_rate']),
                    'current_stock' => $entry['current_stock'],
                    'zadane_mnozstvi' => $entry['zadane_mnozstvi'],
                    'difference' => $entry['difference'],
                    'users' => 'Test User',
                    'last_updated' => $entry['last_updated'],
                    'can_edit' => true
                ];
            }
            
            echo json_encode(['entries' => $responseData]);
            break;
            
        case 'entries':
            // Získání individuálních záznamů
            $sessionId = $_GET['session_id'] ?? null;
            
            if (!$sessionId) {
                http_response_code(400);
                echo json_encode(['error' => 'session_id je povinné']);
                exit;
            }
            
            $stmt = $pdo->prepare("
                SELECT 
                    ie.id,
                    ie.product_id,
                    ie.zadane_mnozstvi,
                    ie.user_id,
                    ie.ean_code,
                    ie.last_updated,
                    p.name as product_name,
                    p.code as product_code,
                    u.username
                FROM inventory_entries ie
                LEFT JOIN products p ON ie.product_id = p.id
                LEFT JOIN inventory_users u ON ie.user_id = u.id
                WHERE ie.session_id = ? AND ie.status = 'active'
                ORDER BY ie.last_updated DESC
            ");
            
            $stmt->execute([$sessionId]);
            $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode(['entries' => $entries]);
            break;
            
        case 'test-connection':
            // Test připojení k databázi
            $stmt = $pdo->query("SELECT 1 as test");
            $result = $stmt->fetch();
            
            echo json_encode([
                'status' => 'success',
                'message' => 'Databázové připojení funguje',
                'test_result' => $result['test']
            ]);
            break;
            
        case 'table-counts':
            // Počty záznamů v tabulkách
            $tables = ['inventory_sessions', 'inventory_entries', 'inventory_totals', 'inventory_users'];
            $counts = [];
            
            foreach ($tables as $table) {
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) FROM $table");
                    $counts[$table] = $stmt->fetchColumn();
                } catch (Exception $e) {
                    $counts[$table] = 'Chyba: ' . $e->getMessage();
                }
            }
            
            echo json_encode(['table_counts' => $counts]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Neznámá akce: ' . $action]);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Chyba serveru: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
?>
