<?php
/**
 * Zobrazení všech triggerů v databázi
 */

// Načtení pomocných funkcí
require_once 'utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    // Získání všech triggerů
    $stmt = $pdo->query("SHOW TRIGGERS");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Výpis triggerů
    echo "<h1>Triggery v datab<PERSON>zi</h1>";
    echo "<pre>";
    print_r($triggers);
    echo "</pre>";
    
    // Výpis kódu triggerů
    echo "<h2>Kód triggerů</h2>";
    foreach ($triggers as $trigger) {
        $triggerName = $trigger['Trigger'];
        echo "<h3>Trigger: $triggerName</h3>";
        
        $stmt = $pdo->query("SHOW CREATE TRIGGER `$triggerName`");
        $triggerCode = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<pre>";
        print_r($triggerCode);
        echo "</pre>";
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
} catch (PDOException $e) {
    // Výpis chyby
    echo "<h1>Chyba při získávání triggerů</h1>";
    echo "<p>Došlo k chybě při připojení k databázi nebo získávání triggerů:</p>";
    echo "<pre>" . $e->getMessage() . "</pre>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
