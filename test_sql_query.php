<?php
/**
 * Skript pro testování SQL dotazů
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

try {
    // Připojení k databázi
    $pdo = getDbConnection();
    
    echo "<h1>Testování SQL dotazů</h1>";
    
    // Testování dotazu pro inventory_stock_changes
    echo "<h2>Test dotazu pro inventory_stock_changes</h2>";
    
    try {
        $sql = "
            INSERT INTO inventory_stock_changes (
                session_id,
                product_id,
                initial_stock,
                stock_changes_during_inventory
            )
            VALUES (
                1,
                '123',
                10,
                0
            )
            ON DUPLICATE KEY UPDATE
                initial_stock = 10
        ";
        
        echo "<p>SQL dotaz:</p>";
        echo "<pre>" . htmlspecialchars($sql) . "</pre>";
        
        // Spuštění dotazu
        $pdo->exec($sql);
        
        echo "<p style='color: green;'>✓ Dotaz byl úspěšn<PERSON> proveden.</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Chyba při provádění dotazu: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Kontrola existence tabulky inventory_stock_changes
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_stock_changes'");
    $tableExists = $stmt->rowCount() > 0;
    
    echo "<h2>Tabulka inventory_stock_changes</h2>";
    
    if ($tableExists) {
        echo "<p style='color: green;'>✓ Tabulka inventory_stock_changes existuje.</p>";
        
        // Získání struktury tabulky
        $stmt = $pdo->query("DESCRIBE inventory_stock_changes");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Struktura tabulky</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            foreach ($column as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Získání indexů tabulky
        $stmt = $pdo->query("SHOW INDEX FROM inventory_stock_changes");
        $indexes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Indexy tabulky</h3>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Key_name</th><th>Column_name</th><th>Non_unique</th><th>Seq_in_index</th></tr>";
        
        foreach ($indexes as $index) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($index['Key_name']) . "</td>";
            echo "<td>" . htmlspecialchars($index['Column_name']) . "</td>";
            echo "<td>" . htmlspecialchars($index['Non_unique']) . "</td>";
            echo "<td>" . htmlspecialchars($index['Seq_in_index']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p style='color: red;'>✗ Tabulka inventory_stock_changes neexistuje!</p>";
        
        // Vytvoření tabulky inventory_stock_changes
        echo "<h3>Vytvoření tabulky inventory_stock_changes</h3>";
        
        try {
            $sql = "
                CREATE TABLE IF NOT EXISTS `inventory_stock_changes` (
                  `id` INT AUTO_INCREMENT PRIMARY KEY,
                  `session_id` INT NOT NULL,
                  `product_id` VARCHAR(255) NOT NULL,
                  `initial_stock` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `stock_changes_during_inventory` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `last_update` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  UNIQUE KEY `uk_inventory_stock_changes_session_product` (`session_id`, `product_id`),
                  INDEX `idx_inventory_stock_changes_product` (`product_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ";
            
            echo "<p>SQL dotaz:</p>";
            echo "<pre>" . htmlspecialchars($sql) . "</pre>";
            
            // Spuštění dotazu
            $pdo->exec($sql);
            
            echo "<p style='color: green;'>✓ Tabulka inventory_stock_changes byla úspěšně vytvořena.</p>";
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Chyba při vytváření tabulky: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
