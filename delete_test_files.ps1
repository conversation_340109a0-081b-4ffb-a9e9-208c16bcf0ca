Remove-Item .htaccess, add_last_updated_column.php, add_logging_to_create_entry.php, add_units_column.php, add_units_direct.php, add_units_to_all_tables_in_db.php, add_units_to_all_tables.php, aktualizace_podle_prodeju.php, alternative_solution.php, analyze_database_structure.php, analyze_triggers.php, api_test_dashboard.php, api_test_no_auth.php, auto_sync_after_sale.php, auto_update_inventory_totals.php, capture_sql_queries.php, cleanup.php, complete_diagnosis.php, comprehensive_zadane_mnozstvi_test.php, create_alternative_triggers.php, create_correct_trigger.php, create_correct_triggers.php, create_inventory_stock_changes.php, create_inventory_totals_table.php, create_inventory_triggers.php, create_inventory_update_trigger.php, create_missing_tables.php, create_new_trigger_approach.php, create_previous_stock_table.php, create_simple_trigger.php, create_stock_changes_table.php, create_sync_trigger.php, create_tables.php, create_test_inventory_data.php, create_ticketlines_trigger_new.php, create_ticketlines_trigger.php, create_ticketlines_view.php, create_ticketlines_with_units.php, create_universal_auth.php, create_views_with_units.php, cron_sync_inventory.php, cron_update_inventory.log, cron_update_inventory.php, deactivate_change_type_triggers.log, deactivate_change_type_triggers.php, debug_500_error.php, debug_api_error.php, debug_create_entry.php, debug_inventory_totals_update.php, debug_login_issue.php, debug_login.php, debug_stockcurrent_changes.php, debug_stockcurrent_trigger.php, debug_trigger_execution.php, debug_trigger_file.php, debug_trigger.log, debug_triggers.php, debug_unicenta_sales.php, diagnose_inventory_update.php, diagnose_unicenta_sales.php, direct_sync_inventory_totals.php, direct_update_inventory_quantity.php, direct_update_inventory_totals.php, direct_update_inventory.php, emergency_debug.php, emergency.log, export_inventory.php, final_fix_inventory_update.php, final_solution.php, fix_all_database_objects.php, fix_all_products.php, fix_all_triggers.php, fix_and_test_triggers.php, fix_automatic_update.php, fix_broken_triggers.php, fix_create_entry_complete.php, fix_create_entry.php, fix_create_session.php, fix_diagnose_columns.php, fix_double_deduction_final_solution.php, fix_double_deduction_final.php, fix_double_deduction.php, fix_double_update_trigger.php, fix_full_name_column.php, fix_inventory_sessions_table.php, fix_inventory_sessions.php, fix_inventory_totals_sync.php, fix_inventory_totals_trigger_file.php, fix_inventory_totals_trigger_web.php, fix_inventory_totals_trigger.php, fix_inventory_totals.php, fix_inventory_trigger_correct_update.php, fix_inventory_trigger_no_update.php, fix_inventory_trigger_total_only.php, fix_inventory_triggers.php, fix_inventory_update_triggers.php, fix_log_tables.php, fix_login_issue.php, fix_sales_ticket_issue.php, fix_session_auth.php, fix_session_issues.php, fix_status_column.php, fix_stockcurrent_triggers_correct.php, fix_stockcurrent_triggers.php, fix_tables_direct.php, fix_test_simulation.php, fix_ticketlines_log.php, fix_ticketlines_table.php, fix_ticketlines_triggers.php, fix_total_inventory.php, fix_trigger_error.php, fix_triggers_multiple_updates.php, fix_units_column.php, fix_units.html, fix_updated_column.php, fix_user_passwords.php, fix_validation.php, get_db_info.php, check_all_tables.php, check_all_triggers_final.log, check_all_triggers_final.php, check_all_triggers.php, check_automatic_update.php, check_current_triggers.php, check_database_objects.php, check_database_structure.php, check_database_tables.php, check_database_triggers.php, check_database_views.php, check_db_tables.php, check_duplicate_triggers.php, check_error_log.php, check_change_type_tables.log, check_change_type_tables.php, check_inventory_entries_structure.php, check_inventory_entries_table.php, check_inventory_entries.php, check_inventory_sessions_and_totals.php, check_inventory_sessions_structure.log, check_inventory_sessions_structure.php, check_inventory_sessions.php, check_inventory_stock_changes.php, check_inventory_totals_columns.log, check_inventory_totals_columns.php, check_inventory_totals_detailed.php, check_inventory_totals_structure.php, check_inventory_totals.php, check_multiple_entries.log, check_multiple_entries.php, check_other_tables.php, check_php_files_for_updated.php, check_products_table.php, check_recent_sales.php, check_server_logs.php, check_stockcurrent_log_columns.log, check_stockcurrent_log_columns.php, check_stockcurrent_log.log, check_stockcurrent_log.php, check_stockcurrent_triggers.php, check_table_structure.php, check_tables.php, check_temp_tables.php, check_temporary_tables.php, check_ticketlines_log.php, check_ticketlines_structure.php, check_ticketlines_table.php, check_ticketlines.php, check_tickets_structure.php, check_trigger_status.php, check_trigger.log, check_trigger.php, check_triggers.php, check_unicenta_code.php, check_unicenta_triggers.php, check_updated_column.php, check_user_tables.php, check_user.php, check_users.php, kontrola_triggeru.php, manage_triggers.php, manual_update_inventory_totals.php, minimal_api_test.php, monitor_sales_real_time.php, monitor_sql_queries.php, monitor_ticketlines.php, oprava_aktualizace_mnozstvi.php, OPRAVA_INVENTURY.md, test_api_endpoint.php, test_stockcurrent_update.php, test_trigger_simple.php, view_stockcurrent_logs.php, view_stock_changes.php, install_inventory_triggers.php, INSTALL.md, investigate_double_deduction.php, manual_trigger_test.php, update_inventory_totals.php, fix_units.html, fix_units_column.php -Force
