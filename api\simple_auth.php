<?php
/**
 * Jednoduchý autentifikační API endpoint bez složité validace
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Získání metody požadavku
$method = $_SERVER['REQUEST_METHOD'];

// Získání akce z query stringu
$action = $_GET['action'] ?? '';

// Logování pro debug
error_log("simple_auth.php - metoda: $method, akce: $action");

try {
    switch ($action) {
        case 'login':
            if ($method !== 'POST') {
                sendResponse(['error' => 'Metoda není povolena'], 405);
                break;
            }
            
            // Získání JSON vstupu
            $input = json_decode(file_get_contents('php://input'), true);
            error_log("simple_auth.php - vstupní data: " . print_r($input, true));
            
            if (!$input || !isset($input['username']) || !isset($input['password'])) {
                sendResponse(['error' => 'Chybí username nebo password'], 400);
                break;
            }
            
            $username = trim($input['username']);
            $password = $input['password'];
            
            if (empty($username)) {
                sendResponse(['error' => 'Username nesmí být prázdný'], 400);
                break;
            }
            
            // Zajistíme, že tabulky existují
            ensureTablesExist();
            
            // Autentifikace uživatele
            $user = authenticateUser($username, $password);
            
            if (!$user) {
                error_log("simple_auth.php - autentifikace selhala pro uživatele: $username");
                sendResponse(['error' => 'Neplatné uživatelské jméno nebo heslo'], 401);
                break;
            }
            
            error_log("simple_auth.php - autentifikace úspěšná pro uživatele: $username");
            sendResponse([
                'success' => true,
                'user' => $user
            ]);
            break;
            
        case 'logout':
            if ($method !== 'POST') {
                sendResponse(['error' => 'Metoda není povolena'], 405);
                break;
            }
            
            if (!isLoggedIn()) {
                sendResponse(['error' => 'Uživatel není přihlášen'], 401);
                break;
            }
            
            logout();
            sendResponse(['success' => true]);
            break;
            
        case 'check':
            if ($method !== 'GET') {
                sendResponse(['error' => 'Metoda není povolena'], 405);
                break;
            }
            
            // Zajistíme, že tabulky existují
            try {
                ensureTablesExist();
            } catch (Exception $e) {
                error_log("simple_auth.php - chyba při zajištění tabulek: " . $e->getMessage());
            }
            
            if (!isLoggedIn()) {
                sendResponse(['authenticated' => false]);
                break;
            }
            
            sendResponse([
                'authenticated' => true,
                'user' => getCurrentUser()
            ]);
            break;
            
        case 'test':
            // Testovací endpoint
            $pdo = getDbConnection();
            
            // Kontrola tabulky inventory_users
            $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_users'");
            $tableExists = $stmt->rowCount() > 0;
            
            $userCount = 0;
            $users = [];
            
            if ($tableExists) {
                $stmt = $pdo->query("SELECT username, role, status, active FROM inventory_users");
                $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $userCount = count($users);
            }
            
            sendResponse([
                'database_connected' => true,
                'inventory_users_table_exists' => $tableExists,
                'user_count' => $userCount,
                'users' => $users,
                'session_status' => [
                    'session_id' => session_id(),
                    'logged_in' => isLoggedIn(),
                    'current_user' => getCurrentUser()
                ]
            ]);
            break;
            
        default:
            sendResponse(['error' => 'Neznámá akce: ' . $action], 404);
            break;
    }
    
} catch (Exception $e) {
    error_log("simple_auth.php - chyba: " . $e->getMessage());
    sendResponse([
        'error' => 'Chyba serveru: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ], 500);
}

/**
 * Odeslání JSON odpovědi
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}
?>
