<?php
/**
 * Oprava databázového schématu - změna user_id z VARCHAR na INT
 */

// Spuštění session pouze pokud ještě není spuštěna
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Oprava databázového schématu</h1>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    echo "<h2>1. Připojení k databázi</h2>";
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení úspěšné</p>";
    
    echo "<h2>2. Kontrola aktuálního schématu</h2>";
    
    // Kontrola struktury inventory_sessions
    $stmt = $pdo->query("DESCRIBE inventory_sessions");
    $sessionsColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Aktuální struktura inventory_sessions:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($sessionsColumns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Kontrola struktury inventory_entries
    $stmt = $pdo->query("DESCRIBE inventory_entries");
    $entriesColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Aktuální struktura inventory_entries:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($entriesColumns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. Oprava datových typů</h2>";
    
    // Kontrola, zda je potřeba opravit user_id v inventory_sessions
    $sessionsUserIdType = null;
    foreach ($sessionsColumns as $column) {
        if ($column['Field'] === 'user_id') {
            $sessionsUserIdType = $column['Type'];
            break;
        }
    }
    
    if ($sessionsUserIdType && strpos($sessionsUserIdType, 'varchar') !== false) {
        echo "<p style='color: orange;'>⚠ inventory_sessions.user_id je VARCHAR, opravuji na INT...</p>";
        
        // Nejprve odstraníme foreign key constraints, pokud existují
        try {
            $pdo->exec("ALTER TABLE inventory_sessions DROP FOREIGN KEY inventory_sessions_ibfk_1");
        } catch (Exception $e) {
            // Ignorujeme chybu, pokud constraint neexistuje
        }
        
        // Změníme datový typ
        $pdo->exec("ALTER TABLE inventory_sessions MODIFY COLUMN user_id INT NOT NULL");
        
        // Přidáme foreign key constraint
        $pdo->exec("ALTER TABLE inventory_sessions ADD FOREIGN KEY (user_id) REFERENCES inventory_users(id) ON DELETE CASCADE");
        
        echo "<p style='color: green;'>✓ inventory_sessions.user_id opraveno na INT</p>";
    } else {
        echo "<p style='color: green;'>✓ inventory_sessions.user_id už je INT</p>";
    }
    
    // Kontrola, zda je potřeba opravit user_id v inventory_entries
    $entriesUserIdType = null;
    foreach ($entriesColumns as $column) {
        if ($column['Field'] === 'user_id') {
            $entriesUserIdType = $column['Type'];
            break;
        }
    }
    
    if ($entriesUserIdType && strpos($entriesUserIdType, 'varchar') !== false) {
        echo "<p style='color: orange;'>⚠ inventory_entries.user_id je VARCHAR, opravuji na INT...</p>";
        
        // Nejprve odstraníme foreign key constraints, pokud existují
        try {
            $pdo->exec("ALTER TABLE inventory_entries DROP FOREIGN KEY inventory_entries_ibfk_2");
        } catch (Exception $e) {
            // Ignorujeme chybu, pokud constraint neexistuje
        }
        
        // Změníme datový typ
        $pdo->exec("ALTER TABLE inventory_entries MODIFY COLUMN user_id INT NOT NULL");
        
        // Přidáme foreign key constraint
        $pdo->exec("ALTER TABLE inventory_entries ADD FOREIGN KEY (user_id) REFERENCES inventory_users(id) ON DELETE CASCADE");
        
        echo "<p style='color: green;'>✓ inventory_entries.user_id opraveno na INT</p>";
    } else {
        echo "<p style='color: green;'>✓ inventory_entries.user_id už je INT</p>";
    }
    
    echo "<h2>4. Kontrola po opravě</h2>";
    
    // Znovu zkontrolujeme strukturu
    $stmt = $pdo->query("DESCRIBE inventory_sessions");
    $newSessionsColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($newSessionsColumns as $column) {
        if ($column['Field'] === 'user_id') {
            echo "<p>inventory_sessions.user_id: " . $column['Type'] . "</p>";
            break;
        }
    }
    
    $stmt = $pdo->query("DESCRIBE inventory_entries");
    $newEntriesColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($newEntriesColumns as $column) {
        if ($column['Field'] === 'user_id') {
            echo "<p>inventory_entries.user_id: " . $column['Type'] . "</p>";
            break;
        }
    }
    
    echo "<h2>5. Test vytvoření inventury</h2>";
    
    // Zkusíme vytvořit testovací inventuru
    $user = getCurrentUser();
    if (!$user) {
        // Přihlásíme se jako admin
        $user = authenticateUser('admin', 'admin123');
    }
    
    if ($user) {
        echo "<p>Přihlášený uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
        
        $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (?)");
        $result = $stmt->execute([$user['id']]);
        
        if ($result) {
            $sessionId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✓ Testovací inventura vytvořena s ID: $sessionId</p>";
        } else {
            echo "<p style='color: red;'>✗ Nepodařilo se vytvořit testovací inventuru</p>";
            echo "<p>Chyba: " . print_r($stmt->errorInfo(), true) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Nepodařilo se přihlásit</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
?>
