# Inventory Management System

This web application is designed to manage inventory using the existing UniCentaOPOS database. It allows users to perform inventory checks, track differences between entered quantities and current stock levels, and update quantities in the database after completing inventory.

## Features

- User authentication with different permission levels (admin, manager, user)
- Product search by EAN code
- Inventory entry management
- Stock synchronization with sales
- Inventory completion and export
- Reporting and analytics

## Requirements

- PHP 7.4 or higher
- MySQL/MariaDB
- Web server (Apache, Nginx)
- Modern web browser

## Installation

1. Clone the repository
2. Configure the database connection in `config/database.php`
3. Run the database setup script to create custom tables
4. Configure your web server to point to the project directory
5. Access the application through your web browser

## Project Structure

- `api/` - Backend API endpoints
- `config/` - Configuration files
- `database/` - Database scripts and models
- `js/` - Frontend JavaScript files
- `css/` - Stylesheets
- `assets/` - Images and other assets
- `utils/` - Utility functions
- `index.html` - Main entry point

## License

This project is proprietary and confidential.
