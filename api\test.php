<?php
/**
 * Test API
 *
 * Tento soubor slouží k testování API.
 */

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Získání akce z query stringu
$action = $_GET['action'] ?? 'default';

// Zpracování různých akcí
switch ($action) {
    case 'users':
        // Simulovaná data pro users
        $data = [
            'users' => [
                [
                    'id' => 1,
                    'username' => 'admin',
                    'role' => 'admin',
                    'full_name' => 'Administrator',
                    'email' => null,
                    'active' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'id' => 2,
                    'username' => 'manager',
                    'role' => 'manager',
                    'full_name' => 'Manager',
                    'email' => null,
                    'active' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                [
                    'id' => 3,
                    'username' => 'user',
                    'role' => 'user',
                    'full_name' => 'User',
                    'email' => null,
                    'active' => 1,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]
            ]
        ];
        break;

    case 'sessions':
        // Simulovaná data pro sessions
        $data = [
            'sessions' => [
                [
                    'id' => 1,
                    'start_time' => date('Y-m-d H:i:s'),
                    'end_time' => null,
                    'status' => 'active',
                    'user' => 'admin',
                    'entry_count' => 0
                ],
                [
                    'id' => 2,
                    'start_time' => date('Y-m-d H:i:s', strtotime('-1 day')),
                    'end_time' => date('Y-m-d H:i:s', strtotime('-12 hours')),
                    'status' => 'completed',
                    'user' => 'admin',
                    'entry_count' => 15
                ]
            ]
        ];
        break;

    case 'inventory':
        // Simulovaná data pro inventory
        $data = [
            'inventory' => [
                [
                    'id' => 1,
                    'product_id' => 'PROD001',
                    'ean_code' => '1234567890123',
                    'product_name' => 'Testovací produkt 1',
                    'category' => 'Kategorie 1',
                    'pricebuy' => 100,
                    'tax_rate' => 21,
                    'pricesell' => 200,
                    'current_stock' => 10,
                    'zadane_mnozstvi' => 15,
                    'difference' => 5,
                    'user' => 'admin',
                    'last_updated' => date('Y-m-d H:i:s')
                ],
                [
                    'id' => 2,
                    'product_id' => 'PROD002',
                    'ean_code' => '2345678901234',
                    'product_name' => 'Testovací produkt 2',
                    'category' => 'Kategorie 2',
                    'pricebuy' => 200,
                    'tax_rate' => 21,
                    'pricesell' => 400,
                    'current_stock' => 20,
                    'zadane_mnozstvi' => 15,
                    'difference' => -5,
                    'user' => 'admin',
                    'last_updated' => date('Y-m-d H:i:s')
                ]
            ]
        ];
        break;

    case 'entries':
        // Simulovaná data pro entries
        $data = [
            'entries' => [
                [
                    'id' => 1,
                    'product_id' => 'PROD001',
                    'ean_code' => '1234567890123',
                    'product_name' => 'Testovací produkt 1',
                    'category' => 'Kategorie 1',
                    'pricebuy' => 100,
                    'tax_rate' => 21,
                    'pricesell' => 200,
                    'price_with_tax' => 242,
                    'current_stock' => 10,
                    'zadane_mnozstvi' => 15,
                    'difference' => 5,
                    'user' => 'admin',
                    'user_id' => 1,
                    'last_updated' => date('Y-m-d H:i:s'),
                    'can_edit' => true
                ]
            ]
        ];
        break;

    default:
        // Výchozí odpověď
        $data = [
            'success' => true,
            'message' => 'Test API funguje!',
            'action' => $action,
            'server' => $_SERVER
        ];
        break;
}

// Odeslání odpovědi
echo json_encode($data);
