/* <PERSON><PERSON><PERSON><PERSON><PERSON> styly */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Navigačn<PERSON> li<PERSON> */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
}

/* Karty */
.card {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin-bottom: 20px;
}

.card-header {
    border-radius: 8px 8px 0 0 !important;
}

/* Tabulky */
.table {
    margin-bottom: 0;
}

.table th {
    background-color: #f8f9fa;
}

/* Formuláře */
.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* <PERSON><PERSON><PERSON><PERSON>tka */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-outline-light:hover {
    color: #0d6efd;
    background-color: #fff;
    border-color: #fff;
}

/* Inventurní záznamy */
.inventory-entry {
    padding: 10px;
    border-bottom: 1px solid #dee2e6;
}

.inventory-entry:last-child {
    border-bottom: none;
}

.inventory-entry:hover {
    background-color: #f8f9fa;
}

/* Uživatelské role - skrývání prvků podle role */
.admin-only, .manager-only {
    display: none;
}

/* Responzivní úpravy */
@media (max-width: 768px) {
    .card-body {
        padding: 15px;
    }

    h1 {
        font-size: 1.8rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* Animace */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Tabulka inventurních záznamů */
.inventory-table {
    width: 100%;
    border-collapse: collapse;
}

.inventory-table th, .inventory-table td {
    padding: 10px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.inventory-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.inventory-table tr:hover {
    background-color: #f8f9fa;
}

.inventory-table .actions {
    text-align: right;
}

.inventory-table .actions button {
    margin-left: 5px;
}

/* Rozdíl v inventuře */
.difference-positive {
    color: #198754;
}

.difference-negative {
    color: #dc3545;
}

.difference-zero {
    color: #6c757d;
}

/* Ikona uživatele */
.user-info {
    margin-right: 10px;
    cursor: help;
}

/* Spinner pro načítání */
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

/* Notifikace */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 4px;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    opacity: 0;
    transform: translateY(-20px);
    transition: opacity 0.3s, transform 0.3s;
}

.notification.show {
    opacity: 1;
    transform: translateY(0);
}

.notification-success {
    background-color: #198754;
}

.notification-error {
    background-color: #dc3545;
}

.notification-info {
    background-color: #0dcaf0;
}

/* Stránkování */
.pagination {
    display: flex;
    justify-content: center;
    margin-top: 20px;
}

.pagination .page-item {
    margin: 0 5px;
}

.pagination .page-link {
    color: #0d6efd;
    border-radius: 4px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* Vyhledávací pole */
.search-container {
    position: relative;
    margin-bottom: 20px;
}

.search-container .form-control {
    padding-right: 40px;
}

.search-container .search-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* Filtry */
.filter-container {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

/* Detaily produktu */
.product-details-container {
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
}

/* Tlačítka pro akce */
.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

/* Modální okna */
.modal-header {
    border-radius: 8px 8px 0 0;
}

.modal-content {
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Ikony */
.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    color: #0d6efd;
    padding: 5px;
    transition: color 0.2s;
}

.icon-button:hover {
    color: #0a58ca;
}

.icon-button.delete {
    color: #dc3545;
}

.icon-button.delete:hover {
    color: #bb2d3b;
}

/* Aktivní relace */
.active-session {
    background-color: #e8f4ff;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.active-session h5 {
    margin-bottom: 10px;
}

/* Dokončené relace */
.completed-session {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #198754;
}

/* Zrušené relace */
.cancelled-session {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #dc3545;
}
