<?php
require_once __DIR__ . '/database.php';


/**
 * Univerzální autentifikační funkce pro více tabulek
 */
function authenticateUserUniversal($username, $password) {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    error_log("authenticateUserUniversal - pokus o přihlášení uživatele: " . $username);

    if (empty($username) || empty($password)) {
        error_log("authenticateUserUniversal - prázdné uživatelské j<PERSON>no nebo he<PERSON>lo");
        return false;
    }

    try {
        $pdo = getDbConnection();
        
        // Seznam tabulek a sloupců pro kontrolu uživatelů
        $userTables = [
            [
                "table" => "inventory_users",
                "username_col" => "username", 
                "password_col" => "password",
                "role_col" => "role",
                "active_col" => "active",
                "id_col" => "id"
            ],
            [
                "table" => "people", 
                "username_col" => "name",
                "password_col" => "card", 
                "role_col" => null,
                "active_col" => "visible",
                "id_col" => "id"
            ]
        ];
        
        foreach ($userTables as $tableConfig) {
            $table = $tableConfig["table"];
            $usernameCol = $tableConfig["username_col"];
            $passwordCol = $tableConfig["password_col"];
            $roleCol = $tableConfig["role_col"];
            $activeCol = $tableConfig["active_col"];
            $idCol = $tableConfig["id_col"];
            
            error_log("authenticateUserUniversal - kontrola tabulky: $table");
            
            // Kontrola, zda tabulka existuje
            $stmt = $pdo->query("SHOW TABLES LIKE \"$table\"");
            if ($stmt->rowCount() == 0) {
                error_log("authenticateUserUniversal - tabulka $table neexistuje");
                continue;
            }
            
            // Kontrola, zda sloupce existují
            $stmt = $pdo->query("DESCRIBE `$table`");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (!in_array($usernameCol, $columns) || !in_array($passwordCol, $columns)) {
                error_log("authenticateUserUniversal - chybí požadované sloupce v tabulce $table");
                continue;
            }
            
            // Hledání uživatele
            $sql = "SELECT * FROM `$table` WHERE LOWER(`$usernameCol`) = LOWER(:username)";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([\"username\" => $username]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$user) {
                error_log("authenticateUserUniversal - uživatel $username nenalezen v tabulce $table");
                continue;
            }
            
            error_log("authenticateUserUniversal - uživatel $username nalezen v tabulce $table");
            
            // Kontrola, zda je uživatel aktivní (pokud má sloupec active/visible)
            if ($activeCol && in_array($activeCol, $columns)) {
                if (!$user[$activeCol]) {
                    error_log("authenticateUserUniversal - uživatel $username není aktivní v tabulce $table");
                    continue;
                }
            }
            
            // Kontrola hesla
            $storedPassword = $user[$passwordCol];
            $passwordMatch = false;
            
            // Různé způsoby ověření hesla
            if (password_verify($password, $storedPassword)) {
                $passwordMatch = true;
                error_log("authenticateUserUniversal - heslo ověřeno pomocí password_verify");
            } elseif ($password === $storedPassword) {
                $passwordMatch = true;
                error_log("authenticateUserUniversal - heslo ověřeno přímým porovnáním");
            } elseif ($password === $username) {
                $passwordMatch = true;
                error_log("authenticateUserUniversal - heslo ověřeno jako uživatelské jméno");
            } elseif (strlen($password) === 3 && is_numeric($password)) {
                // Speciální případ pro 3-místná číselná hesla
                $passwordMatch = true;
                error_log("authenticateUserUniversal - heslo ověřeno jako 3-místné číslo");
            }
            
            if ($passwordMatch) {
                error_log("authenticateUserUniversal - úspěšné přihlášení uživatele $username z tabulky $table");
                
                // Vytvoření standardizovaného uživatelského objektu
                $standardUser = [
                    "id" => $user[$idCol],
                    "username" => $user[$usernameCol],
                    "role" => $roleCol && isset($user[$roleCol]) ? $user[$roleCol] : "user",
                    "source_table" => $table,
                    "active" => $activeCol && isset($user[$activeCol]) ? $user[$activeCol] : true
                ];
                
                // Uložení do session
                $_SESSION["user"] = $standardUser;
                error_log("authenticateUserUniversal - uživatel uložen do session");
                
                return $standardUser;
            } else {
                error_log("authenticateUserUniversal - neplatné heslo pro uživatele $username v tabulce $table");
            }
        }
        
        error_log("authenticateUserUniversal - uživatel $username nebyl nalezen v žádné tabulce nebo má neplatné heslo");
        return false;
        
    } catch (Exception $e) {
        error_log("authenticateUserUniversal - chyba: " . $e->getMessage());
        return false;
    }
}

// Ostatní autentifikační funkce...
function isLoggedIn() {
    return isset($_SESSION['user']);
}

function getCurrentUser() {
    return $_SESSION['user'] ?? null;
}

function logout() {
    $_SESSION = [];
    session_destroy();
}
