-- <PERSON><PERSON><PERSON><PERSON> triggerů, k<PERSON><PERSON> p<PERSON>vaj<PERSON> sloupec 'units'
-- Vytvořeno: 2025-05-20 21:30:22

DELIMITER //
CREATE TRIGGER `log_stockcurrent_changes`
AFTER UPDATE ON `stockcurrent`
FOR EACH ROW
BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování z<PERSON>ěny pro zpracování
                INSERT INTO stockcurrent_changes (product_id, old_units, new_units, difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference);
            END//
DELIMITER ;

DELIMITER //
CREATE TRIGGER `update_inventory_stock_changes_after_ticketlines_insert`
AFTER INSERT ON `ticketlines`
FOR EACH ROW
BEGIN
                INSERT INTO inventory_stock_changes (product_id, units, change_type, change_date)
                VALUES (NEW.product, NEW.units, 'sale', NOW());
                
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - NEW.units,
                    last_updated = NOW()
                WHERE product_id = NEW.product
                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
            END//
DELIMITER ;

DELIMITER //
CREATE TRIGGER `update_inventory_totals_after_ticketlines_insert`
AFTER INSERT ON `ticketlines`
FOR EACH ROW
BEGIN
                -- Aktualizace zadaného množství v celkové inventuře
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - NEW.units,
                    last_updated = NOW()
                WHERE product_id = NEW.product
                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                
                -- Logování změny
                INSERT INTO inventory_stock_changes (product_id, units, change_type, change_date)
                VALUES (NEW.product, NEW.units, 'sale', NOW());
            END//
DELIMITER ;

DELIMITER //
CREATE TRIGGER `update_inventory_totals_after_ticketlines_update`
AFTER UPDATE ON `ticketlines`
FOR EACH ROW
BEGIN
                -- Aktualizace zadaného množství v celkové inventuře pouze pokud se změnilo množství
                IF NEW.units != OLD.units THEN
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - (NEW.units - OLD.units),
                        last_updated = NOW()
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                    
                    -- Logování změny
                    INSERT INTO inventory_stock_changes (product_id, units, change_type, change_date)
                    VALUES (NEW.product, NEW.units - OLD.units, 'update', NOW());
                END IF;
            END//
DELIMITER ;

DELIMITER //
CREATE TRIGGER `update_inventory_totals_after_ticketlines_delete`
AFTER DELETE ON `ticketlines`
FOR EACH ROW
BEGIN
                -- Aktualizace zadaného množství v celkové inventuře
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi + OLD.units,
                    last_updated = NOW()
                WHERE product_id = OLD.product
                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                
                -- Logování změny
                INSERT INTO inventory_stock_changes (product_id, units, change_type, change_date)
                VALUES (OLD.product, OLD.units, 'delete', NOW());
            END//
DELIMITER ;

