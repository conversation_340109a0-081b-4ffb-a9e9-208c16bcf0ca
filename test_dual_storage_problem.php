<?php
/**
 * Test duálního ukládání "zadaného množství"
 * Kontroluje, zda se data ukládají na více místech a způsobují konflikty
 */

require_once __DIR__ . '/utils/database.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

try {
    $pdo = getDbConnection();

    echo "<h1>🔍 Test duálního ukládání zadaného množství</h1>";

    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #856404;'>🎯 Cíl testu:</h3>";
    echo "<ul style='color: #856404;'>";
    echo "<li><PERSON><PERSON><PERSON><PERSON> v<PERSON> m<PERSON>, kde se ukládá 'zadan<PERSON> množství'</li>";
    echo "<li>Zkontrolovat konzistenci mezi různými úložišti</li>";
    echo "<li>Identifikovat, které místo se aktualizuje při prodejích</li>";
    echo "<li>Najít příčinu, proč se množství nemění</li>";
    echo "</ul>";
    echo "</div>";

    // KROK 1: Najdeme aktivní inventurní relaci
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active' LIMIT 1");
    $activeSession = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$activeSession) {
        logMessage("❌ Žádná aktivní inventurní relace!", true);
        exit;
    }

    $sessionId = $activeSession['id'];
    logMessage("✓ Aktivní inventurní relace ID: $sessionId");

    // KROK 2: Najdeme produkt s daty v obou tabulkách
    $stmt = $pdo->prepare("
        SELECT DISTINCT ie.product_id, p.name as product_name
        FROM inventory_entries ie
        LEFT JOIN products p ON ie.product_id = p.id
        WHERE ie.session_id = ?
        AND ie.status = 'active'
        AND EXISTS (
            SELECT 1 FROM inventory_totals it
            WHERE it.product_id = ie.product_id
            AND it.session_id = ie.session_id
        )
        LIMIT 1
    ");
    $stmt->execute([$sessionId]);
    $testProduct = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$testProduct) {
        logMessage("❌ Žádný produkt s daty v obou tabulkách!", true);
        exit;
    }

    $productId = $testProduct['product_id'];
    $productName = $testProduct['product_name'];
    logMessage("✓ Testovací produkt: $productName ($productId)");

    // KROK 3: Analýza současného stavu
    echo "<h2>📊 Současný stav zadaného množství</h2>";

    // Data z inventory_entries
    $stmt = $pdo->prepare("
        SELECT
            ie.id,
            ie.user_id,
            ie.zadane_mnozstvi,
            ie.created_at,
            u.username
        FROM inventory_entries ie
        LEFT JOIN inventory_users u ON ie.user_id = u.id
        WHERE ie.product_id = ?
        AND ie.session_id = ?
        AND ie.status = 'active'
        ORDER BY ie.created_at
    ");
    $stmt->execute([$productId, $sessionId]);
    $entriesData = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Data z inventory_totals
    $stmt = $pdo->prepare("
        SELECT
            it.id,
            it.total_zadane_mnozstvi,
            it.last_updated,
            it.created_at
        FROM inventory_totals it
        WHERE it.product_id = ?
        AND it.session_id = ?
    ");
    $stmt->execute([$productId, $sessionId]);
    $totalData = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "<h3>📋 Data v inventory_entries:</h3>";
    if (empty($entriesData)) {
        logMessage("❌ Žádná data v inventory_entries!", true);
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>User</th><th>Zadané množství</th><th>Vytvořeno</th></tr>";

        $entriesSum = 0;
        foreach ($entriesData as $entry) {
            $entriesSum += $entry['zadane_mnozstvi'];
            echo "<tr>";
            echo "<td>" . htmlspecialchars($entry['id']) . "</td>";
            echo "<td>" . htmlspecialchars($entry['username'] ?? 'N/A') . "</td>";
            echo "<td><strong>" . htmlspecialchars($entry['zadane_mnozstvi']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($entry['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "<tr style='background: #e9ecef; font-weight: bold;'>";
        echo "<td colspan='2'>SOUČET</td>";
        echo "<td><strong>$entriesSum</strong></td>";
        echo "<td></td>";
        echo "</tr>";
        echo "</table>";

        logMessage("✓ Součet z inventory_entries: $entriesSum");
    }

    echo "<h3>📊 Data v inventory_totals:</h3>";
    if (!$totalData) {
        logMessage("❌ Žádná data v inventory_totals!", true);
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Total zadané množství</th><th>Vytvořeno</th><th>Aktualizováno</th></tr>";
        echo "<tr>";
        echo "<td>" . htmlspecialchars($totalData['id']) . "</td>";
        echo "<td><strong>" . htmlspecialchars($totalData['total_zadane_mnozstvi']) . "</strong></td>";
        echo "<td>" . htmlspecialchars($totalData['created_at']) . "</td>";
        echo "<td>" . htmlspecialchars($totalData['last_updated']) . "</td>";
        echo "</tr>";
        echo "</table>";

        $totalValue = $totalData['total_zadane_mnozstvi'];
        logMessage("✓ Hodnota z inventory_totals: $totalValue");
    }

    // KROK 4: Porovnání hodnot
    echo "<h2>⚖️ Porovnání hodnot</h2>";

    if (isset($entriesSum) && isset($totalValue)) {
        $difference = abs($entriesSum - $totalValue);

        echo "<div style='background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3>📈 Srovnání:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Zdroj</th><th>Hodnota</th><th>Poznámka</th></tr>";
        echo "<tr><td>inventory_entries (součet)</td><td><strong>$entriesSum</strong></td><td>Součet všech uživatelských záznamů</td></tr>";
        echo "<tr><td>inventory_totals</td><td><strong>$totalValue</strong></td><td>Celková hodnota</td></tr>";
        echo "<tr><td>Rozdíl</td><td><strong>$difference</strong></td><td>" . ($difference > 0.001 ? "❌ NEKONZISTENCE!" : "✅ Konzistentní") . "</td></tr>";
        echo "</table>";
        echo "</div>";

        if ($difference > 0.001) {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3 style='color: #721c24;'>🚨 PROBLÉM IDENTIFIKOVÁN!</h3>";
            echo "<p style='color: #721c24;'>Data v inventory_entries a inventory_totals nejsou konzistentní!</p>";
            echo "<p style='color: #721c24;'>Toto může způsobovat problémy při aktualizaci zadaného množství.</p>";
            echo "</div>";
        }
    }

    // KROK 5: Test aktualizace
    if (isset($_POST['test_update'])) {
        echo "<h2>🧪 Test aktualizace</h2>";

        $testAmount = (float)$_POST['test_amount'];
        logMessage("🔄 Testuji aktualizaci o $testAmount...");

        try {
            $pdo->beginTransaction();

            // Zaznamenání původních hodnot
            $originalTotal = $totalValue;
            $originalEntries = $entriesData;

            // Test 1: Aktualizace inventory_totals
            logMessage("Test 1: Aktualizace inventory_totals...");
            $stmt = $pdo->prepare("
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - ?,
                    last_updated = NOW()
                WHERE product_id = ? AND session_id = ?
            ");
            $stmt->execute([$testAmount, $productId, $sessionId]);

            // Kontrola změny
            $stmt = $pdo->prepare("
                SELECT total_zadane_mnozstvi, last_updated
                FROM inventory_totals
                WHERE product_id = ? AND session_id = ?
            ");
            $stmt->execute([$productId, $sessionId]);
            $newTotal = $stmt->fetch(PDO::FETCH_ASSOC);

            $expectedTotal = $originalTotal - $testAmount;
            $actualTotal = $newTotal['total_zadane_mnozstvi'];

            if (abs($actualTotal - $expectedTotal) < 0.001) {
                logMessage("✅ inventory_totals se aktualizoval správně: $originalTotal → $actualTotal");
            } else {
                logMessage("❌ inventory_totals se neaktualizoval správně: očekáváno $expectedTotal, skutečnost $actualTotal", true);
            }

            // Test 2: Kontrola, zda se změnily inventory_entries
            logMessage("Test 2: Kontrola inventory_entries...");
            $stmt = $pdo->prepare("
                SELECT zadane_mnozstvi
                FROM inventory_entries
                WHERE product_id = ? AND session_id = ? AND status = 'active'
            ");
            $stmt->execute([$productId, $sessionId]);
            $newEntries = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $entriesChanged = false;
            foreach ($newEntries as $i => $newEntry) {
                if (isset($originalEntries[$i])) {
                    if ($newEntry['zadane_mnozstvi'] != $originalEntries[$i]['zadane_mnozstvi']) {
                        $entriesChanged = true;
                        break;
                    }
                }
            }

            if ($entriesChanged) {
                logMessage("⚠️ inventory_entries se také změnily (neočekáváno)");
            } else {
                logMessage("✅ inventory_entries zůstaly nezměněné (očekáváno)");
            }

            // Rollback
            $pdo->rollBack();
            logMessage("🔄 Test dokončen - změny vráceny zpět");

        } catch (Exception $e) {
            $pdo->rollBack();
            logMessage("❌ Chyba při testu: " . $e->getMessage(), true);
        }
    }

    // Formulář pro test
    echo "<h2>🎯 Test aktualizace</h2>";
    echo "<form method='POST'>";
    echo "<p>";
    echo "<label for='test_amount'>Množství k odečtení:</label> ";
    echo "<input type='number' name='test_amount' id='test_amount' value='1' min='0.1' step='0.1' style='width: 100px;'>";
    echo "</p>";
    echo "<p>";
    echo "<button type='submit' name='test_update' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🧪 Otestovat aktualizaci</button>";
    echo "</p>";
    echo "</form>";

    echo "<h2>🔗 Navigace</h2>";
    echo "<p>";
    echo "<a href='comprehensive_zadane_mnozstvi_test.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Komplexní test</a>";
    echo "<a href='test_trigger_with_existing_ticket.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test triggerů</a>";
    echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
    echo "</p>";

} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
