<?php
/**
 * Test přihlašování
 */

// Spuštění session pouze pokud ještě není spuštěna
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Test přihlašování</h1>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    echo "<h2>1. Kontrola databáze a tabulek</h2>";
    
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
    
    ensureTablesExist();
    echo "<p style='color: green;'>✓ Tabulky existují</p>";
    
    echo "<h2>2. Kontrola admin uživatele</h2>";
    
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin uživatel existuje</p>";
        echo "<p>ID: " . $admin['id'] . "</p>";
        echo "<p>Username: " . $admin['username'] . "</p>";
        echo "<p>Role: " . $admin['role'] . "</p>";
        echo "<p>Active: " . ($admin['active'] ? 'Ano' : 'Ne') . "</p>";
        echo "<p>Password: " . $admin['password'] . "</p>";
    } else {
        echo "<p style='color: red;'>✗ Admin uživatel neexistuje</p>";
        
        // Vytvoříme admin uživatele
        echo "<p>Vytvářím admin uživatele...</p>";
        $stmt = $pdo->prepare("
            INSERT INTO inventory_users (username, password, role, full_name, email, active) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $result = $stmt->execute(['admin', 'admin123', 'admin', 'Administrátor', '<EMAIL>', 1]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin uživatel vytvořen</p>";
        } else {
            echo "<p style='color: red;'>✗ Nepodařilo se vytvořit admin uživatele</p>";
        }
    }
    
    echo "<h2>3. Test přihlášení</h2>";
    
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné</p>";
        echo "<p>Uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
        echo "<p>Role: " . $user['role'] . "</p>";
        echo "<p>Session ID: " . session_id() . "</p>";
        echo "<p>Session data: <pre>" . print_r($_SESSION, true) . "</pre></p>";
    } else {
        echo "<p style='color: red;'>✗ Přihlášení selhalo</p>";
        
        // Zkusíme různé kombinace hesel
        echo "<h3>Zkouším různé kombinace hesel:</h3>";
        
        $passwords = ['admin123', 'admin', '123'];
        foreach ($passwords as $password) {
            echo "<p>Zkouším heslo: '$password'</p>";
            $testUser = authenticateUser('admin', $password);
            if ($testUser) {
                echo "<p style='color: green;'>✓ Úspěch s heslem: '$password'</p>";
                break;
            } else {
                echo "<p style='color: red;'>✗ Neúspěch s heslem: '$password'</p>";
            }
        }
    }
    
    echo "<h2>4. Test API přihlášení</h2>";
    
    // Simulace POST požadavku na simple_auth.php
    $loginData = json_encode(['username' => 'admin', 'password' => 'admin123']);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($loginData)
            ],
            'content' => $loginData
        ]
    ]);
    
    $url = 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php?action=login';
    $response = file_get_contents($url, false, $context);
    
    echo "<h3>Odpověď z API:</h3>";
    echo "<pre>" . htmlspecialchars($response) . "</pre>";
    
    echo "<h2>5. Všichni uživatelé v databázi</h2>";
    
    $stmt = $pdo->query("SELECT * FROM inventory_users");
    $users = $stmt->fetchAll();
    
    if ($users) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Password</th><th>Role</th><th>Active</th><th>Full Name</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>" . $user['id'] . "</td>";
            echo "<td>" . $user['username'] . "</td>";
            echo "<td>" . $user['password'] . "</td>";
            echo "<td>" . $user['role'] . "</td>";
            echo "<td>" . ($user['active'] ? 'Ano' : 'Ne') . "</td>";
            echo "<td>" . $user['full_name'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Žádní uživatelé v databázi</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
echo "<p><a href='test_create_inventory.html'>Test vytváření inventury</a></p>";
?>
