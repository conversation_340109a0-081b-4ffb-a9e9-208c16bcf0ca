<?php
/**
 * Test přihlašování
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Test přihlašování</h1>";

// Načtení potřebných souborů
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

echo "<h2>1. Test databázového připojení</h2>";
try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba připojení k databázi: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>2. Test existence tabulek</h2>";
try {
    ensureTablesExist();
    echo "<p style='color: green;'>✓ Tabulky existují nebo byly vytvořeny</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba při kontrole/vytváření tabulek: " . $e->getMessage() . "</p>";
}

echo "<h2>3. Test uživatelů v databázi</h2>";
try {
    $stmt = $pdo->query("SELECT id, username, role, active FROM inventory_users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: orange;'>⚠ Žádní uživatelé v tabulce inventory_users</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Active</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['role']}</td>";
            echo "<td>" . ($user['active'] ? 'Ano' : 'Ne') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba při načítání uživatelů: " . $e->getMessage() . "</p>";
}

echo "<h2>4. Test přihlášení admin/admin123</h2>";
try {
    $user = authenticateUser('admin', 'admin123');
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné</p>";
        echo "<pre>" . print_r($user, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Přihlášení neúspěšné</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba při přihlašování: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Test API volání</h2>";
echo "<form method='post' action='api/simple_auth.php?action=login'>";
echo "<input type='hidden' name='username' value='admin'>";
echo "<input type='hidden' name='password' value='admin123'>";
echo "<button type='submit'>Test API přihlášení</button>";
echo "</form>";

echo "<h2>6. JavaScript test</h2>";
?>
<script>
async function testLogin() {
    try {
        const response = await fetch('api/simple_auth.php?action=login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        const data = await response.text();
        console.log('Response data:', data);
        
        document.getElementById('js-result').innerHTML = 
            '<h3>Výsledek JavaScript testu:</h3>' +
            '<p>Status: ' + response.status + '</p>' +
            '<p>Data: <pre>' + data + '</pre></p>';
            
    } catch (error) {
        console.error('Error:', error);
        document.getElementById('js-result').innerHTML = 
            '<h3>Chyba JavaScript testu:</h3>' +
            '<p style="color: red;">' + error.message + '</p>';
    }
}
</script>

<button onclick="testLogin()">Test JavaScript přihlášení</button>
<div id="js-result"></div>
