<?php
/**
 * Test aktualizace zadaného množství v celkové inventuře po prodeji
 * 
 * Tento skript testuje, zda se zadané množství v celkové inventuře správně aktualizuje
 * po prodeji produktů. Simuluje prodej produktu a kontroluje, zda se zadané množství
 * v celkové inventuře správně aktualizovalo.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test aktualizace zadaného množství v celkové inventuře po prodeji</h1>";
    
    // Kontrola, zda existuje aktivní inventurní relace
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active'");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sessions)) {
        logMessage("Neexistuje žádná aktivní inventurní relace. Vytvořte nejprve aktivní inventurní relaci.", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    $sessionId = $sessions[0]['id'];
    logMessage("Nalezena aktivní inventurní relace s ID: " . $sessionId);
    
    // Získání produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logMessage("Nebyl nalezen žádný produkt s kladným množstvím na skladě. Nelze provést test.", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    $productId = $product['id'];
    $productName = $product['name'];
    $currentUnits = $product['units'];
    
    logMessage("Vybrán produkt pro test: " . $productName . " (ID: " . $productId . ", Množství na skladě: " . $currentUnits . ")");
    
    // Kontrola, zda existuje záznam v inventory_totals pro tento produkt
    $stmt = $pdo->prepare("
        SELECT * FROM inventory_totals
        WHERE product_id = ?
        AND session_id = ?
    ");
    $stmt->execute([$productId, $sessionId]);
    $inventoryTotal = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$inventoryTotal) {
        logMessage("Pro vybraný produkt neexistuje záznam v inventory_totals. Vytvářím nový záznam...");
        
        // Vytvoření záznamu v inventory_totals
        $stmt = $pdo->prepare("
            INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$sessionId, $productId, $currentUnits]);
        
        // Kontrola, zda byl záznam vytvořen
        $stmt = $pdo->prepare("
            SELECT * FROM inventory_totals
            WHERE product_id = ?
            AND session_id = ?
        ");
        $stmt->execute([$productId, $sessionId]);
        $inventoryTotal = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$inventoryTotal) {
            logMessage("Nepodařilo se vytvořit záznam v inventory_totals. Nelze provést test.", true);
            echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
            exit;
        }
        
        logMessage("Záznam v inventory_totals byl úspěšně vytvořen.");
    }
    
    $totalZadaneMnozstvi = $inventoryTotal['total_zadane_mnozstvi'];
    logMessage("Aktuální zadané množství v celkové inventuře: " . $totalZadaneMnozstvi);
    
    // Simulace prodeje produktu
    $unitsToSell = 1;
    if ($currentUnits < $unitsToSell) {
        $unitsToSell = $currentUnits;
    }
    
    logMessage("Simuluji prodej " . $unitsToSell . " kusů produktu " . $productName);
    
    // Aktualizace stockcurrent - snížení stavu zásob
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = units - ?
        WHERE product = ?
    ");
    $stmt->execute([$unitsToSell, $productId]);
    
    // Kontrola, zda byla aktualizace úspěšná
    $stmt = $pdo->prepare("
        SELECT units FROM stockcurrent
        WHERE product = ?
    ");
    $stmt->execute([$productId]);
    $newUnits = $stmt->fetchColumn();
    
    logMessage("Nové množství na skladě: " . $newUnits . " (bylo: " . $currentUnits . ")");
    
    // Kontrola, zda se aktualizovalo zadané množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi FROM inventory_totals
        WHERE product_id = ?
        AND session_id = ?
    ");
    $stmt->execute([$productId, $sessionId]);
    $newTotalZadaneMnozstvi = $stmt->fetchColumn();
    
    logMessage("Nové zadané množství v celkové inventuře: " . $newTotalZadaneMnozstvi . " (bylo: " . $totalZadaneMnozstvi . ")");
    
    // Kontrola, zda se zadané množství správně aktualizovalo
    $expectedTotalZadaneMnozstvi = $totalZadaneMnozstvi - $unitsToSell;
    
    if (abs($newTotalZadaneMnozstvi - $expectedTotalZadaneMnozstvi) < 0.001) {
        logMessage("TEST ÚSPĚŠNÝ: Zadané množství v celkové inventuře se správně aktualizovalo po prodeji produktu.");
    } else {
        logMessage("TEST SELHAL: Zadané množství v celkové inventuře se neaktualizovalo správně po prodeji produktu.", true);
        logMessage("Očekávaná hodnota: " . $expectedTotalZadaneMnozstvi . ", Skutečná hodnota: " . $newTotalZadaneMnozstvi, true);
    }
    
    // Vrácení stavu zásob na původní hodnotu
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = ?
        WHERE product = ?
    ");
    $stmt->execute([$currentUnits, $productId]);
    
    logMessage("Stav zásob byl vrácen na původní hodnotu: " . $currentUnits);
    
    // Vrácení zadaného množství v celkové inventuře na původní hodnotu
    $stmt = $pdo->prepare("
        UPDATE inventory_totals
        SET total_zadane_mnozstvi = ?
        WHERE product_id = ?
        AND session_id = ?
    ");
    $stmt->execute([$totalZadaneMnozstvi, $productId, $sessionId]);
    
    logMessage("Zadané množství v celkové inventuře bylo vráceno na původní hodnotu: " . $totalZadaneMnozstvi);
    
    echo "<p><a href='check_all_triggers.php'>Zkontrolovat všechny triggery</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
