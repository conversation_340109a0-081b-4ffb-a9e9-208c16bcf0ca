-- Custom tables for inventory management

-- Table for inventory sessions
CREATE TABLE IF NOT EXISTS `inventory_sessions` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `start_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_time` D<PERSON><PERSON><PERSON>E NULL,
  `status` ENUM('active', 'completed', 'cancelled') NOT NULL DEFAULT 'active',
  `user_id` VARCHAR(255) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_inventory_sessions_status` (`status`),
  INDEX `idx_inventory_sessions_user` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for inventory entries
CREATE TABLE IF NOT EXISTS `inventory_entries` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `session_id` INT NOT NULL,
  `product_id` VARCHAR(255) NOT NULL,
  `ean_code` VARCHAR(255) NOT NULL,
  `user_id` VARCHAR(255) NOT NULL,
  `zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
  `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` ENUM('active', 'deleted') NOT NULL DEFAULT 'active',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`session_id`) REFERENCES `inventory_sessions` (`id`) ON DELETE CASCADE,
  INDEX `idx_inventory_entries_product` (`product_id`),
  INDEX `idx_inventory_entries_session` (`session_id`),
  INDEX `idx_inventory_entries_user` (`user_id`),
  INDEX `idx_inventory_entries_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for tracking stock changes during inventory
CREATE TABLE IF NOT EXISTS `inventory_stock_changes` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `session_id` INT NOT NULL,
  `product_id` VARCHAR(255) NOT NULL,
  `initial_stock` DECIMAL(10,3) NOT NULL DEFAULT 0,
  `stock_changes_during_inventory` DECIMAL(10,3) NOT NULL DEFAULT 0,
  `last_update` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (`session_id`) REFERENCES `inventory_sessions` (`id`) ON DELETE CASCADE,
  UNIQUE KEY `uk_inventory_stock_changes_session_product` (`session_id`, `product_id`),
  INDEX `idx_inventory_stock_changes_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for previous stock for detecting changes
CREATE TABLE IF NOT EXISTS `previous_stock` (
  `product_id` VARCHAR(255) NOT NULL PRIMARY KEY,
  `units` DECIMAL(10,3) NOT NULL DEFAULT 0,
  `last_update` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  INDEX `idx_previous_stock_product` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Table for user management
CREATE TABLE IF NOT EXISTS `inventory_users` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `username` VARCHAR(255) NOT NULL UNIQUE,
  `password` VARCHAR(255) NOT NULL,
  `role` ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
  `full_name` VARCHAR(255) NOT NULL,
  `email` VARCHAR(255) NULL,
  `active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX `idx_inventory_users_username` (`username`),
  INDEX `idx_inventory_users_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Insert default admin user (password: admin123)
INSERT INTO `inventory_users` (`username`, `password`, `role`, `full_name`)
VALUES ('admin', '$2y$10$8zTlsRlxAV5JVtZyaOyVVeVbIl.JyW8vRGzGvB0O/vQrZ5ZB7Tsqm', 'admin', 'Administrator')
ON DUPLICATE KEY UPDATE `id` = `id`;

-- Create trigger to update previous_stock when stockcurrent changes
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `update_previous_stock_after_stockcurrent_update`
AFTER UPDATE ON `stockcurrent`
FOR EACH ROW
BEGIN
    INSERT INTO `previous_stock` (`product_id`, `units`)
    VALUES (NEW.product, NEW.units)
    ON DUPLICATE KEY UPDATE `units` = NEW.units;
END //
DELIMITER ;

-- Create trigger to update previous_stock when stockcurrent inserts
DELIMITER //
CREATE TRIGGER IF NOT EXISTS `update_previous_stock_after_stockcurrent_insert`
AFTER INSERT ON `stockcurrent`
FOR EACH ROW
BEGIN
    INSERT INTO `previous_stock` (`product_id`, `units`)
    VALUES (NEW.product, NEW.units)
    ON DUPLICATE KEY UPDATE `units` = NEW.units;
END //
DELIMITER ;
