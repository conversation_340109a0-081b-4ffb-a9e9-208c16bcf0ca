<?php
/**
 * Náhradní API endpoint pro vytvoření inventurního záznamu
 */

// Nastavení h<PERSON> pro CORS a JSON
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';

// Funkce pro odpověď
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

// Kontrola metody
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(['error' => 'Neplatná metoda. Použijte POST.'], 405);
}

// Kontrola, zda je uživatel přihlášen
if (!isLoggedIn()) {
    sendResponse(['error' => 'Neautorizovaný přístup'], 401);
}

// Získání aktuálního uživatele
$user = getCurrentUser();
if (!$user) {
    sendResponse(['error' => 'Nepodařilo se získat informace o uživateli'], 401);
}

// Získání JSON vstupu
$jsonInput = file_get_contents('php://input');
error_log("inventory_entry_create - JSON vstup: " . $jsonInput);

$input = json_decode($jsonInput, true);

if (!$input) {
    error_log("inventory_entry_create - Neplatný JSON: " . json_last_error_msg());
    sendResponse(['error' => 'Neplatný JSON: ' . json_last_error_msg()], 400);
}

error_log("inventory_entry_create - Dekódovaný vstup: " . print_r($input, true));

// Ruční validace vstupu
$errors = [];
$validData = [];

// Validace session_id
if (!isset($input['session_id'])) {
    $errors['session_id'] = 'This field is required';
} else {
    $sessionId = filter_var($input['session_id'], FILTER_VALIDATE_INT);
    if ($sessionId === false) {
        $errors['session_id'] = 'Invalid integer value';
    } else {
        $validData['session_id'] = $sessionId;
    }
}

// Validace ean_code
if (!isset($input['ean_code']) || $input['ean_code'] === '') {
    $errors['ean_code'] = 'This field is required';
} else {
    $eanCode = trim($input['ean_code']);
    if (!preg_match('/^[0-9]+$/', $eanCode)) {
        $errors['ean_code'] = 'Invalid EAN code';
    } else {
        $validData['ean_code'] = $eanCode;
    }
}

// Validace zadane_mnozstvi
if (!isset($input['zadane_mnozstvi'])) {
    $errors['zadane_mnozstvi'] = 'This field is required';
} else {
    $zadaneMnozstvi = $input['zadane_mnozstvi'];
    if (is_string($zadaneMnozstvi)) {
        $zadaneMnozstvi = str_replace(',', '.', $zadaneMnozstvi);
    }
    $zadaneMnozstvi = filter_var($zadaneMnozstvi, FILTER_VALIDATE_FLOAT);
    if ($zadaneMnozstvi === false || $zadaneMnozstvi < 0) {
        $errors['zadane_mnozstvi'] = 'Invalid float value';
    } else {
        $validData['zadane_mnozstvi'] = $zadaneMnozstvi;
    }
}

error_log("inventory_entry_create - Výsledek ruční validace: " . (empty($errors) ? "OK" : "Chyby: " . print_r($errors, true)));

if (!empty($errors)) {
    error_log("inventory_entry_create - Validace selhala: " . print_r($errors, true));
    sendResponse(['error' => 'Validace selhala', 'details' => $errors], 400);
}

// Získání hodnot
$sessionId = $validData['session_id'];
$eanCode = $validData['ean_code'];
$zadaneMnozstvi = $validData['zadane_mnozstvi'];

error_log("inventory_entry_create - Získané hodnoty: sessionId=" . $sessionId . ", eanCode=" . $eanCode . ", zadaneMnozstvi=" . $zadaneMnozstvi);

try {
    // Připojení k databázi
    $pdo = getDbConnection();
    error_log("inventory_entry_create - Připojení k databázi vytvořeno");

    // Kontrola, zda relace existuje
    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    error_log("inventory_entry_create - SQL dotaz pro kontrolu relace: SELECT * FROM inventory_sessions WHERE id = " . $sessionId);
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();
    error_log("inventory_entry_create - Výsledek kontroly relace: " . ($session ? "nalezena" : "nenalezena"));

    if (!$session) {
        error_log("inventory_entry_create - Inventurní relace nenalezena: " . $sessionId);
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
    }

    // Kontrola, zda je relace aktivní
    error_log("inventory_entry_create - Status relace: " . $session['status']);
    if ($session['status'] !== 'active') {
        error_log("inventory_entry_create - Inventurní relace není aktivní: " . $session['status']);
        sendResponse(['error' => 'Inventurní relace není aktivní'], 400);
    }

    // Kontrola, zda produkt existuje
    $sql = "
        SELECT p.*, COALESCE(s.units, 0) AS current_stock
        FROM products p
        LEFT JOIN stockcurrent s ON p.id = s.product
        WHERE p.code = :ean_code
    ";
    error_log("inventory_entry_create - SQL dotaz pro kontrolu produktu: " . $sql . " (ean_code=" . $eanCode . ")");

    $stmt = $pdo->prepare($sql);
    $stmt->execute(['ean_code' => $eanCode]);

    $product = $stmt->fetch();
    error_log("inventory_entry_create - Výsledek kontroly produktu: " . ($product ? "nalezen" : "nenalezen"));

    if (!$product) {
        error_log("inventory_entry_create - Produkt nebyl nalezen: " . $eanCode);
        sendResponse(['error' => 'Produkt nebyl nalezen'], 404);
    }

    error_log("inventory_entry_create - Nalezený produkt: " . print_r($product, true));

    // Získání aktuálního stavu skladu
    $currentStock = $product['current_stock'] ?? 0;
    error_log("inventory_entry_create - Aktuální stav skladu: " . $currentStock);

    // Začátek transakce
    $pdo->beginTransaction();
    error_log("inventory_entry_create - Transakce zahájena");

    // Kontrola, zda již existuje záznam pro tento produkt a relaci
    $stmt = $pdo->prepare("
        SELECT id
        FROM inventory_entries
        WHERE session_id = :session_id
        AND product_id = :product_id
        AND user_id = :user_id
        AND status = 'active'
    ");

    $stmt->execute([
        'session_id' => $sessionId,
        'product_id' => $product['id'],
        'user_id' => $user['id']
    ]);

    $existingEntry = $stmt->fetch();

    if ($existingEntry) {
        // Aktualizace existujícího záznamu
        $stmt = $pdo->prepare("
            UPDATE inventory_entries
            SET zadane_mnozstvi = :zadane_mnozstvi,
                last_updated = NOW()
            WHERE id = :id
        ");

        $stmt->execute([
            'id' => $existingEntry['id'],
            'zadane_mnozstvi' => $zadaneMnozstvi
        ]);

        $entryId = $existingEntry['id'];
        error_log("inventory_entry_create - Existující záznam byl aktualizován (ID: " . $entryId . ")");
    } else {
        // Vytvoření nového inventurního záznamu
        $stmt = $pdo->prepare("
            INSERT INTO inventory_entries (
                session_id,
                product_id,
                ean_code,
                user_id,
                zadane_mnozstvi
            )
            VALUES (
                :session_id,
                :product_id,
                :ean_code,
                :user_id,
                :zadane_mnozstvi
            )
        ");

        $stmt->execute([
            'session_id' => $sessionId,
            'product_id' => $product['id'],
            'ean_code' => $eanCode,
            'user_id' => $user['id'],
            'zadane_mnozstvi' => $zadaneMnozstvi
        ]);

        $entryId = $pdo->lastInsertId();
        error_log("inventory_entry_create - Nový záznam byl vytvořen (ID: " . $entryId . ")");
    }

    // Aktualizace celkového zadaného množství v inventory_totals
    $stmt = $pdo->prepare("
        SELECT id, total_zadane_mnozstvi
        FROM inventory_totals
        WHERE session_id = :session_id
        AND product_id = :product_id
    ");

    $stmt->execute([
        'session_id' => $sessionId,
        'product_id' => $product['id']
    ]);

    $existingTotal = $stmt->fetch();

    if ($existingTotal) {
        error_log("inventory_entry_create - Existující záznam v inventory_totals nalezen (ID: " . $existingTotal['id'] . ")");

        // Získání všech záznamů pro tento produkt a relaci
        $stmt = $pdo->prepare("
            SELECT SUM(zadane_mnozstvi) AS total_zadane_mnozstvi
            FROM inventory_entries
            WHERE session_id = :session_id
            AND product_id = :product_id
            AND status = 'active'
        ");

        $stmt->execute([
            'session_id' => $sessionId,
            'product_id' => $product['id']
        ]);

        $totalResult = $stmt->fetch();
        $totalZadaneMnozstvi = $totalResult['total_zadane_mnozstvi'] ?? 0;

        error_log("inventory_entry_create - Celkové zadané množství: " . $totalZadaneMnozstvi);

        // Aktualizace existujícího záznamu
        $stmt = $pdo->prepare("
            UPDATE inventory_totals
            SET total_zadane_mnozstvi = :total_zadane_mnozstvi,
                last_updated = NOW()
            WHERE id = :id
        ");

        $stmt->execute([
            'id' => $existingTotal['id'],
            'total_zadane_mnozstvi' => $totalZadaneMnozstvi
        ]);

        error_log("inventory_entry_create - Záznam v inventory_totals byl aktualizován");
    } else {
        error_log("inventory_entry_create - Vytváření nového záznamu v inventory_totals");

        // Vytvoření nového záznamu
        $stmt = $pdo->prepare("
            INSERT INTO inventory_totals (
                session_id,
                product_id,
                total_zadane_mnozstvi
            )
            VALUES (
                :session_id,
                :product_id,
                :total_zadane_mnozstvi
            )
        ");

        $stmt->execute([
            'session_id' => $sessionId,
            'product_id' => $product['id'],
            'total_zadane_mnozstvi' => $zadaneMnozstvi
        ]);

        error_log("inventory_entry_create - Nový záznam v inventory_totals byl vytvořen");
    }

    // Sledování změn stavu zásob
    try {
        // Nejprve zkontrolujeme, zda tabulka inventory_stock_changes existuje
        $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_stock_changes'");
        $tableExists = $stmt->rowCount() > 0;

        if (!$tableExists) {
            error_log("inventory_entry_create - Tabulka inventory_stock_changes neexistuje, vytvářím ji");

            // Vytvoření tabulky inventory_stock_changes
            $pdo->exec("
                CREATE TABLE IF NOT EXISTS `inventory_stock_changes` (
                  `id` INT AUTO_INCREMENT PRIMARY KEY,
                  `session_id` INT NOT NULL,
                  `product_id` VARCHAR(255) NOT NULL,
                  `initial_stock` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `stock_changes_during_inventory` DECIMAL(10,3) NOT NULL DEFAULT 0,
                  `last_update` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                  UNIQUE KEY `uk_inventory_stock_changes_session_product` (`session_id`, `product_id`),
                  INDEX `idx_inventory_stock_changes_product` (`product_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");

            error_log("inventory_entry_create - Tabulka inventory_stock_changes byla vytvořena");
        }

        // Kontrola, zda existuje záznam pro tento produkt a relaci
        $stmt = $pdo->prepare("
            SELECT id
            FROM inventory_stock_changes
            WHERE session_id = :session_id
            AND product_id = :product_id
        ");

        $stmt->execute([
            'session_id' => $sessionId,
            'product_id' => $product['id']
        ]);

        $existingStockChange = $stmt->fetch();

        if ($existingStockChange) {
            // Aktualizace existujícího záznamu
            $stmt = $pdo->prepare("
                UPDATE inventory_stock_changes
                SET initial_stock = :initial_stock,
                    last_update = NOW()
                WHERE id = :id
            ");

            $stmt->execute([
                'id' => $existingStockChange['id'],
                'initial_stock' => $currentStock
            ]);

            error_log("inventory_entry_create - Existující záznam v inventory_stock_changes byl aktualizován (ID: " . $existingStockChange['id'] . ")");
        } else {
            // Vytvoření nového záznamu
            $stmt = $pdo->prepare("
                INSERT INTO inventory_stock_changes (
                    session_id,
                    product_id,
                    initial_stock,
                    stock_changes_during_inventory
                )
                VALUES (
                    :session_id,
                    :product_id,
                    :initial_stock,
                    0
                )
            ");

            $stmt->execute([
                'session_id' => $sessionId,
                'product_id' => $product['id'],
                'initial_stock' => $currentStock
            ]);

            error_log("inventory_entry_create - Nový záznam v inventory_stock_changes byl vytvořen");
        }
    } catch (PDOException $e) {
        error_log("inventory_entry_create - Chyba při práci s inventory_stock_changes: " . $e->getMessage());
        // Pokračujeme dál i v případě chyby, aby se dokončila transakce
    }

    error_log("inventory_entry_create - Záznam v inventory_stock_changes byl vytvořen/aktualizován");

    // Commit transakce
    $pdo->commit();
    error_log("inventory_entry_create - Transakce úspěšně dokončena");

    // Příprava odpovědi
    $response = [
        'success' => true,
        'entry_id' => $entryId,
        'message' => 'Inventurní záznam byl úspěšně ' . ($existingEntry ? 'aktualizován' : 'vytvořen'),
        'product' => [
            'id' => $product['id'],
            'ean_code' => $product['code'],
            'name' => $product['name'],
            'current_stock' => $currentStock,
            'zadane_mnozstvi' => $zadaneMnozstvi,
            'difference' => $zadaneMnozstvi - $currentStock
        ]
    ];

    error_log("inventory_entry_create - Odpověď: " . print_r($response, true));

    sendResponse($response);
} catch (Exception $e) {
    // Rollback transakce v případě chyby
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
        error_log("inventory_entry_create - Transakce vrácena zpět");
    }

    error_log("inventory_entry_create - Chyba: " . $e->getMessage());
    error_log("inventory_entry_create - Stack trace: " . $e->getTraceAsString());
    sendResponse(['error' => 'Došlo k chybě: ' . $e->getMessage()], 500);
}
?>
