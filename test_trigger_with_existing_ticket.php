<?php
/**
 * Test triggerů pomocí existujícího ticketu
 */

require_once __DIR__ . '/utils/database.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

try {
    $pdo = getDbConnection();
    
    echo "<h1>🧪 Test triggerů s existujícím ticketem</h1>";
    
    // Najdeme existující ticket
    $stmt = $pdo->query("
        SELECT id, tickettype, ticketid, person, customer, status
        FROM tickets 
        ORDER BY id DESC 
        LIMIT 5
    ");
    $existingTickets = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($existingTickets)) {
        logMessage("❌ Žádné existující tickety nenalezeny!", true);
        echo "<p>Nelze otestovat triggery bez existujíc<PERSON>ch ticketů.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    echo "<h2>📋 Existující tickety:</h2>";
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
    echo "<tr><th>ID</th><th>Type</th><th>TicketID</th><th>Person</th><th>Customer</th><th>Status</th></tr>";
    
    foreach ($existingTickets as $ticket) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($ticket['id']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['tickettype']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['ticketid']) . "</td>";
        echo "<td>" . htmlspecialchars($ticket['person'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($ticket['customer'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($ticket['status']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test triggeru
    if (isset($_POST['test_trigger_existing'])) {
        $productId = $_POST['product_id'];
        $testQuantity = (float)$_POST['test_quantity'];
        $ticketId = $_POST['ticket_id'];
        
        logMessage("🔄 Testuji trigger s existujícím ticketem...");
        logMessage("Ticket ID: $ticketId");
        logMessage("Produkt ID: $productId");
        logMessage("Testovací množství: $testQuantity");
        
        try {
            $pdo->beginTransaction();
            
            // Získání původního stavu
            $stmt = $pdo->prepare("
                SELECT total_zadane_mnozstvi, last_updated
                FROM inventory_totals
                WHERE product_id = ? AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
            ");
            $stmt->execute([$productId]);
            $originalState = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$originalState) {
                logMessage("❌ Produkt nenalezen v inventory_totals!", true);
            } else {
                $originalQuantity = $originalState['total_zadane_mnozstvi'];
                $originalUpdated = $originalState['last_updated'];
                
                logMessage("✓ Původní zadané množství: $originalQuantity");
                logMessage("✓ Původní čas aktualizace: $originalUpdated");
                
                // Najdeme nejvyšší line number pro tento ticket
                $stmt = $pdo->prepare("
                    SELECT COALESCE(MAX(line), -1) + 1 as next_line
                    FROM ticketlines
                    WHERE ticket = ?
                ");
                $stmt->execute([$ticketId]);
                $nextLine = $stmt->fetchColumn();
                
                logMessage("✓ Použiji line number: $nextLine");
                
                // Vložení do ticketlines (mělo by spustit trigger)
                $stmt = $pdo->prepare("
                    INSERT INTO ticketlines (ticket, line, product, attributesetinstance_id, units, price, taxid, attributes)
                    VALUES (?, ?, ?, NULL, ?, 33.25, '001', NULL)
                ");
                $stmt->execute([$ticketId, $nextLine, $productId, $testQuantity]);
                
                logMessage("✓ Vložen záznam do ticketlines");
                
                // Krátká pauza pro zpracování triggeru
                usleep(100000); // 0.1 sekundy
                
                // Kontrola nového stavu
                $stmt = $pdo->prepare("
                    SELECT total_zadane_mnozstvi, last_updated
                    FROM inventory_totals
                    WHERE product_id = ? AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
                ");
                $stmt->execute([$productId]);
                $newState = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$newState) {
                    logMessage("❌ Produkt nenalezen po testu!", true);
                } else {
                    $newQuantity = $newState['total_zadane_mnozstvi'];
                    $newUpdated = $newState['last_updated'];
                    $actualDifference = $originalQuantity - $newQuantity;
                    
                    echo "<div style='background: #e9ecef; border: 1px solid #ced4da; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3>📊 Výsledky testu triggeru:</h3>";
                    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                    echo "<tr><th>Parametr</th><th>Hodnota</th></tr>";
                    echo "<tr><td>Původní množství</td><td><strong>$originalQuantity</strong></td></tr>";
                    echo "<tr><td>Testované množství</td><td><strong>$testQuantity</strong></td></tr>";
                    echo "<tr><td>Nové množství</td><td><strong>$newQuantity</strong></td></tr>";
                    echo "<tr><td>Skutečný rozdíl</td><td><strong>$actualDifference</strong></td></tr>";
                    echo "<tr><td>Původní čas</td><td>$originalUpdated</td></tr>";
                    echo "<tr><td>Nový čas</td><td>$newUpdated</td></tr>";
                    echo "<tr><td>Čas se změnil?</td><td>" . ($originalUpdated != $newUpdated ? "✅ ANO" : "❌ NE") . "</td></tr>";
                    echo "</table>";
                    echo "</div>";
                    
                    if ($actualDifference == $testQuantity && $originalUpdated != $newUpdated) {
                        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                        echo "<h3 style='color: #155724;'>🎉 TRIGGER FUNGUJE PERFEKTNĚ!</h3>";
                        echo "<p style='color: #155724;'>Zadané množství bylo správně sníženo o $testQuantity a čas byl aktualizován.</p>";
                        echo "<p style='color: #155724;'><strong>Závěr:</strong> Triggery fungují! Problém může být v tom, že se nespouštějí při reálných prodejích z UniCenta.</p>";
                        echo "</div>";
                    } elseif ($actualDifference == $testQuantity) {
                        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                        echo "<h3 style='color: #856404;'>⚠️ TRIGGER ČÁSTEČNĚ FUNGUJE</h3>";
                        echo "<p style='color: #856404;'>Množství bylo sníženo, ale čas nebyl aktualizován. Možná problém v triggeru.</p>";
                        echo "</div>";
                    } else {
                        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                        echo "<h3 style='color: #721c24;'>❌ TRIGGER NEFUNGUJE!</h3>";
                        echo "<p style='color: #721c24;'>Očekávaná změna: -$testQuantity, skutečná změna: -$actualDifference</p>";
                        if ($actualDifference == 0) {
                            echo "<p style='color: #721c24;'><strong>Problém:</strong> Trigger se vůbec nespustil nebo neaktualizoval data.</p>";
                        }
                        echo "</div>";
                    }
                }
            }
            
            // Rollback transakce
            $pdo->rollBack();
            logMessage("🔄 Test dokončen - data byla vrácena zpět");
            
        } catch (Exception $e) {
            $pdo->rollBack();
            logMessage("❌ Chyba při testu: " . $e->getMessage(), true);
        }
    }
    
    // Formulář pro test
    echo "<h2>🎯 Test triggeru s existujícím ticketem:</h2>";
    
    // Získání produktů pro test
    $stmt = $pdo->query("
        SELECT 
            it.product_id,
            p.name as product_name,
            it.total_zadane_mnozstvi
        FROM inventory_totals it
        LEFT JOIN products p ON it.product_id = p.id
        WHERE it.session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
        AND it.total_zadane_mnozstvi > 0
        ORDER BY it.total_zadane_mnozstvi DESC
        LIMIT 5
    ");
    $testProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($testProducts) && !empty($existingTickets)) {
        echo "<form method='POST'>";
        
        echo "<h3>Vyberte ticket:</h3>";
        echo "<select name='ticket_id' required>";
        foreach ($existingTickets as $ticket) {
            echo "<option value='" . htmlspecialchars($ticket['id']) . "'>";
            echo "ID: " . htmlspecialchars($ticket['id']) . " (Type: " . htmlspecialchars($ticket['tickettype']) . ")";
            echo "</option>";
        }
        echo "</select>";
        
        echo "<h3>Vyberte produkt:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
        echo "<tr><th>Vybrat</th><th>Produkt</th><th>Název</th><th>Aktuální množství</th></tr>";
        
        foreach ($testProducts as $i => $product) {
            $checked = $i === 0 ? 'checked' : '';
            echo "<tr>";
            echo "<td><input type='radio' name='product_id' value='" . htmlspecialchars($product['product_id']) . "' $checked></td>";
            echo "<td>" . htmlspecialchars(substr($product['product_id'], 0, 30)) . "...</td>";
            echo "<td><strong>" . htmlspecialchars($product['product_name'] ?? 'N/A') . "</strong></td>";
            echo "<td><strong>" . htmlspecialchars($product['total_zadane_mnozstvi']) . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p>";
        echo "<label for='test_quantity'>Testovací množství:</label> ";
        echo "<input type='number' name='test_quantity' id='test_quantity' value='1' min='0.1' step='0.1' style='width: 100px;'>";
        echo "</p>";
        
        echo "<p>";
        echo "<button type='submit' name='test_trigger_existing' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>🧪 Otestovat trigger</button>";
        echo "</p>";
        
        echo "</form>";
    } else {
        logMessage("❌ Žádné produkty nebo tickety pro test!", true);
    }
    
    echo "<h2>🔗 Navigace</h2>";
    echo "<p>";
    echo "<a href='simple_trigger_test.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Jednoduchý test</a>";
    echo "<a href='create_correct_triggers.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Opravit triggery</a>";
    echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
    echo "</p>";
    
} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
