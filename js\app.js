/**
 * Inventurní systém - Hlavní JavaScript soubor
 */

// Globální proměnné
let currentUser = null;
let activeSession = null;
let activeSessionEntries = [];
let isAdmin = false;
let isAdminOrManager = false;

// Konfigurace API
const API_URL = 'api';
const PROXY_URL = null; // Proxy není potřeba pro lokální API

// Pomocná funkce pro vytvoření URL s parametry
function buildApiUrl(endpoint, params = {}) {
    console.log('buildApiUrl - endpoint:', endpoint);
    console.log('buildApiUrl - params:', params);

    // Upravíme endpoint pro API
    // Speciální případ pro simple_auth
    if (endpoint === 'simple_auth') {
        endpoint = 'simple_auth.php';
    } else {
        // Pokud endpoint obsahuje lomítko (např. 'auth/login'), použijeme první část jako soubor a druhou jako parametr action
        if (endpoint.includes('/')) {
            const parts = endpoint.split('/');
            const file = parts[0];
            const action = parts[1];

            // Pokud poslední část je číslo (ID), použijeme PATH_INFO
            if (!isNaN(action)) {
                endpoint = file + '.php/' + action;
            } else {
                // Jinak použijeme parametr action
                endpoint = endpoint + '.php';
            }
        } else {
            // Pokud endpoint neobsahuje lomítko, jednoduše přidáme .php';
            endpoint = endpoint + '.php';
        }
    }

    console.log('buildApiUrl - upravený endpoint:', endpoint);

    // Upravíme cestu k API tak, aby neobsahovala dvojité lomítko
    let baseUrl = API_URL;
    if (baseUrl.endsWith('/')) {
        baseUrl = baseUrl.slice(0, -1);
    }

    let url = baseUrl + '/' + endpoint;
    console.log('buildApiUrl - základní URL:', url);

    // Přidání parametrů do URL
    const queryParams = new URLSearchParams();

    for (const key in params) {
        if (params[key] !== null && params[key] !== undefined) {
            queryParams.append(key, params[key]);
        }
    }

    const queryString = queryParams.toString();
    if (queryString) {
        url += `?${queryString}`;
    }

    console.log('buildApiUrl - výsledná URL:', url);
    // Proxy není potřeba pro lokální API
    return url;
}

/**
 * Kontrola a vytvoření potřebných tabulek a výchozího administrátorského účtu
 *
 * @returns {Promise} Promise, který se vyřeší po dokončení kontroly
 */
function checkTables() {
    return fetch('check_tables.php')
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se zkontrolovat tabulky');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('Tabulky a výchozí administrátorský účet byly úspěšně zkontrolovány');
            } else {
                console.error('Chyba při kontrole tabulek:', data.error);
            }
        });
}

/**
 * Prozatímní funkce pro přepnutí sekce změny hesla.
 */
function togglePasswordChangeSection() {
    console.warn('Funkce togglePasswordChangeSection je volána, ale není implementována.');
    // Zde může být například alert:
    // alert('Funkce pro změnu hesla zatím není implementována.');
}

/**
 * Prozatímní funkce pro zpracování vyhledávání produktů.
 * @param {Event} event - Událost odeslání formuláře.
 */
function handleProductSearch(event) {
    if (event && typeof event.preventDefault === 'function') {
        event.preventDefault();
    }
    console.warn('Funkce handleProductSearch je volána, ale není implementována.');
    // Zde může být například alert:
    // alert('Funkce pro vyhledávání produktů zatím není implementována.');
}
// Inicializace aplikace
document.addEventListener('DOMContentLoaded', () => {
    console.log('Inicializace aplikace...');

    // Skryjeme dynamické načítání informací o databázi
    const dbInfoSpinner = document.querySelector('#db-info .text-center');
    if (dbInfoSpinner) {
        dbInfoSpinner.style.display = 'none';
    }

    // Nastavení event listenerů
    setupEventListeners();

    // Nastavení event listeneru pro tlačítko změny hesla (pokud existuje)
    const changePasswordBtn = document.getElementById('change-password-btn');
    if (changePasswordBtn) {
        changePasswordBtn.addEventListener('click', togglePasswordChangeSection);
    }

    // Zobrazíme přihlašovací stránku
    showLoginPage();

    console.log('Aplikace inicializována');
});

/**
 * Kontrola, zda je uživatel přihlášen
 */
function checkAuth() {
    console.log('Kontrola přihlášení...');

    fetch(buildApiUrl('simple_auth', { action: 'check' }))
        .then(response => {
            // Kontrola, zda je odpověď OK
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Odpověď z API:', data);

            if (data.authenticated) {
                // Uživatel je přihlášen
                currentUser = data.user;
                showLoggedInUI();
            } else {
                // Uživatel není přihlášen
                showLoginPage();
            }
        })
        .catch(error => {
            console.error('Chyba při kontrole přihlášení:', error);
            // Zobrazení přihlašovací stránky v případě chyby
            showLoginPage();
        });
}

/**
 * Nastavení event listenerů
 */
function setupEventListeners() {
    // Přihlašovací formulář
    document.getElementById('login-form').addEventListener('submit', handleLogin);

    // Odhlášení
    document.getElementById('logout-btn').addEventListener('click', handleLogout);

    // Navigace
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            const page = e.target.getAttribute('data-page');
            showPage(page);
        });
    });

    // Ostatní event listenery přidáme až po implementaci příslušných funkcí

    // Vyhledávání produktů
    const productSearchForm = document.getElementById('product-search-form');
    if (productSearchForm && typeof handleProductSearch === 'function') {
        productSearchForm.addEventListener('submit', handleProductSearch);
    }

    // Přidání produktu do inventury
    const inventoryEntryForm = document.getElementById('inventory-entry-form');
    if (inventoryEntryForm && typeof handleAddInventoryEntry === 'function') {
        inventoryEntryForm.addEventListener('submit', handleAddInventoryEntry);
    }

    // Vytvoření nové inventurní relace
    const newSessionBtn = document.getElementById('new-session-btn');
    if (newSessionBtn && typeof handleCreateSession === 'function') {
        newSessionBtn.addEventListener('click', handleCreateSession);
    }

    // Export dat - prozatím deaktivováno
    // const exportForm = document.getElementById('export-form');
    // if (exportForm && typeof handleExport === 'function') {
        // exportForm.addEventListener('submit', handleExport);
   // }

    // Správa uživatelů
    const newUserBtn = document.getElementById('new-user-btn');
    if (newUserBtn && typeof showUserModal === 'function') {
        newUserBtn.addEventListener('click', () => showUserModal());
    }

    const saveUserBtn = document.getElementById('saveUserBtn');
    if (saveUserBtn && typeof handleSaveUser === 'function') {
        saveUserBtn.addEventListener('click', handleSaveUser);
    }
}

/**
 * Zpracování přihlášení
 */
function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    console.log('handleLogin - začátek přihlašování');
    console.log('handleLogin - uživatelské jméno:', username);
    console.log('handleLogin - délka uživatelského jména:', username.length);
    console.log('handleLogin - kód prvního znaku:', username.charCodeAt(0));
    console.log('handleLogin - kód posledního znaku:', username.charCodeAt(username.length - 1));
    console.log('handleLogin - heslo:', password);
    console.log('handleLogin - délka hesla:', password.length);

    // Zobrazení informace o probíhajícím přihlašování
    const loginError = document.getElementById('login-error');
    loginError.textContent = 'Probíhá přihlašování...';
    loginError.classList.remove('d-none');
    loginError.classList.remove('alert-danger');
    loginError.classList.add('alert-info');

    // Volání API pro přihlášení
    // Odstranění případných mezer na začátku a konci uživatelského jména a hesla
    const trimmedUsername = username.trim();
    const trimmedPassword = password.trim();

    // Kontrola, zda došlo ke změně po odstranění po odstranění mezer
    if (trimmedUsername !== username) {
        console.log('handleLogin - uživatelské jméno obsahovalo mezery na začátku nebo konci');
        console.log('handleLogin - původní uživatelské jméno:', username);
        console.log('handleLogin - upravené uživatelské jméno:', trimmedUsername);
    }

    if (trimmedPassword !== password) {
        console.log('handleLogin - heslo obsahovalo mezery na začátku nebo konci');
        console.log('handleLogin - původní heslo:', password);
        console.log('handleLogin - upravené heslo:', trimmedPassword);
    }

    const url = buildApiUrl('simple_auth', { action: 'login' });
    console.log('handleLogin - URL pro přihlášení:', url);
    console.log('handleLogin - data pro přihlášení:', JSON.stringify({ username: trimmedUsername, password: trimmedPassword }));

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ username: trimmedUsername, password: trimmedPassword })
    })
        .then(response => {
            console.log('handleLogin - odpověď ze serveru:', response);
            console.log('handleLogin - status odpovědi:', response.status);
            console.log('handleLogin - hlavičky odpovědi:', response.headers);

            // Kontrola, zda je odpověď OK
            if (!response.ok) {
                return response.text().then(text => {
                    console.error(`Chyba odpovědi: ${text}`);
                    let errorMessage = `HTTP error! status: ${response.status}, text: ${text}`;
                    try {
                        // Zkusíme parsovat text jako JSON
                        const errorData = JSON.parse(text);
                        errorMessage = `HTTP error! status: ${response.status}, message: ${errorData.error || 'Neznámá chyba'}, details: ${JSON.stringify(errorData.details || {})}`;
                    } catch (e) {
                        // Pokud parsování selže, ponecháme původní text
                        console.warn('Nepodařilo se parsovat JSON z odpovědi:', text);
                    }
                    throw new Error(errorMessage);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('handleLogin - data z odpovědi:', data);

            if (data.success) {
                console.log('handleLogin - přihlášení úspěšné');
                console.log('handleLogin - data uživatele:', data.user);

                // Úspěšné přihlášení
                currentUser = data.user;
                showLoggedInUI();

                // Skrytí chybové zprávy
                loginError.classList.add('d-none');
            } else {
                console.log('handleLogin - přihlášení neúspěšné');
                console.log('handleLogin - chybová zpráva:', data.error);

                // Neúspěšné přihlášení
                loginError.textContent = data.error || 'Neplatné přihlašovací údaje';
                loginError.classList.remove('alert-info');
                loginError.classList.add('alert-danger');
            }
        })
        .catch(error => {
            console.error('handleLogin - chyba při přihlašování:', error);
            console.error('handleLogin - stack trace:', error.stack);

            // Zobrazení chybové zprávy
            let errorMessage = 'Došlo k chybě při přihlašování. Zkontrolujte své přihlašovací údaje.';

            // Pokud máme podrobnější chybovou zprávu, zobrazíme ji
            if (error.message) {
                errorMessage = `Chyba při přihlašování: ${error.message}`; // Výchozí zpráva
                // Pokus o extrakci a parsování strukturovaných detailů
                try {
                    const detailsMatch = error.message.match(/details: (\{.*\})/);
                    if (detailsMatch && detailsMatch[1]) {
                        const details = JSON.parse(detailsMatch[1]);
                        let detailsHtml = '<ul>';
                        for (const field in details) {
                            detailsHtml += `<li>${field}: ${details[field]}</li>`;
                        }
                        detailsHtml += '</ul>';
                        errorMessage = `Chyba při přihlašování: ${detailsHtml}`;
                    }
                } catch (parseError) {
                    console.warn('Nepodařilo se parsovat detaily chyby z chybové zprávy:', parseError);
                    // errorMessage zůstává jako původní error.message, pokud parsování selže
                }

}
            loginError.innerHTML = errorMessage;
            loginError.classList.remove('alert-info');
            loginError.classList.add('alert-danger');
        });
}

/**
 * Zpracování odhlášení
 */
function handleLogout() {
    // Zobrazení informace o probíhajícím odhlašování
    console.log('Probíhá odhlašování...');

    fetch(buildApiUrl('simple_auth', { action: 'logout' }), {
        method: 'POST'
    })
        .then(response => {
            // Kontrola, zda je odpověď OK
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Odpověď z API:', data);

            // Resetování stavu aplikace
            currentUser = null;
            activeSession = null;
            activeSessionEntries = [];
            isAdmin = false;
            isAdminOrManager = false;

            // Odhlášení úspěšné
            console.log('handleLogout - odhlášení úspěšné');

            // Zobrazení přihlašovací stránky
            showLoginPage();
        })
        .catch(error => {
            console.error('Chyba při odhlašování:', error);

            // I v případě chyby odhlásíme uživatele lokálně
            currentUser = null;
            activeSession = null;
            activeSessionEntries = [];
            isAdmin = false;
            isAdminOrManager = false;
            showLoginPage();
        });
}

/**
 * Zobrazení přihlašovací stránky
 */
function showLoginPage() {
    // Skrytí navigační lišty
    document.getElementById('main-navbar').classList.add('d-none');

    // Skrytí všech stránek
    document.querySelectorAll('.page').forEach(page => {
        page.classList.add('d-none');
    });

    // Zobrazení přihlašovací stránky
    document.getElementById('login-page').classList.remove('d-none');

    // Vyčištění přihlašovacího formuláře
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
    document.getElementById('login-error').classList.add('d-none');
}

/**
 * Získání názvu role
 */
function getRoleName(role) {
    switch (role) {
        case 'admin':
        case 1:
        case '1':
            return 'Administrátor';
        case 'manager':
        case 2:
        case '2':
            return 'Manažer';
        case 'user':
        case 3:
        case '3':
        default:
            return 'Uživatel';
    }
}

/**
 * Zobrazení UI pro přihlášeného uživatele
 */
function showLoggedInUI() {
    // Zobrazení navigační lišty
    document.getElementById('main-navbar').classList.remove('d-none');

    // Nastavení informací o uživateli
    const fullName = currentUser.full_name || currentUser.username || 'Neznámý uživatel';
    document.getElementById('user-info').textContent = `${fullName} (${getRoleName(currentUser.role)})`;

    // Diagnostické výpisy
    console.log('showLoggedInUI - přihlášený uživatel:', currentUser);
    console.log('showLoggedInUI - role uživatele:', currentUser.role);
    console.log('showLoggedInUI - typ role uživatele:', currentUser.role);

    // Zobrazení/skrytí prvků podle role
    console.log('showLoggedInUI - kontrola role pro admin-only prvky');

    // Výpis všech admin-only prvků na stránce
    const adminOnlyElements = document.querySelectorAll('.admin-only');
    console.log('showLoggedInUI - počet admin-only prvků na stránce:', adminOnlyElements.length);
    adminOnlyElements.forEach((el, index) => {
        console.log(`showLoggedInUI - admin-only prvek ${index}:`, el);
        console.log(`showLoggedInUI - admin-only prvek ${index} display:`, window.getComputedStyle(el).display);
        console.log(`showLoggedInUI - admin-only prvek ${index} visibility:`, window.getComputedStyle(el).visibility);
        console.log(`showLoggedInUI - admin-only prvek ${index} HTML:`, el.outerHTML);
    });

    // Kontrola, zda je uživatel admin (jakýmkoliv způsobem)
    isAdmin = (
        currentUser.role === 'admin' ||
        (typeof currentUser.role === 'string' && currentUser.role.toLowerCase() === 'admin') ||
        currentUser.role === 1 ||
        currentUser.role === '1'
    );

    console.log('showLoggedInUI - je uživatel admin?', isAdmin);

    if (isAdmin) {
        console.log('showLoggedInUI - uživatel je admin, zobrazuji admin-only prvky');

        // Zobrazení všech admin-only prvků
        document.querySelectorAll('.admin-only').forEach(el => {
            console.log('showLoggedInUI - nastavuji admin-only prvek na display:block:', el);
            // Zkusíme různé způsoby zobrazení prvku
            el.style.display = 'block';
            el.style.visibility = 'visible';
            el.classList.remove('d-none');

            // Pokud je to položka menu, ujistíme se, že je viditelná
            if (el.classList.contains('nav-item')) {
                console.log('showLoggedInUI - manager-only prvek je položka menu, nastavuji speciální styly');
                el.style.display = 'list-item';
            }

            // Pokud je to stránka, ujistíme se, že je viditelná
            if (el.classList.contains('page')) {
                console.log('showLoggedInUI - admin-only prvek je stránka, odstraňuji d-none');
                el.classList.remove('d-none');
            }
        });
    } else {
        console.log('showLoggedInUI - uživatel NENÍ admin ani manager, skrývám manager-only prvky');
    }

    // Kontrola pro manager-only prvky
    console.log('showLoggedInUI - kontrola role pro manager-only prvky');

    // Výpis všech manager-only prvků na stránce
    const managerOnlyElements = document.querySelectorAll('.manager-only');
    console.log('showLoggedInUI - počet manager-only prvků na stránce:', managerOnlyElements.length);
    managerOnlyElements.forEach((el, index) => {
        console.log(`showLoggedInUI - manager-only prvek ${index}:`, el);
        console.log(`showLoggedInUI - manager-only prvek ${index} display:`, window.getComputedStyle(el).display);
        console.log(`showLoggedInUI - manager-only prvek ${index} visibility:`, window.getComputedStyle(el).visibility);
        console.log(`showLoggedInUI - manager-only prvek ${index} HTML:`, el.outerHTML);
    });

    // Kontrola, zda je uživatel admin nebo manager (jakýmkoliv způsobem)
    isAdminOrManager = (
        isAdmin ||
        currentUser.role === 'manager' ||
        (typeof currentUser.role === 'string' && currentUser.role.toLowerCase() === 'manager') ||
        currentUser.role === 2 ||
        currentUser.role === '2'
    );

    console.log('showLoggedInUI - je uživatel admin nebo manager?', isAdminOrManager);

    if (isAdminOrManager) {
        console.log('showLoggedInUI - uživatel je admin nebo manager, zobrazuji manager-only prvky');

        // Zobrazení všech manager-only prvků
        document.querySelectorAll('.manager-only').forEach(el => {
            console.log('showLoggedInUI - nastavuji manager-only prvek na display:block:', el);
            // Zkusíme různé způsoby zobrazení prvku
            el.style.display = 'block';
            el.style.visibility = 'visible';
            el.classList.remove('d-none');

            // Pokud je to položka menu, ujistíme se, že je viditelná
            if (el.classList.contains('nav-item')) {
                console.log('showLoggedInUI - manager-only prvek je položka menu, nastavuji speciální styly');
                el.style.display = 'list-item';
            }

            // Pokud je to stránka, ujistíme se, že je viditelná
            if (el.classList.contains('page')) {
                console.log('showLoggedInUI - admin-only prvek je stránka, odstraňuji d-none');
                el.classList.remove('d-none');
            }
        });
    } else {
        console.log('showLoggedInUI - uživatel NENÍ admin ani manager, skrývám manager-only prvky');
    }

    // Nastavení viditelnosti položky menu Uživatelé pouze pro administrátory a manažery
    console.log('showLoggedInUI - nastavuji viditelnost položky menu Uživatelé');
    const usersMenuItem = document.querySelector('.nav-link[data-page="users"]');
    if (usersMenuItem) {
        console.log('showLoggedInUI - nalezena položka menu Uživatelé:', usersMenuItem);
        const usersMenuItemParent = usersMenuItem.closest('.nav-item');
        if (usersMenuItemParent) {
            console.log('showLoggedInUI - nalezen rodič položky menu Uživatelé:', usersMenuItemParent);

            // Zobrazení pouze pro administrátory a manažery
            if (isAdminOrManager) {
                usersMenuItemParent.style.display = 'list-item';
                usersMenuItemParent.style.visibility = 'visible';
                usersMenuItemParent.classList.remove('d-none');
            } else {
                usersMenuItemParent.style.display = 'none';
                usersMenuItemParent.style.visibility = 'hidden';
                usersMenuItemParent.classList.add('d-none');
            }
        }
    }

    // Nastavení viditelnosti stránky Uživatelé pouze pro administrátory a manažery
    const usersPage = document.getElementById('users-page');
    if (usersPage) {
        console.log('showLoggedInUI - nalezena stránka Uživatelé:', usersPage);

        // Zobrazení pouze pro administrátory a manažery
        if (isAdminOrManager) {
            usersPage.classList.remove('d-none');
        } else {
            usersPage.classList.add('d-none');
        }
    }

    // Zobrazení dashboardu
    showPage('dashboard');
}

/**
 * Zobrazení konkrétní stránky
 */
function showPage(pageName) {
    console.log('showPage - zobrazuji stránku:', pageName);

    // Skrytí všech stránek
    document.querySelectorAll('.page').forEach(page => {
        page.classList.add('d-none');
    });

    // Zobrazení požadované stránky
    const pageElement = document.getElementById(`${pageName}-page`);
    if (pageElement) {
        console.log('showPage - nalezena stránka:', pageElement);
        pageElement.classList.remove('d-none');

        // Speciální zpracování pro stránku uživatelů
        if (pageName === 'users') {
            console.log('showPage - zobrazuji stránku uživatelů');

            // Zobrazení stránky uživatelů pouze pro administrátory a manažery
            if (isAdminOrManager) {
                console.log('showPage - zobrazuji stránku uživatelů pro administrátory a manažery');
                pageElement.style.display = 'block';
                pageElement.style.visibility = 'visible';
                pageElement.classList.remove('d-none');

                // Načtení dat uživatelů
                loadUsersData();
            } else {
                console.log('showPage - skrývám stránku uživatelů pro běžné uživatele');
                pageElement.style.display = 'none';
                pageElement.style.visibility = 'hidden';
                pageElement.classList.add('d-none');

                // Přesměrování na dashboard
                showPage('dashboard');
            }
        }
    } else {
        console.error('showPage - stránka nebyla nalezena:', pageName);
    }

    // Aktualizace aktivní položky v navigaci
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('data-page') === pageName) {
            link.classList.add('active');
        }
    });

    // Načtení dat pro konkrétní stránku
    switch (pageName) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'inventory':
            loadInventoryData();
            break;
        case 'total-inventory':
            loadTotalInventoryData();
            break;
        case 'reports':
            loadReportsData();
            break;
        case 'users':
            loadUsersData();
            break;
    }
}

/**
 * Načtení dat pro dashboard
 */
function loadDashboardData() {
    // Zobrazení načítání
    const activeSessionsList = document.getElementById('active-sessions-list');
    const completedSessionsList = document.getElementById('completed-sessions-list');

    if (activeSessionsList) {
        activeSessionsList.innerHTML = '<p class="text-center"><i class="fas fa-spinner fa-spin"></i> Načítání aktivních inventur...</p>';
    }

    if (completedSessionsList) {
        completedSessionsList.innerHTML = '<p class="text-center"><i class="fas fa-spinner fa-spin"></i> Načítání dokončených inventur...</p>';
    }

    // Načtení dat z API
    const url = buildApiUrl('inventory', { action: 'sessions' });
    console.log('URL pro načítání dat pro dashboard:', url);

    fetch(url)
        .then(response => {
            console.log('Odpověď ze serveru:', response);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('Data ze serveru:', data);

            if (data.sessions) {
                // Rozdělení relací na aktivní a dokončené
                const activeSessions = data.sessions.filter(session => session.status === 'active');
                const completedSessions = data.sessions.filter(session => session.status !== 'active');

                // Zobrazení aktivních relací
                const activeSessionsList = document.getElementById('active-sessions-list');
                if (activeSessions.length > 0) {
                    let html = '';
                    activeSessions.forEach(session => {
                        html += `
                            <div class="active-session">
                                <h5>Inventura #${session.id}</h5>
                                <p><strong>Vytvořeno:</strong> ${formatDate(session.start_time)}</p>
                                <p><strong>Uživatel:</strong> ${session.user}</p>
                                <p><strong>Počet položek:</strong> ${session.entry_count}</p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="selectSession(${session.id})">Vybrat</button>
                                    ${isAdminOrManager ? `
                                    <button class="btn btn-success" onclick="completeSession(${session.id})">Dokončit</button>
                                    <button class="btn btn-danger" onclick="cancelSession(${session.id})">Zrušit</button>
                                    ` : ''}
                                </div>
                            </div>
                        `;
                    });
                    activeSessionsList.innerHTML = html;
                } else {
                    activeSessionsList.innerHTML = '<p>Žádné aktivní inventury</p>';
                }

                // Zobrazení dokončených relací pouze pro administrátory a manažery
                const completedSessionsList = document.getElementById('completed-sessions-list');
                if (completedSessionsList && isAdminOrManager) {
                    if (completedSessions.length > 0) {
                        let html = '';
                        completedSessions.forEach(session => {
                            const sessionClass = session.status === 'completed' ? 'completed-session' : 'cancelled-session';
                            html += `
                                <div class="${sessionClass}">
                                    <h5>Inventura #${session.id}</h5>
                                    <p><strong>Vytvořeno:</strong> ${formatDate(session.start_time)}</p>
                                    <p><strong>Dokončeno:</strong> ${formatDate(session.end_time)}</p>
                                    <p><strong>Uživatel:</strong> ${session.user}</p>
                                    <p><strong>Stav:</strong> ${getStatusName(session.status)}</p>
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-primary btn-sm" onclick="viewSessionDetails(${session.id})">Zobrazit detail</button>
                                        <button class="btn btn-danger btn-sm" onclick="deleteCompletedSession(${session.id})">Smazat</button>
                                    </div>
                                </div>
                            `;
                        });
                        completedSessionsList.innerHTML = html;
                    } else {
                        completedSessionsList.innerHTML = '<p>Žádné dokončené inventury</p>';
                    }
                }
            }
        })
        .catch(error => {
            console.error('Chyba při načítání dat pro dashboard:', error);

            // V případě chyby zobrazíme prázdné seznamy
            const activeSessionsList = document.getElementById('active-sessions-list');
            if (activeSessionsList) {
                activeSessionsList.innerHTML = '<p>Nepodařilo se načíst aktivní inventury</p>';
            }

            const completedSessionsList = document.getElementById('completed-sessions-list');
            if (completedSessionsList) {
                completedSessionsList.innerHTML = '<p>Nepodařilo se načíst dokončené inventury</p>';
            }
        });
}

/**
 * Načtení dat pro inventuru
 */
function loadInventoryData() {
    // Zobrazení načítání
    const activeSessionInfo = document.getElementById('active-session-info');
    if (activeSessionInfo) {
        activeSessionInfo.innerHTML = '<p class="text-center"><i class="fas fa-spinner fa-spin"></i> Načítání inventury...</p>';
    }

    // Pokud již máme vybranou relaci, použijeme ji
    if (activeSession) {
        // Zobrazení informací o aktivní relaci
        if (activeSessionInfo) {
            activeSessionInfo.innerHTML = `
                <div class="active-session-info">
                    <h5>Inventura #${activeSession.id}</h5>
                    <p><strong>Vytvořeno:</strong> ${formatDate(activeSession.start_time)}</p>
                    <p><strong>Uživatel:</strong> ${activeSession.user}</p>
                    <p><strong>Počet položek:</strong> ${activeSession.entry_count}</p>
                    </div>
                </div>
            `;
        }

        // Načtení inventurních záznamů
        loadInventoryEntries();

        // Nastavení vyhledávání produktů
        setupProductSearch();
    } else {
        // Načtení všech inventurních relací
        fetch(buildApiUrl('inventory', { action: 'sessions' }))
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Zobrazení informace o tom, že není vybrána žádná aktivní inventura
                if (activeSessionInfo) {
                    activeSessionInfo.innerHTML = `
                        <div class="mb-3">
                            <p>Vyberte existující inventuru na dashboardu</p>
                        </div>
                    `;
                }

                // Zobrazení informace o výběru inventury v inventurních záznamech
                const inventoryEntries = document.getElementById('inventory-entries');
                if (inventoryEntries) {
                    inventoryEntries.innerHTML = '<div class="alert alert-info">Vyberte existující inventuru nebo vytvořte novou pro zobrazení inventurních záznamů.</div>';
                }

                // Nastavení vyhledávání produktů
                setupProductSearch();
            })
            .catch(error => {
                console.error('Chyba při načítání inventurních relací:', error);

                // V případě chyby zobrazíme informaci o chybě
                if (activeSessionInfo) {
                    activeSessionInfo.innerHTML = `
                        <div class="alert alert-danger">
                            <p>Nepodařilo se načíst inventurn</p>
                        </div>
                    `;
                }
            })
    }
}
function getStatusName(status) {
    switch (status) {
        case 'active':
            return 'Aktivní';
        case 'completed':
            return 'Dokončeno';
        case 'cancelled':
            return 'Zrušeno';
        default:
            return 'Neznámý stav';
    }
}

/**
 * Formátování data
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('cs-CZ');
}

/**
 * Načtení dat uživatelů
 */
function loadUsersData() {
    console.log('loadUsersData - začátek načítání uživatelů');

    // Zobrazení načítání
    const usersList = document.getElementById('users-list');
    if (usersList) {
        usersList.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Načítání...</span></div></div>';
    }

    // Načtení dat z API
    const url = buildApiUrl('users');
    console.log('loadUsersData - URL pro načítání uživatelů:', url);

    fetch(url)
        .then(response => {
            console.log('loadUsersData - odpověď ze serveru:', response);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('loadUsersData - data ze serveru:', data);

            if (data.users) {
                displayUsers(data.users);
            } else {
                throw new Error('Neplatná odpověď ze serveru');
            }
        })
        .catch(error => {
            console.error('loadUsersData - chyba při načítání uživatelů:', error);

            if (usersList) {
                usersList.innerHTML = `
                    <div class="alert alert-danger">
                        <p>Nepodařilo se načíst uživatele: ${error.message}</p>
                        <button class="btn btn-primary btn-sm" onclick="loadUsersData()">Zkusit znovu</button>
                    </div>
                `;
            }
        });
}

/**
 * Zobrazení seznamu uživatelů
 */
function displayUsers(users) {
    console.log('displayUsers - zobrazování uživatelů:', users);

    const usersList = document.getElementById('users-list');
    if (!usersList) {
        console.error('displayUsers - element users-list nenalezen');
        return;
    }

    if (!users || users.length === 0) {
        usersList.innerHTML = '<p>Žádní uživatelé k zobrazení.</p>';
        return;
    }

    let html = '<div class="table-responsive"><table class="table table-striped">';
    html += '<thead><tr>';
    html += '<th>ID</th>';
    html += '<th>Uživatelské jméno</th>';
    html += '<th>Celé jméno</th>';
    html += '<th>E-mail</th>';
    html += '<th>Role</th>';
    html += '<th>Aktivní</th>';
    html += '<th>Akce</th>';
    html += '</tr></thead><tbody>';

    users.forEach(user => {
        html += '<tr>';
        html += `<td>${user.id}</td>`;
        html += `<td>${user.username}</td>`;
        html += `<td>${user.full_name || '-'}</td>`;
        html += `<td>${user.email || '-'}</td>`;
        html += `<td>${getRoleName(user.role)}</td>`;
        html += `<td>${user.active ? '<span class="badge bg-success">Ano</span>' : '<span class="badge bg-danger">Ne</span>'}</td>`;
        html += '<td>';
        html += `<button class="btn btn-sm btn-primary me-1" onclick="editUser(${user.id})">Upravit</button>`;
        html += `<button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">Smazat</button>`;
        html += '</td>';
        html += '</tr>';
    });

    html += '</tbody></table></div>';
    usersList.innerHTML = html;
}

/**
 * Úprava uživatele
 */
function editUser(userId) {
    console.log('editUser - úprava uživatele s ID:', userId);
    // TODO: Implementovat úpravu uživatele
    alert('Funkce úpravy uživatele zatím není implementována.');
}

/**
 * Smazání uživatele
 */
function deleteUser(userId) {
    console.log('deleteUser - mazání uživatele s ID:', userId);

    if (!confirm('Opravdu chcete smazat tohoto uživatele?')) {
        return;
    }

    // TODO: Implementovat mazání uživatele
    alert('Funkce mazání uživatele zatím není implementována.');
}

/**
 * Zobrazení modálního okna pro uživatele
 */
function showUserModal(userId = null) {
    console.log('showUserModal - zobrazení modálního okna pro uživatele, ID:', userId);
    // TODO: Implementovat zobrazení modálního okna
    alert('Funkce pro správu uživatelů zatím není implementována.');
}

/**
 * Uložení uživatele
 */
function handleSaveUser() {
    console.log('handleSaveUser - uložení uživatele');
    // TODO: Implementovat uložení uživatele
    alert('Funkce pro uložení uživatele zatím není implementována.');
}

/**
 * Načtení dat pro celkovou inventuru
 */
function loadTotalInventoryData() {
    console.log('loadTotalInventoryData - načítání dat pro celkovou inventuru');
    const content = document.getElementById('total-inventory-content');
    if (content) {
        content.innerHTML = '<p>Funkce celkové inventury zatím není implementována.</p>';
    }
}

/**
 * Načtení dat pro reporty
 */
function loadReportsData() {
    console.log('loadReportsData - načítání dat pro reporty');
    // TODO: Implementovat načítání dat pro reporty
}

/**
 * Načtení dat pro inventuru
 */
function loadInventoryData() {
    console.log('loadInventoryData - načítání dat pro inventuru');
    // TODO: Implementovat načítání dat pro inventuru
}
