<?php
/**
 * Skript pro testování SQL dotazu pro chybějící produkty
 */

require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
$pdo = getDbConnection();

// Získání ID relace z GET parametru
$sessionId = $_GET['session_id'] ?? 1;

// SQL dotaz pro chybějící produkty
$sql = "
    SELECT
        p.id AS product_id,
        p.code AS ean_code,
        p.name AS product_name,
        c.name AS category,
        p.pricebuy,
        t.rate AS tax_rate,
        p.pricesell,
        COALESCE(s.units, 0) AS current_stock
    FROM
        products p
    LEFT JOIN
        categories c ON p.category = c.id
    LEFT JOIN
        taxes t ON p.taxcat = t.id
    LEFT JOIN
        stockcurrent s ON p.id = s.product
    LEFT JOIN (
        SELECT DISTINCT product_id
        FROM inventory_entries
        WHERE session_id = :session_id AND status = 'active'
    ) ie ON p.id = ie.product_id
    WHERE
        ie.product_id IS NULL
    ORDER BY
        p.name
";

// Parametry pro SQL dotaz
$params = ['session_id' => $sessionId];

try {
    // Provedení SQL dotazu
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    // Získání výsledků
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Výpis výsledků
    echo "<h1>Chybějící produkty pro relaci #$sessionId</h1>";
    echo "<p>Počet nalezených produktů: " . count($products) . "</p>";
    
    if (count($products) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($products[0]) as $key) {
            echo "<th>$key</th>";
        }
        echo "</tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            foreach ($product as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Žádné chybějící produkty nebyly nalezeny.</p>";
    }
} catch (Exception $e) {
    echo "<h1>Chyba</h1>";
    echo "<p>Došlo k chybě při provádění SQL dotazu: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
