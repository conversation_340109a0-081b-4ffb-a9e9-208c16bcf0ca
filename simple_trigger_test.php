<?php
/**
 * Diagnostika problému s automatickou aktualizací zadaného množství
 * <PERSON>ji<PERSON>, jak<PERSON> tabulky UniCenta používá pro prodeje
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();

    echo "<h1>Jednoduchý test triggerů - Přímá aktualizace</h1>";

    // KROK 1: Kontrola existujících triggerů
    logMessage("🔍 KROK 1: Kontroluji existující triggery...");

    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($triggers)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ PROBLÉM IDENTIFIKOVÁN</h3>";
        echo "<p style='color: #721c24; margin: 0;'><strong>V databázi nejsou žádné triggery!</strong></p>";
        echo "<p style='color: #721c24; margin: 10px 0 0 0;'>To je důvod, proč se zadané množství neaktualizuje při prodeji.</p>";
        echo "</div>";

        echo "<p><a href='create_simple_trigger.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Vytvořit trigger</a></p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    echo "<h3>Nalezené triggery:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Název</th><th>Událost</th><th>Tabulka</th><th>Timing</th></tr>";

    $inventory_triggers = 0;
    foreach ($triggers as $trigger) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($trigger['TRIGGER_NAME']) . "</td>";
        echo "<td>" . htmlspecialchars($trigger['EVENT_MANIPULATION']) . "</td>";
        echo "<td>" . htmlspecialchars($trigger['EVENT_OBJECT_TABLE']) . "</td>";
        echo "<td>" . htmlspecialchars($trigger['ACTION_TIMING']) . "</td>";
        echo "</tr>";

        // Počítáme triggery relevantní pro inventuru
        if (in_array($trigger['EVENT_OBJECT_TABLE'], ['ticketlines', 'stockcurrent', 'stockdiary'])) {
            $inventory_triggers++;
        }
    }
    echo "</table>";

    logMessage("✓ Nalezeno " . count($triggers) . " triggerů, z toho $inventory_triggers relevantních pro inventuru");

    // KROK 2: Test přímé aktualizace inventory_totals
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_direct_update'])) {
        $productId = $_POST['product_id'];
        $changeAmount = floatval($_POST['change_amount']);

        logMessage("🔄 Testuji přímou aktualizaci zadaného množství...");

        // Najdeme aktivní inventurní relaci
        $stmt = $pdo->query("
            SELECT id, title
            FROM inventory_sessions
            WHERE status = 'active'
            LIMIT 1
        ");
        $session = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$session) {
            logMessage("❌ Nebyla nalezena aktivní inventurní relace", true);
        } else {
            $sessionId = $session['id'];

            // Získáme aktuální zadané množství PŘED změnou
            $stmt = $pdo->prepare("
                SELECT total_zadane_mnozstvi, last_updated
                FROM inventory_totals
                WHERE product_id = ? AND session_id = ?
            ");
            $stmt->execute([$productId, $sessionId]);
            $currentTotal = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$currentTotal) {
                logMessage("❌ Produkt nemá záznam v inventory_totals", true);
            } else {
                $beforeChange = $currentTotal['total_zadane_mnozstvi'];
                $lastUpdatedBefore = $currentTotal['last_updated'];

                logMessage("✓ Zadané množství PŘED změnou: $beforeChange");
                logMessage("✓ Poslední aktualizace PŘED změnou: $lastUpdatedBefore");

                // Provedeme přímou aktualizaci
                $stmt = $pdo->prepare("
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi + ?,
                        last_updated = NOW()
                    WHERE product_id = ? AND session_id = ?
                ");
                $stmt->execute([$changeAmount, $productId, $sessionId]);

                logMessage("✓ Provedena přímá aktualizace (změna: $changeAmount)");

                // Zkontrolujeme výsledek
                $stmt = $pdo->prepare("
                    SELECT total_zadane_mnozstvi, last_updated
                    FROM inventory_totals
                    WHERE product_id = ? AND session_id = ?
                ");
                $stmt->execute([$productId, $sessionId]);
                $afterTotal = $stmt->fetch(PDO::FETCH_ASSOC);

                $afterChange = $afterTotal['total_zadane_mnozstvi'];
                $lastUpdatedAfter = $afterTotal['last_updated'];

                logMessage("✓ Zadané množství PO změně: $afterChange");
                logMessage("✓ Poslední aktualizace PO změně: $lastUpdatedAfter");

                $expectedAfter = $beforeChange + $changeAmount;

                if (abs($afterChange - $expectedAfter) < 0.001) {
                    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>✅ PŘÍMÁ AKTUALIZACE FUNGUJE</h3>";
                    echo "<p style='color: #155724; margin: 0;'>Tabulka inventory_totals se dá aktualizovat. Problém je v triggerech - buď neexistují, nebo se nespouštějí při prodeji.</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ CHYBA V AKTUALIZACI</h3>";
                    echo "<p style='color: #721c24; margin: 0;'>Ani přímá aktualizace nefunguje správně. Problém může být v databázi.</p>";
                    echo "</div>";
                }
            }
        }
    }

    // KROK 3: Test triggeru na ticketlines (pokud existuje)
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_trigger'])) {
        $productId = $_POST['product_id'];
        $quantity = floatval($_POST['quantity']);

        logMessage("🔄 Testuji trigger na ticketlines...");

        // Najdeme existující ticket pro test
        $stmt = $pdo->query("
            SELECT id
            FROM tickets
            ORDER BY id DESC
            LIMIT 1
        ");
        $existingTicket = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$existingTicket) {
            logMessage("❌ Nebyl nalezen žádný existující ticket pro test", true);
        } else {
            $ticketId = $existingTicket['id'];

            // Najdeme aktivní inventurní relaci
            $stmt = $pdo->query("
                SELECT id
                FROM inventory_sessions
                WHERE status = 'active'
                LIMIT 1
            ");
            $session = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$session) {
                logMessage("❌ Nebyla nalezena aktivní inventurní relace", true);
            } else {
                $sessionId = $session['id'];

                // Získáme aktuální zadané množství PŘED testem triggeru
                $stmt = $pdo->prepare("
                    SELECT total_zadane_mnozstvi
                    FROM inventory_totals
                    WHERE product_id = ? AND session_id = ?
                ");
                $stmt->execute([$productId, $sessionId]);
                $currentTotal = $stmt->fetch(PDO::FETCH_ASSOC);

                $beforeTrigger = $currentTotal ? $currentTotal['total_zadane_mnozstvi'] : 0;
                logMessage("✓ Zadané množství PŘED testem triggeru: $beforeTrigger");

                // Najdeme nejvyšší číslo řádku pro tento ticket
                $stmt = $pdo->prepare("
                    SELECT COALESCE(MAX(line), 0) + 1 as next_line
                    FROM ticketlines
                    WHERE ticket = ?
                ");
                $stmt->execute([$ticketId]);
                $nextLine = $stmt->fetch(PDO::FETCH_COLUMN);

                // Vložíme testovací řádek do ticketlines
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO ticketlines (ticket, line, product, units)
                        VALUES (?, ?, ?, ?)
                    ");
                    $stmt->execute([$ticketId, $nextLine, $productId, $quantity]);

                    logMessage("✓ Vložen testovací řádek do ticketlines");

                    // Počkáme na zpracování triggeru
                    sleep(1);

                    // Zkontrolujeme, zda se změnilo zadané množství
                    $stmt = $pdo->prepare("
                        SELECT total_zadane_mnozstvi, last_updated
                        FROM inventory_totals
                        WHERE product_id = ? AND session_id = ?
                    ");
                    $stmt->execute([$productId, $sessionId]);
                    $afterTotal = $stmt->fetch(PDO::FETCH_ASSOC);

                    $afterTrigger = $afterTotal ? $afterTotal['total_zadane_mnozstvi'] : 0;
                    $lastUpdated = $afterTotal ? $afterTotal['last_updated'] : 'N/A';

                    logMessage("✓ Zadané množství PO testu triggeru: $afterTrigger");
                    logMessage("✓ Poslední aktualizace: $lastUpdated");

                    $expectedAfter = $beforeTrigger - $quantity;

                    if (abs($afterTrigger - $expectedAfter) < 0.001) {
                        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                        echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>🎉 TRIGGER FUNGUJE!</h3>";
                        echo "<p style='color: #155724; margin: 0;'>Trigger na ticketlines správně aktualizuje zadané množství. Problém může být v tom, že UniCenta nepoužívá ticketlines pro prodeje.</p>";
                        echo "</div>";
                    } else {
                        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                        echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ TRIGGER NEFUNGUJE</h3>";
                        echo "<p style='color: #721c24; margin: 0;'>Trigger na ticketlines neaktualizuje zadané množství. Trigger buď neexistuje, nebo má chybu.</p>";
                        echo "</div>";
                    }

                    // Vyčistíme testovací data
                    $stmt = $pdo->prepare("
                        DELETE FROM ticketlines
                        WHERE ticket = ? AND line = ? AND product = ?
                    ");
                    $stmt->execute([$ticketId, $nextLine, $productId]);

                    logMessage("✓ Testovací data byla vyčištěna");

                } catch (PDOException $e) {
                    logMessage("❌ Chyba při testu triggeru: " . $e->getMessage(), true);
                }
            }
        }
    }

    // Formulář pro testování
    echo "<h2>Testování aktualizace zadaného množství</h2>";

    // Získání seznamu produktů s inventory_totals
    $stmt = $pdo->query("
        SELECT DISTINCT it.product_id, p.name, it.total_zadane_mnozstvi, s.title
        FROM inventory_totals it
        JOIN products p ON it.product_id = p.id
        JOIN inventory_sessions s ON it.session_id = s.id
        WHERE s.status = 'active'
        ORDER BY p.name
        LIMIT 10
    ");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($products)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #856404; margin: 0 0 10px 0;'>⚠ Žádné produkty k testování</h3>";
        echo "<p style='color: #856404; margin: 0;'>Nejsou žádné produkty v inventory_totals pro aktivní inventurní relace.</p>";
        echo "</div>";

        echo "<p><a href='direct_update_inventory_quantity.php'>Vytvořit záznamy v inventory_totals</a></p>";
    } else {
        echo "<h3>Test 1: Přímá aktualizace inventory_totals</h3>";
        echo "<form method='POST'>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Vybrat</th><th>Produkt</th><th>Aktuální zadané množství</th></tr>";

        foreach ($products as $product) {
            echo "<tr>";
            echo "<td><input type='radio' name='product_id' value='" . htmlspecialchars($product['product_id']) . "' required></td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['total_zadane_mnozstvi']) . "</td>";
            echo "</tr>";
        }

        echo "</table>";
        echo "<p><label>Změna množství: <input type='number' name='change_amount' value='-1' step='0.001' required></label> (záporné číslo = snížení)</p>";
        echo "<p><button type='submit' name='test_direct_update' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test přímé aktualizace</button></p>";
        echo "</form>";

        if ($inventory_triggers > 0) {
            echo "<h3>Test 2: Test triggeru na ticketlines</h3>";
            echo "<form method='POST'>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Vybrat</th><th>Produkt</th><th>Aktuální zadané množství</th></tr>";

            foreach ($products as $product) {
                echo "<tr>";
                echo "<td><input type='radio' name='product_id' value='" . htmlspecialchars($product['product_id']) . "' required></td>";
                echo "<td>" . htmlspecialchars($product['name']) . "</td>";
                echo "<td>" . htmlspecialchars($product['total_zadane_mnozstvi']) . "</td>";
                echo "</tr>";
            }

            echo "</table>";
            echo "<p><label>Množství k prodeji: <input type='number' name='quantity' value='1' step='0.001' min='0.001' required></label></p>";
            echo "<p><button type='submit' name='test_trigger' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test triggeru</button></p>";
            echo "</form>";
        }
    }

    echo "<p><a href='create_simple_trigger.php'>Vytvořit/opravit triggery</a></p>";
    echo "<p><a href='analyze_triggers.php'>Analyzovat triggery</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";

} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
