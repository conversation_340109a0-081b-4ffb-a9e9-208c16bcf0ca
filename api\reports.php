<?php
/**
 * Reporty API
 *
 * Tento soubor zpracovává API endpointy související s reporty a exporty.
 */

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../utils/validation.php';

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Získání metody požadavku
$method = $_SERVER['REQUEST_METHOD'];

// Získání cesty požadavku
$path = $_SERVER['PATH_INFO'] ?? '/';

// Kontrola, zda je uživatel přihlášen
if (!isLoggedIn()) {
    sendResponse(['error' => 'Neautorizovaný přístup'], 401);
    exit;
}

// Kontrola, zda je uživatel admin nebo manažer
if (!isAdminOrManager()) {
    sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
    exit;
}

// Zpracování různých endpointů
switch ($path) {
    case '/total':
        handleTotalInventory();
        break;

    case '/export':
        handleExport();
        break;

    case '/missing':
        handleMissingProducts();
        break;

    default:
        sendResponse(['error' => 'Endpoint nenalezen'], 404);
        break;
}

/**
 * Zpracování požadavku na celkovou inventuru
 */
function handleTotalInventory() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Kontrola, zda existuje tabulka inventory_totals
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = :dbname
        AND TABLE_NAME = 'inventory_totals'
    ");

    $dbname = $pdo->query("SELECT DATABASE()")->fetchColumn();
    $stmt->execute(['dbname' => $dbname]);
    $totalsTableExists = $stmt->fetchColumn() > 0;

    if (!$totalsTableExists) {
        // Vytvoření tabulky inventory_totals
        $pdo->exec("
            CREATE TABLE `inventory_totals` (
              `id` INT AUTO_INCREMENT PRIMARY KEY,
              `session_id` INT NOT NULL,
              `product_id` VARCHAR(255) NOT NULL,
              `total_zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
              `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (`session_id`) REFERENCES `inventory_sessions` (`id`) ON DELETE CASCADE,
              UNIQUE KEY `uk_inventory_totals_session_product` (`session_id`, `product_id`),
              INDEX `idx_inventory_totals_product` (`product_id`),
              INDEX `idx_inventory_totals_session` (`session_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");

        // Naplnění tabulky inventory_totals existujícími daty
        $pdo->exec("
            INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
            SELECT
                session_id,
                product_id,
                SUM(zadane_mnozstvi) AS total_zadane_mnozstvi
            FROM
                inventory_entries
            WHERE
                status = 'active'
            GROUP BY
                session_id, product_id
            ON DUPLICATE KEY UPDATE
                total_zadane_mnozstvi = VALUES(total_zadane_mnozstvi)
        ");
    }

    // Získání celkové inventury z tabulky inventory_totals
    $stmt = $pdo->prepare("
        SELECT
            p.id AS product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock,
            it.total_zadane_mnozstvi,
            (it.total_zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
            (SELECT COUNT(DISTINCT user_id) FROM inventory_entries WHERE session_id = :session_id AND product_id = p.id AND status = 'active') AS user_count,
            it.last_updated
        FROM
            inventory_totals it
        JOIN
            products p ON it.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        WHERE
            it.session_id = :session_id
        ORDER BY
            it.last_updated DESC
    ");

    $stmt->execute(['session_id' => $sessionId]);

    $inventory = $stmt->fetchAll();

    // Příprava dat pro odpověď
    $responseData = [];

    foreach ($inventory as $item) {
        $responseData[] = [
            'product_id' => $item['product_id'],
            'ean_code' => $item['ean_code'],
            'product_name' => $item['product_name'],
            'category' => $item['category'],
            'pricebuy' => $item['pricebuy'],
            'tax_rate' => $item['tax_rate'],
            'pricesell' => $item['pricesell'],
            'price_with_tax' => $item['pricesell'] * (1 + ($item['tax_rate'] / 100)),
            'current_stock' => $item['current_stock'],
            'zadane_mnozstvi' => $item['total_zadane_mnozstvi'],
            'difference' => $item['difference'],
            'user_count' => $item['user_count'],
            'last_updated' => $item['last_updated']
        ];
    }

    sendResponse(['inventory' => $responseData]);
}

/**
 * Zpracování požadavku na export dat
 */
function handleExport() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Kontrola, zda existuje tabulka inventory_totals
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = :dbname
        AND TABLE_NAME = 'inventory_totals'
    ");

    $dbname = $pdo->query("SELECT DATABASE()")->fetchColumn();
    $stmt->execute(['dbname' => $dbname]);
    $totalsTableExists = $stmt->fetchColumn() > 0;

    if (!$totalsTableExists) {
        // Vytvoření tabulky inventory_totals
        $pdo->exec("
            CREATE TABLE `inventory_totals` (
              `id` INT AUTO_INCREMENT PRIMARY KEY,
              `session_id` INT NOT NULL,
              `product_id` VARCHAR(255) NOT NULL,
              `total_zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
              `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
              FOREIGN KEY (`session_id`) REFERENCES `inventory_sessions` (`id`) ON DELETE CASCADE,
              UNIQUE KEY `uk_inventory_totals_session_product` (`session_id`, `product_id`),
              INDEX `idx_inventory_totals_product` (`product_id`),
              INDEX `idx_inventory_totals_session` (`session_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");

        // Naplnění tabulky inventory_totals existujícími daty
        $pdo->exec("
            INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
            SELECT
                session_id,
                product_id,
                SUM(zadane_mnozstvi) AS total_zadane_mnozstvi
            FROM
                inventory_entries
            WHERE
                status = 'active'
            GROUP BY
                session_id, product_id
            ON DUPLICATE KEY UPDATE
                total_zadane_mnozstvi = VALUES(total_zadane_mnozstvi)
        ");
    }

    // Získání dat pro export z tabulky inventory_totals
    $stmt = $pdo->prepare("
        SELECT
            p.id AS product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            p.pricesell * (1 + (t.rate / 100)) AS price_with_tax,
            COALESCE(s.units, 0) AS current_stock,
            it.total_zadane_mnozstvi,
            (it.total_zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
            (SELECT GROUP_CONCAT(DISTINCT u.username) FROM inventory_entries ie JOIN inventory_users u ON ie.user_id = u.id WHERE ie.session_id = :session_id AND ie.product_id = p.id AND ie.status = 'active') AS users,
            it.last_updated
        FROM
            inventory_totals it
        JOIN
            products p ON it.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        WHERE
            it.session_id = :session_id
        ORDER BY
            p.name
    ");

    $stmt->execute(['session_id' => $sessionId]);

    $data = $stmt->fetchAll();

    // Příprava dat pro export
    $exportData = [];

    foreach ($data as $item) {
        $exportData[] = [
            'ID' => $item['product_id'],
            'EAN' => $item['ean_code'],
            'Název produktu' => $item['product_name'],
            'Kategorie' => $item['category'],
            'Nákupní cena' => $item['pricebuy'],
            'DPH (%)' => $item['tax_rate'],
            'Prodejní cena bez DPH' => $item['pricesell'],
            'Prodejní cena s DPH' => $item['price_with_tax'],
            'Aktuální stav' => $item['current_stock'],
            'Zadané množství' => $item['total_zadane_mnozstvi'],
            'Rozdíl' => $item['difference'],
            'Uživatelé' => $item['users'],
            'Poslední aktualizace' => $item['last_updated']
        ];
    }

    sendResponse(['data' => $exportData]);
}

/**
 * Zpracování požadavku na nezadané položky
 */
function handleMissingProducts() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Metoda není povolena'], 405);
        return;
    }

    // Získání ID relace
    $sessionId = $_GET['session_id'] ?? null;

    if (!$sessionId) {
        sendResponse(['error' => 'ID relace je povinné'], 400);
        return;
    }

    // Validace ID relace
    $sessionId = validateInt($sessionId);

    if (!$sessionId) {
        sendResponse(['error' => 'Neplatné ID relace'], 400);
        return;
    }

    // Kontrola, zda relace existuje
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
    $stmt->execute(['id' => $sessionId]);

    $session = $stmt->fetch();

    if (!$session) {
        sendResponse(['error' => 'Inventurní relace nenalezena'], 404);
        return;
    }

    // Získání nezadaných položek
    $stmt = $pdo->prepare("
        SELECT
            p.id AS product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock
        FROM
            products p
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        LEFT JOIN (
            SELECT
                product_id
            FROM
                inventory_entries
            WHERE
                session_id = :session_id
                AND status = 'active'
            GROUP BY
                product_id
        ) ie ON p.id = ie.product_id
        WHERE
            ie.product_id IS NULL
            AND COALESCE(s.units, 0) > 0
        ORDER BY
            p.name
    ");

    $stmt->execute(['session_id' => $sessionId]);

    $products = $stmt->fetchAll();

    // Příprava dat pro odpověď
    $responseData = [];

    foreach ($products as $product) {
        $responseData[] = [
            'product_id' => $product['product_id'],
            'ean_code' => $product['ean_code'],
            'product_name' => $product['product_name'],
            'category' => $product['category'],
            'pricebuy' => $product['pricebuy'],
            'tax_rate' => $product['tax_rate'],
            'pricesell' => $product['pricesell'],
            'price_with_tax' => $product['pricesell'] * (1 + ($product['tax_rate'] / 100)),
            'current_stock' => $product['current_stock']
        ];
    }

    sendResponse(['products' => $responseData]);
}

/**
 * Odeslání JSON odpovědi
 *
 * @param mixed $data Data odpovědi
 * @param int $statusCode HTTP stavový kód
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}
