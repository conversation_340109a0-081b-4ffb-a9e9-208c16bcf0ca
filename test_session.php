<?php
/**
 * Test session mezi API soubory
 */

session_start();

echo "<h1>Test session</h1>";

// Test 1: Přihlášení
echo "<h2>1. Test přihlášení</h2>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    ensureTablesExist();
    
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné</p>";
        echo "<p>Uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
        echo "<p>Session ID: " . session_id() . "</p>";
        echo "<p>Session data: <pre>" . print_r($_SESSION, true) . "</pre></p>";
    } else {
        echo "<p style='color: red;'>✗ Přihl<PERSON>š<PERSON><PERSON> selhalo</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
}

// Test 2: Kontrola přihlášení
echo "<h2>2. Test kontroly přihlášení</h2>";

if (isLoggedIn()) {
    echo "<p style='color: green;'>✓ Uživatel je přihlášen</p>";
    
    $currentUser = getCurrentUser();
    if ($currentUser) {
        echo "<p>Aktuální uživatel: " . $currentUser['username'] . " (ID: " . $currentUser['id'] . ")</p>";
    } else {
        echo "<p style='color: red;'>✗ getCurrentUser() vrátil null</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Uživatel není přihlášen</p>";
}

// Test 3: Simulace API volání
echo "<h2>3. Test API volání</h2>";

echo "<h3>3a. Test simple_auth.php check</h3>";
$url = 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/simple_auth.php?action=check';
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Cookie: ' . $_SERVER['HTTP_COOKIE'] ?? ''
    ]
]);

$response = file_get_contents($url, false, $context);
echo "<p>Odpověď: <pre>" . htmlspecialchars($response) . "</pre></p>";

echo "<h3>3b. Test inventory.php GET</h3>";
$url = 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php';
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Cookie: ' . $_SERVER['HTTP_COOKIE'] ?? ''
    ]
]);

$response = file_get_contents($url, false, $context);
echo "<p>Odpověď: <pre>" . htmlspecialchars($response) . "</pre></p>";

echo "<h3>3c. Test inventory.php POST</h3>";
$url = 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php';
$data = json_encode([]);
$context = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Cookie: ' . ($_SERVER['HTTP_COOKIE'] ?? '')
        ],
        'content' => $data
    ]
]);

$response = file_get_contents($url, false, $context);
echo "<p>Odpověď: <pre>" . htmlspecialchars($response) . "</pre></p>";

echo "<h2>Závěr</h2>";
echo "<p><a href='test_create_inventory.html'>Test přes JavaScript</a></p>";
echo "<p><a href='index.html'>Hlavní aplikace</a></p>";
?>
