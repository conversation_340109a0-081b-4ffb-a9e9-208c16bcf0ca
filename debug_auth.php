<?php
/**
 * Debug autentizace
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug autentizace</h1>";

// Načtení potřebných souborů
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

echo "<h2>1. Test databázového připojení</h2>";
try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba připojení k databázi: " . $e->getMessage() . "</p>";
    exit;
}

echo "<h2>2. Test existence tabulek</h2>";
try {
    ensureTablesExist();
    echo "<p style='color: green;'>✓ Tabulky existují nebo byly vytvořeny</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba při kontrole/vytváření tabulek: " . $e->getMessage() . "</p>";
}

echo "<h2>3. Obsah tabulky inventory_users</h2>";
try {
    $stmt = $pdo->query("SELECT * FROM inventory_users ORDER BY id");
    $users = $stmt->fetchAll();
    
    if (empty($users)) {
        echo "<p style='color: orange;'>⚠ Žádní uživatelé v tabulce inventory_users</p>";
        
        // Pokusíme se vytvořit admin uživatele
        echo "<h3>Vytváření admin uživatele...</h3>";
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO inventory_users (username, password, role, full_name, email, active) VALUES (?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute(['admin', $hashedPassword, 'admin', 'Administrátor', '<EMAIL>', 1]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin uživatel byl vytvořen</p>";
        } else {
            echo "<p style='color: red;'>✗ Nepodařilo se vytvořit admin uživatele</p>";
        }
        
        // Znovu načteme uživatele
        $stmt = $pdo->query("SELECT * FROM inventory_users ORDER BY id");
        $users = $stmt->fetchAll();
    }
    
    if (!empty($users)) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Password (hash)</th><th>Role</th><th>Full Name</th><th>Email</th><th>Active</th></tr>";
        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>" . substr($user['password'], 0, 20) . "...</td>";
            echo "<td>{$user['role']}</td>";
            echo "<td>{$user['full_name']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>" . ($user['active'] ? 'Ano' : 'Ne') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba při načítání uživatelů: " . $e->getMessage() . "</p>";
}

echo "<h2>4. Test přihlášení admin/admin123</h2>";
try {
    $user = authenticateUser('admin', 'admin123');
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné</p>";
        echo "<pre>" . print_r($user, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Přihlášení neúspěšné</p>";
        
        // Zkusíme různé kombinace
        echo "<h3>Testování různých kombinací:</h3>";
        
        // Test s prázdným heslem
        $user = authenticateUser('admin', '');
        echo "<p>admin + prázdné heslo: " . ($user ? "✓ úspěch" : "✗ neúspěch") . "</p>";
        
        // Test s uživatelským jménem jako heslem
        $user = authenticateUser('admin', 'admin');
        echo "<p>admin + admin: " . ($user ? "✓ úspěch" : "✗ neúspěch") . "</p>";
        
        // Test s číselným heslem
        $user = authenticateUser('admin', '123');
        echo "<p>admin + 123: " . ($user ? "✓ úspěch" : "✗ neúspěch") . "</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba při přihlašování: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Test session</h2>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>Session data: <pre>" . print_r($_SESSION, true) . "</pre></p>";
echo "<p>Is logged in: " . (isLoggedIn() ? "Ano" : "Ne") . "</p>";

echo "<h2>6. Test API volání</h2>";
?>
<script>
async function testApiLogin() {
    try {
        const response = await fetch('api/simple_auth.php?action=login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                username: 'admin',
                password: 'admin123'
            })
        });
        
        const data = await response.json();
        
        document.getElementById('api-result').innerHTML = 
            '<h3>Výsledek API testu:</h3>' +
            '<p>Status: ' + response.status + '</p>' +
            '<p>Data: <pre>' + JSON.stringify(data, null, 2) + '</pre></p>';
            
    } catch (error) {
        document.getElementById('api-result').innerHTML = 
            '<h3>Chyba API testu:</h3>' +
            '<p style="color: red;">' + error.message + '</p>';
    }
}
</script>

<button onclick="testApiLogin()">Test API přihlášení</button>
<div id="api-result"></div>
