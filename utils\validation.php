<?php
/**
 * Validation Utility
 *
 * This file provides functions for input validation and sanitization.
 */

/**
 * Sanitize a string
 *
 * @param string $input The input string
 * @return string The sanitized string
 */
function sanitizeString($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate and sanitize an integer
 *
 * @param mixed $input The input value
 * @param int $min Minimum allowed value
 * @param int $max Maximum allowed value
 * @return int|false The sanitized integer or false if invalid
 */
function validateInt($input, $min = null, $max = null) {
    $input = filter_var($input, FILTER_VALIDATE_INT);

    if ($input === false) {
        return false;
    }

    if ($min !== null && $input < $min) {
        return false;
    }

    if ($max !== null && $input > $max) {
        return false;
    }

    return $input;
}

/**
 * Validate and sanitize a float
 *
 * @param mixed $input The input value
 * @param float $min Minimum allowed value
 * @param float $max Maximum allowed value
 * @return float|false The sanitized float or false if invalid
 */
function validateFloat($input, $min = null, $max = null) {
    $input = filter_var($input, FILTER_VALIDATE_FLOAT);

    if ($input === false) {
        return false;
    }

    if ($min !== null && $input < $min) {
        return false;
    }

    if ($max !== null && $input > $max) {
        return false;
    }

    return $input;
}

/**
 * Validate an email address
 *
 * @param string $email The email address
 * @return string|false The sanitized email or false if invalid
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

/**
 * Validate a username
 *
 * @param string $username The username
 * @param int $minLength Minimum length
 * @param int $maxLength Maximum length
 * @return string|false The sanitized username or false if invalid
 */
function validateUsername($username, $minLength = 1, $maxLength = 50) {
    $username = trim($username);

    if (strlen($username) < $minLength || strlen($username) > $maxLength) {
        return false;
    }

    // Akceptujeme všechny znaky kromě nebezpečných znaků pro HTML a SQL
    if (preg_match('/[<>\'";]/', $username)) {
        return false;
    }

    return $username;
}

/**
 * Validate a password
 *
 * @param string $password The password
 * @param int $minLength Minimum length
 * @return bool True if valid, false otherwise
 */
function validatePassword($password, $minLength = 1) {
    return strlen($password) >= $minLength;
}

/**
 * Validate an EAN code
 *
 * @param string $ean The EAN code
 * @return string|false The sanitized EAN or false if invalid
 */
function validateEAN($ean) {
    $ean = trim($ean);

    // EAN-13 is 13 digits, EAN-8 is 8 digits, UPC-A is 12 digits
    // For testing purposes, we'll accept any numeric string
    if (!preg_match('/^[0-9]+$/', $ean)) {
        return false;
    }

    return $ean;
}

/**
 * Validate input data against a schema
 *
 * @param array $data The input data
 * @param array $schema The validation schema
 * @return array Array with 'valid' (bool) and 'errors' (array) keys
 */
function validateData($data, $schema) {
    $errors = [];
    $validData = [];

    foreach ($schema as $field => $rules) {
        $value = $data[$field] ?? null;

        // Check if field is required
        if (isset($rules['required']) && $rules['required'] && ($value === null || $value === '')) {
            $errors[$field] = 'This field is required';
            continue;
        }

        // Skip validation if field is not required and value is empty
        if (($value === null || $value === '') && (!isset($rules['required']) || !$rules['required'])) {
            continue;
        }

        // Validate based on type
        switch ($rules['type']) {
            case 'string':
                $validData[$field] = sanitizeString($value);
                break;

            case 'int':
                $min = $rules['min'] ?? null;
                $max = $rules['max'] ?? null;
                $validValue = validateInt($value, $min, $max);

                if ($validValue === false) {
                    $errors[$field] = 'Invalid integer value';
                } else {
                    $validData[$field] = $validValue;
                }
                break;

            case 'float':
                $min = $rules['min'] ?? null;
                $max = $rules['max'] ?? null;
                
                // Oprava: Převod hodnoty na float před validací
                if (is_string($value)) {
                    // Nahrazení čárky tečkou (pro české formátování)
                    $value = str_replace(',', '.', $value);
                }
                
                $validValue = validateFloat($value, $min, $max);

                if ($validValue === false) {
                    $errors[$field] = 'Invalid float value';
                } else {
                    $validData[$field] = $validValue;
                }
                break;

            case 'email':
                $validValue = validateEmail($value);

                if ($validValue === false) {
                    $errors[$field] = 'Invalid email address';
                } else {
                    $validData[$field] = $validValue;
                }
                break;

            case 'username':
                $minLength = $rules['minLength'] ?? 3;
                $maxLength = $rules['maxLength'] ?? 50;
                $validValue = validateUsername($value, $minLength, $maxLength);

                if ($validValue === false) {
                    $errors[$field] = 'Invalid username';
                } else {
                    $validData[$field] = $validValue;
                }
                break;

            case 'password':
                $minLength = $rules['minLength'] ?? 1;

                if (!validatePassword($value, $minLength)) {
                    $errors[$field] = "Password must be at least $minLength characters";
                } else {
                    $validData[$field] = $value;
                }
                break;

            case 'ean':
                $validValue = validateEAN($value);

                if ($validValue === false) {
                    $errors[$field] = 'Invalid EAN code';
                } else {
                    $validData[$field] = $validValue;
                }
                break;
        }
    }

    return [
        'valid' => empty($errors),
        'errors' => $errors,
        'data' => $validData
    ];
}
