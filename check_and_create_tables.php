<?php
/**
 * Kontrola a vytvoření tabulek
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Kontrola a vytvoření tabulek</h1>";

try {
    // Načtení potřebných souborů
    require_once __DIR__ . '/utils/database.php';
    require_once __DIR__ . '/utils/auth.php';
    
    echo "<h2>1. Připojení k datab<PERSON>zi</h2>";
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení úspěšné</p>";
    
    echo "<h2>2. Kontrola existujících tabulek</h2>";
    $stmt = $pdo->query("SHOW TABLES");
    $existingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Existujíc<PERSON> tabulky:</p>";
    echo "<ul>";
    foreach ($existingTables as $table) {
        echo "<li>$table</li>";
    }
    echo "</ul>";
    
    echo "<h2>3. Kontrola inventurních tabulek</h2>";
    $requiredTables = [
        'inventory_sessions',
        'inventory_entries', 
        'inventory_stock_changes',
        'previous_stock',
        'inventory_users',
        'inventory_totals'
    ];
    
    $missingTables = [];
    foreach ($requiredTables as $table) {
        if (in_array($table, $existingTables)) {
            echo "<p style='color: green;'>✓ Tabulka '$table' existuje</p>";
        } else {
            echo "<p style='color: red;'>✗ Tabulka '$table' neexistuje</p>";
            $missingTables[] = $table;
        }
    }
    
    if (!empty($missingTables)) {
        echo "<h2>4. Vytváření chybějících tabulek</h2>";
        ensureTablesExist();
        echo "<p style='color: green;'>✓ Tabulky byly vytvořeny</p>";
        
        // Znovu zkontrolujeme
        $stmt = $pdo->query("SHOW TABLES");
        $newExistingTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($missingTables as $table) {
            if (in_array($table, $newExistingTables)) {
                echo "<p style='color: green;'>✓ Tabulka '$table' byla úspěšně vytvořena</p>";
            } else {
                echo "<p style='color: red;'>✗ Tabulka '$table' se nepodařilo vytvořit</p>";
            }
        }
    } else {
        echo "<p style='color: green;'>✓ Všechny potřebné tabulky existují</p>";
    }
    
    echo "<h2>5. Test vytvoření inventury</h2>";
    
    // Nejdříve se ujistíme, že máme admin uživatele
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "<p style='color: orange;'>⚠ Admin uživatel neexistuje, vytvářím...</p>";
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO inventory_users (username, password, role, full_name, email, active) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute(['admin', $hashedPassword, 'admin', 'Administrátor', '<EMAIL>', 1]);
        $adminId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Admin uživatel vytvořen s ID: $adminId</p>";
    } else {
        echo "<p style='color: green;'>✓ Admin uživatel existuje s ID: {$admin['id']}</p>";
        $adminId = $admin['id'];
    }
    
    // Zkusíme vytvořit testovací inventuru
    echo "<p>Vytváření testovací inventury...</p>";
    $stmt = $pdo->prepare("
        INSERT INTO inventory_sessions (user_id) 
        VALUES (?)
    ");
    $stmt->execute([$adminId]);
    $sessionId = $pdo->lastInsertId();
    
    if ($sessionId) {
        echo "<p style='color: green;'>✓ Testovací inventura vytvořena s ID: $sessionId</p>";
        
        // Zkontrolujeme, že se vytvořila
        $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = ?");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch();
        
        if ($session) {
            echo "<p style='color: green;'>✓ Inventura byla úspěšně uložena do databáze</p>";
            echo "<pre>" . print_r($session, true) . "</pre>";
        } else {
            echo "<p style='color: red;'>✗ Inventura se neuložila do databáze</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Nepodařilo se vytvořit testovací inventuru</p>";
    }
    
    echo "<h2>6. Kontrola všech inventur</h2>";
    $stmt = $pdo->query("
        SELECT 
            s.id, 
            s.start_time, 
            s.status, 
            u.username 
        FROM inventory_sessions s 
        JOIN inventory_users u ON s.user_id = u.id 
        ORDER BY s.id DESC 
        LIMIT 10
    ");
    $sessions = $stmt->fetchAll();
    
    if ($sessions) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Čas vytvoření</th><th>Stav</th><th>Uživatel</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['start_time']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>{$session['username']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Žádné inventury v databázi</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='test_create_inventory.html'>Test vytváření inventury</a></p>";
echo "<p><a href='index.html'>Hlavní aplikace</a></p>";
?>
