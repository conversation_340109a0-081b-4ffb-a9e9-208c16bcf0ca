<?php
/**
 * Test vytváření inventury s platnou session
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🧪 Test vytváření inventury s platnou session</h1>";

try {
    // Spuštění session
    session_start();
    
    // Kontrola přihlášení
    if (!isLoggedIn()) {
        logMessage("❌ Uživatel není přihl<PERSON>en - přesměrování na přihlášení", true);
        echo "<p><a href='index.html'>Přihlásit se</a></p>";
        exit;
    }
    
    $currentUser = getCurrentUser();
    logMessage("✅ Uživatel je přihlášen: " . $currentUser['username']);
    
    echo "<h2>📋 Informace o uživateli:</h2>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Vlastnost</th><th>Hodnota</th></tr>";
    echo "<tr><td>ID</td><td>" . htmlspecialchars($currentUser['id']) . "</td></tr>";
    echo "<tr><td>Username</td><td>" . htmlspecialchars($currentUser['username']) . "</td></tr>";
    echo "<tr><td>Role</td><td>" . htmlspecialchars($currentUser['role']) . "</td></tr>";
    echo "<tr><td>Session ID</td><td>" . htmlspecialchars(session_id()) . "</td></tr>";
    echo "</table>";
    
    // Test přímého volání createSession funkce
    echo "<h2>🔧 Test přímého volání createSession:</h2>";
    
    // Simulace POST požadavku
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET['action'] = 'sessions';
    
    // Zachycení výstupu
    ob_start();
    
    // Nastavení JSON vstupu
    $jsonInput = json_encode([]);
    
    // Simulace file_get_contents('php://input')
    $tempFile = tempnam(sys_get_temp_dir(), 'test_input');
    file_put_contents($tempFile, $jsonInput);
    
    // Přesměrování stdin
    $originalStdin = fopen('php://stdin', 'r');
    
    try {
        // Načtení a spuštění API
        include __DIR__ . '/api/inventory.php';
        
        $output = ob_get_contents();
        ob_end_clean();
        
        echo "<h3>Výstup API:</h3>";
        echo "<pre style='background: #f8f9fa; padding: 10px;'>";
        echo htmlspecialchars($output);
        echo "</pre>";
        
        // Analýza JSON odpovědi
        $jsonResponse = json_decode($output, true);
        if ($jsonResponse) {
            if (isset($jsonResponse['success']) && $jsonResponse['success']) {
                logMessage("✅ Inventura byla úspěšně vytvořena!");
                logMessage("📋 ID nové inventury: " . $jsonResponse['session_id']);
            } elseif (isset($jsonResponse['error'])) {
                logMessage("❌ Chyba při vytváření inventury: " . $jsonResponse['error'], true);
            }
        } else {
            logMessage("❌ Neplatná JSON odpověď", true);
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        logMessage("❌ Chyba při volání API: " . $e->getMessage(), true);
    }
    
    // Vyčištění
    unlink($tempFile);
    
    // Test pomocí interního volání
    echo "<h2>🔧 Test interního volání:</h2>";
    
    try {
        $pdo = getDbConnection();
        
        // Přímé vytvoření inventury
        $stmt = $pdo->prepare("
            INSERT INTO inventory_sessions (user_id, start_time, status)
            VALUES (:user_id, NOW(), 'active')
        ");
        
        $stmt->execute(['user_id' => $currentUser['id']]);
        $newSessionId = $pdo->lastInsertId();
        
        logMessage("✅ Inventura vytvořena přímo v databázi s ID: $newSessionId");
        
        // Ověření vytvoření
        $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = :id");
        $stmt->execute(['id' => $newSessionId]);
        $newSession = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($newSession) {
            echo "<h3>📋 Detaily nové inventury:</h3>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            echo "<tr><th>Vlastnost</th><th>Hodnota</th></tr>";
            foreach ($newSession as $key => $value) {
                echo "<tr><td>" . htmlspecialchars($key) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        logMessage("❌ Chyba při přímém vytvoření: " . $e->getMessage(), true);
    }
    
    // Test AJAX volání s cookies
    echo "<h2>🌐 Test AJAX volání s cookies:</h2>";
    
    $sessionName = session_name();
    $sessionId = session_id();
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_COOKIE, "$sessionName=$sessionId");
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p><strong>HTTP kód:</strong> $httpCode</p>";
    echo "<p><strong>Session cookie:</strong> $sessionName=$sessionId</p>";
    echo "<pre style='background: #f8f9fa; padding: 10px;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    if ($httpCode == 200) {
        $jsonResponse = json_decode($response, true);
        if ($jsonResponse && isset($jsonResponse['success'])) {
            logMessage("✅ AJAX test úspěšný!");
        } else {
            logMessage("❌ AJAX test vrátil neočekávanou odpověď", true);
        }
    } else {
        logMessage("❌ AJAX test neúspěšný (HTTP $httpCode)", true);
    }
    
    // JavaScript test
    echo "<h2>🖥️ JavaScript test:</h2>";
    echo "<button id='test-create-btn' onclick='testCreateInventory()' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test vytvoření inventury</button>";
    echo "<div id='test-result' style='margin-top: 10px;'></div>";
    
    echo "<script>
    function testCreateInventory() {
        const resultDiv = document.getElementById('test-result');
        resultDiv.innerHTML = '<p style=\"color: blue;\">Testování...</p>';
        
        fetch('api/inventory.php?action=sessions', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({})
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                resultDiv.innerHTML = '<p style=\"color: green;\">✅ Inventura úspěšně vytvořena! ID: ' + data.session_id + '</p>';
            } else {
                resultDiv.innerHTML = '<p style=\"color: red;\">❌ Chyba: ' + (data.error || 'Neznámá chyba') + '</p>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ Chyba při volání API: ' + error.message + '</p>';
        });
    }
    </script>";
    
} catch (Exception $e) {
    logMessage("❌ Chyba: " . $e->getMessage(), true);
    echo "<pre style='background: #f8d7da; padding: 10px;'>";
    echo "Soubor: " . $e->getFile() . "\n";
    echo "Řádek: " . $e->getLine() . "\n";
    echo "Chyba: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
    echo "</pre>";
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='index.html' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Hlavní aplikace</a>";
echo "<a href='fix_create_session.php' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Diagnostika</a>";
echo "</p>";
?>
