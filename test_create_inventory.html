<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test vytváření inventury</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test vytváření inventury</h1>
    
    <div class="test-section">
        <h2>1. Test přihlášení</h2>
        <button onclick="testLogin()">Přihlásit jako admin</button>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test API inventury</h2>
        <button onclick="testInventoryAPI()">Test GET api/inventory.php</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test vytvoření inventury</h2>
        <button onclick="testCreateInventory()">Vytvořit novou inventuru</button>
        <div id="create-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Test načtení inventur</h2>
        <button onclick="testLoadInventories()">Načíst seznam inventur</button>
        <div id="load-result"></div>
    </div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('login-result');
            resultDiv.innerHTML = '<div class="info">Přihlašování...</div>';
            
            try {
                const response = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Přihlášení úspěšné</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ Přihlášení neúspěšné: ${data.error}</div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function testInventoryAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="info">Testování API...</div>';
            
            try {
                const response = await fetch('api/inventory.php');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✓ API funguje</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function testCreateInventory() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.innerHTML = '<div class="info">Vytváření inventury...</div>';
            
            try {
                const response = await fetch('api/inventory.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const text = await response.text();
                console.log('Response text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Odpověď není JSON: ' + text);
                }
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Inventura vytvořena úspěšně!</div>
                        <p>ID inventury: ${data.session_id}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ Vytvoření inventury selhalo: ${data.error}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function testLoadInventories() {
            const resultDiv = document.getElementById('load-result');
            resultDiv.innerHTML = '<div class="info">Načítání inventur...</div>';
            
            try {
                const response = await fetch('api/inventory.php');
                const data = await response.json();
                
                if (data.sessions) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Inventury načteny</div>
                        <p>Počet inventur: ${data.sessions.length}</p>
                        <table border="1" style="border-collapse: collapse; width: 100%;">
                            <tr>
                                <th>ID</th>
                                <th>Uživatel</th>
                                <th>Stav</th>
                                <th>Vytvořeno</th>
                                <th>Počet položek</th>
                            </tr>
                            ${data.sessions.map(session => `
                                <tr>
                                    <td>${session.id}</td>
                                    <td>${session.user}</td>
                                    <td>${session.status}</td>
                                    <td>${session.start_time}</td>
                                    <td>${session.entry_count}</td>
                                </tr>
                            `).join('')}
                        </table>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ Neplatná odpověď</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        // Automatický test při načtení
        window.onload = function() {
            testLogin();
        };
    </script>
</body>
</html>
