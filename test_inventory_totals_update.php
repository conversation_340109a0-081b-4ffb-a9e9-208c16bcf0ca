<?php
/**
 * Test aktualizace inventory_totals
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test aktualizace inventory_totals</h1>";
    
    // Kontrola, zda existuje tabulka inventory_sessions
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_sessions'");
    $inventorySessionsExists = $stmt->rowCount() > 0;
    
    if (!$inventorySessionsExists) {
        echo "<p>Tabulka inventory_sessions neexistuje. Nelze pokračovat.</p>";
        exit;
    }
    
    // Kontrola, zda existuje tabulka inventory_totals
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_totals'");
    $inventoryTotalsExists = $stmt->rowCount() > 0;
    
    if (!$inventoryTotalsExists) {
        echo "<p>Tabulka inventory_totals neexistuje. Nelze pokračovat.</p>";
        exit;
    }
    
    // Kontrola, zda existuje tabulka stockcurrent
    $stmt = $pdo->query("SHOW TABLES LIKE 'stockcurrent'");
    $stockcurrentExists = $stmt->rowCount() > 0;
    
    if (!$stockcurrentExists) {
        echo "<p>Tabulka stockcurrent neexistuje. Nelze pokračovat.</p>";
        exit;
    }
    
    // Získání aktivních inventory_sessions
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active'");
    $activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($activeSessions)) {
        echo "<p>Nejsou žádné aktivní inventory_sessions. Nelze pokračovat.</p>";
        exit;
    }
    
    // Získání produktů z tabulky stockcurrent
    $stmt = $pdo->query("SELECT * FROM stockcurrent ORDER BY product ASC LIMIT 10");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "<p>Nejsou žádné produkty v tabulce stockcurrent. Nelze pokračovat.</p>";
        exit;
    }
    
    // Vytvoření testovacích záznamů v tabulce inventory_totals
    if (isset($_POST['create_test_records'])) {
        $sessionId = $_POST['session_id'];
        
        foreach ($products as $product) {
            $productId = $product['product'];
            
            // Kontrola, zda již existuje záznam pro tento produkt a session
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM inventory_totals WHERE session_id = ? AND product_id = ?");
            $stmt->execute([$sessionId, $productId]);
            $count = $stmt->fetchColumn();
            
            if ($count == 0) {
                // Vytvoření nového záznamu
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
                    VALUES (?, ?, 100)
                ");
                $stmt->execute([$sessionId, $productId]);
            } else {
                // Aktualizace existujícího záznamu
                $stmt = $pdo->prepare("
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = 100
                    WHERE session_id = ? AND product_id = ?
                ");
                $stmt->execute([$sessionId, $productId]);
            }
        }
        
        echo "<p>Testovací záznamy byly vytvořeny.</p>";
    }
    
    // Simulace změny v tabulce stockcurrent
    if (isset($_POST['simulate_stockcurrent_change'])) {
        $productId = $_POST['product_id'];
        $difference = $_POST['difference'];
        
        // Získání aktuální hodnoty units
        $stmt = $pdo->prepare("SELECT units FROM stockcurrent WHERE product = ?");
        $stmt->execute([$productId]);
        $currentUnits = $stmt->fetchColumn();
        
        if ($currentUnits === false) {
            echo "<p>Produkt s ID " . htmlspecialchars($productId) . " nebyl nalezen v tabulce stockcurrent.</p>";
        } else {
            // Aktualizace units
            $newUnits = $currentUnits - $difference;
            
            $stmt = $pdo->prepare("
                UPDATE stockcurrent
                SET units = ?
                WHERE product = ?
            ");
            $stmt->execute([$newUnits, $productId]);
            
            echo "<p>Hodnota units v tabulce stockcurrent byla změněna z " . htmlspecialchars($currentUnits) . " na " . htmlspecialchars($newUnits) . ".</p>";
        }
    }
    
    // Odstranění existujícího triggeru
    if (isset($_POST['recreate_trigger'])) {
        try {
            $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_totals_after_stockcurrent_update`");
            
            // Vytvoření nového triggeru
            $pdo->exec("
                CREATE TRIGGER `update_inventory_totals_after_stockcurrent_update`
                AFTER UPDATE ON `stockcurrent`
                FOR EACH ROW
                BEGIN
                    -- Výpočet rozdílu mezi starou a novou hodnotou
                    DECLARE difference DECIMAL(10,3);
                    SET difference = OLD.units - NEW.units;

                    -- Logování změny pro diagnostiku
                    INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                    VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                    -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme celkové součty
                    IF difference != 0 THEN
                        -- Aktualizace celkových součtů v inventory_totals
                        UPDATE inventory_totals
                        SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                        WHERE product_id = NEW.product
                        AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                    END IF;
                END
            ");
            
            echo "<p>Trigger byl znovu vytvořen.</p>";
        } catch (PDOException $e) {
            echo "<p>Chyba při vytváření triggeru: " . $e->getMessage() . "</p>";
        }
    }
    
    // Formulář pro vytvoření testovacích záznamů
    echo "<h2>Vytvoření testovacích záznamů v tabulce inventory_totals</h2>";
    echo "<form method='post'>";
    echo "<p>Session ID: <select name='session_id'>";
    
    foreach ($activeSessions as $session) {
        echo "<option value='" . htmlspecialchars($session['id']) . "'>" . htmlspecialchars($session['id']) . " - " . htmlspecialchars($session['name']) . "</option>";
    }
    
    echo "</select></p>";
    echo "<p><input type='submit' name='create_test_records' value='Vytvořit testovací záznamy'></p>";
    echo "</form>";
    
    // Formulář pro simulaci změny v tabulce stockcurrent
    echo "<h2>Simulace změny v tabulce stockcurrent</h2>";
    echo "<form method='post'>";
    echo "<p>Product ID: <select name='product_id'>";
    
    foreach ($products as $product) {
        echo "<option value='" . htmlspecialchars($product['product']) . "'>" . htmlspecialchars($product['product']) . " - Units: " . htmlspecialchars($product['units']) . "</option>";
    }
    
    echo "</select></p>";
    echo "<p>Difference (kladné číslo = snížení units, záporné číslo = zvýšení units): <input type='text' name='difference' value='1'></p>";
    echo "<p><input type='submit' name='simulate_stockcurrent_change' value='Simulovat změnu'></p>";
    echo "</form>";
    
    // Formulář pro opětovné vytvoření triggeru
    echo "<h2>Opětovné vytvoření triggeru</h2>";
    echo "<form method='post'>";
    echo "<p><input type='submit' name='recreate_trigger' value='Znovu vytvořit trigger'></p>";
    echo "</form>";
    
    // Získání obsahu tabulky inventory_totals
    $stmt = $pdo->query("SELECT * FROM inventory_totals ORDER BY session_id DESC, product_id ASC LIMIT 100");
    $totals = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Obsah tabulky inventory_totals</h2>";
    
    if (empty($totals)) {
        echo "<p>Tabulka inventory_totals je prázdná.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr>";
        
        // Dynamicky vytvoříme hlavičku tabulky podle dostupných sloupců
        foreach (array_keys($totals[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
        
        echo "</tr>";
        
        foreach ($totals as $total) {
            echo "<tr>";
            
            foreach ($total as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Získání obsahu tabulky stockcurrent_log
    $stmt = $pdo->query("SELECT * FROM stockcurrent_log ORDER BY created_at DESC LIMIT 10");
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Poslední záznamy v tabulce stockcurrent_log</h2>";
    
    if (empty($logs)) {
        echo "<p>Tabulka stockcurrent_log je prázdná.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Product ID</th><th>Old Units</th><th>New Units</th><th>Difference</th><th>Created At</th></tr>";
        
        foreach ($logs as $log) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($log['id']) . "</td>";
            echo "<td>" . htmlspecialchars($log['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($log['old_units']) . "</td>";
            echo "<td>" . htmlspecialchars($log['new_units']) . "</td>";
            echo "<td>" . htmlspecialchars($log['difference']) . "</td>";
            echo "<td>" . htmlspecialchars($log['created_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
