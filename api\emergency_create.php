<?php
error_reporting(E_ALL);
ini_set("display_errors", 1);

// Logování do souboru
$logFile = __DIR__ . "/../emergency.log";
function emergencyLog($message) {
    global $logFile;
    file_put_contents($logFile, date("Y-m-d H:i:s") . " - " . $message . "\n", FILE_APPEND);
}

emergencyLog("=== API CALL START ===");

try {
    emergencyLog("Starting session...");
    session_start();
    emergencyLog("Session started: " . session_id());
    
    emergencyLog("Setting headers...");
    header("Content-Type: application/json");
    
    emergencyLog("Loading database...");
    require_once __DIR__ . "/../utils/database.php";
    
    emergencyLog("Loading auth...");
    require_once __DIR__ . "/../utils/auth.php";
    
    emergencyLog("Checking login...");
    if (!isLoggedIn()) {
        emergencyLog("User not logged in");
        http_response_code(401);
        echo json_encode(["error" => "Not logged in"]);
        exit;
    }
    
    emergencyLog("Getting current user...");
    $user = getCurrentUser();
    emergencyLog("Current user: " . $user["username"]);
    
    emergencyLog("Getting database connection...");
    $pdo = getDbConnection();
    
    emergencyLog("Checking if table exists...");
    $stmt = $pdo->query("SHOW TABLES LIKE \"inventory_sessions\"");
    if ($stmt->rowCount() == 0) {
        emergencyLog("Table does not exist, creating...");
        $createSQL = "CREATE TABLE inventory_sessions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status VARCHAR(20) DEFAULT \"active\"
        )";
        $pdo->exec($createSQL);
        emergencyLog("Table created");
    }
    
    emergencyLog("Inserting new session...");
    $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (?)");
    $result = $stmt->execute([$user["id"]]);
    
    if ($result) {
        $sessionId = $pdo->lastInsertId();
        emergencyLog("Session created with ID: " . $sessionId);
        echo json_encode([
            "success" => true,
            "session_id" => $sessionId,
            "message" => "OK"
        ]);
    } else {
        emergencyLog("Failed to create session");
        echo json_encode(["error" => "Failed to create session"]);
    }
    
} catch (Exception $e) {
    emergencyLog("EXCEPTION: " . $e->getMessage());
    emergencyLog("TRACE: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage(), "trace" => $e->getTraceAsString()]);
}

emergencyLog("=== API CALL END ===");
?>