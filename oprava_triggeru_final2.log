[2025-05-22 14:43:42] Připojení k datab<PERSON>zi úspěšn<PERSON>
[2025-05-22 14:43:43] Trigger update_inventory_entries_after_stockcurrent_update existuje
[2025-05-22 14:43:43] Kód triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                -- <PERSON>ku<PERSON> je roz<PERSON><PERSON><PERSON> (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- <PERSON><PERSON><PERSON> prode<PERSON> (difference > 0) odečítáme zadané množství
                    -- <PERSON><PERSON><PERSON> na<PERSON> (difference < 0) přičít<PERSON>me zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 14:43:43] Existující trigger byl odstraněn
[2025-05-22 14:43:43] Nový trigger byl úspěšně vytvořen
[2025-05-22 14:43:43] Trigger update_inventory_entries_after_stockcurrent_update byl úspěšně vytvořen
[2025-05-22 14:43:43] Kód nového triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference, change_type)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference, 'UPDATE');

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 14:43:43] Oprava dokončena
