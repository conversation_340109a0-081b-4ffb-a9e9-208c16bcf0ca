<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jednoduché p<PERSON></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Jednoduch<PERSON> přihlášení</h4>
                    </div>
                    <div class="card-body">
                        <form id="simple-login-form">
                            <div class="mb-3">
                                <label for="username" class="form-label">Už<PERSON><PERSON>k<PERSON> j<PERSON></label>
                                <input type="text" class="form-control" id="username" value="admin" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">He<PERSON><PERSON></label>
                                <input type="password" class="form-control" id="password" value="admin123" required>
                            </div>
                            <div class="alert alert-danger d-none" id="login-error"></div>
                            <div class="alert alert-success d-none" id="login-success"></div>
                            <button type="submit" class="btn btn-primary w-100">Přihlásit</button>
                        </form>
                        
                        <hr>
                        
                        <div class="mt-3">
                            <h5>Diagnostika:</h5>
                            <button class="btn btn-secondary btn-sm me-2" onclick="testDatabase()">Test databáze</button>
                            <button class="btn btn-secondary btn-sm me-2" onclick="createAdmin()">Vytvořit admin</button>
                            <button class="btn btn-secondary btn-sm" onclick="testAPI()">Test API</button>
                        </div>
                        
                        <div id="diagnostic-result" class="mt-3"></div>
                    </div>
                </div>
                
                <!-- Úspěšné přihlášení -->
                <div id="logged-in-section" class="card mt-3 d-none">
                    <div class="card-header bg-success text-white">
                        <h4 class="mb-0">Přihlášen úspěšně!</h4>
                    </div>
                    <div class="card-body">
                        <div id="user-info"></div>
                        <button class="btn btn-danger mt-3" onclick="logout()">Odhlásit</button>
                        <a href="index.html" class="btn btn-primary mt-3">Přejít do aplikace</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentUser = null;
        
        document.getElementById('simple-login-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('login-error');
            const successDiv = document.getElementById('login-success');
            
            // Skrytí předchozích zpráv
            errorDiv.classList.add('d-none');
            successDiv.classList.add('d-none');
            
            console.log('Pokus o přihlášení:', username, password);
            
            fetch('api/simple_auth.php?action=login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Odpověď není JSON: ' + text);
                }
                
                if (data.success) {
                    currentUser = data.user;
                    successDiv.textContent = 'Přihlášení úspěšné!';
                    successDiv.classList.remove('d-none');
                    
                    // Zobrazení informací o uživateli
                    document.getElementById('user-info').innerHTML = `
                        <h5>Informace o uživateli:</h5>
                        <p><strong>ID:</strong> ${data.user.id}</p>
                        <p><strong>Username:</strong> ${data.user.username}</p>
                        <p><strong>Role:</strong> ${data.user.role}</p>
                        <p><strong>Full Name:</strong> ${data.user.full_name || 'N/A'}</p>
                        <p><strong>Email:</strong> ${data.user.email || 'N/A'}</p>
                        <p><strong>Active:</strong> ${data.user.active ? 'Ano' : 'Ne'}</p>
                    `;
                    
                    document.getElementById('logged-in-section').classList.remove('d-none');
                } else {
                    errorDiv.textContent = data.error || 'Přihlášení selhalo';
                    errorDiv.classList.remove('d-none');
                }
            })
            .catch(error => {
                console.error('Chyba:', error);
                errorDiv.textContent = 'Chyba: ' + error.message;
                errorDiv.classList.remove('d-none');
            });
        });
        
        function logout() {
            fetch('api/simple_auth.php?action=logout', {
                method: 'POST'
            })
            .then(() => {
                currentUser = null;
                document.getElementById('logged-in-section').classList.add('d-none');
                document.getElementById('login-success').classList.add('d-none');
            })
            .catch(error => {
                console.error('Chyba při odhlašování:', error);
            });
        }
        
        function testDatabase() {
            const resultDiv = document.getElementById('diagnostic-result');
            resultDiv.innerHTML = '<div class="text-primary">Testování databáze...</div>';
            
            fetch('debug_auth.php')
            .then(response => response.text())
            .then(text => {
                resultDiv.innerHTML = `
                    <h6>Test databáze:</h6>
                    <iframe src="debug_auth.php" width="100%" height="300" style="border: 1px solid #ccc;"></iframe>
                `;
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="text-danger">Chyba při testování databáze: ' + error.message + '</div>';
            });
        }
        
        function createAdmin() {
            const resultDiv = document.getElementById('diagnostic-result');
            resultDiv.innerHTML = '<div class="text-primary">Vytváření admin uživatele...</div>';
            
            fetch('create_admin.php')
            .then(response => response.text())
            .then(text => {
                resultDiv.innerHTML = `
                    <h6>Vytvoření admin uživatele:</h6>
                    <iframe src="create_admin.php" width="100%" height="300" style="border: 1px solid #ccc;"></iframe>
                `;
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="text-danger">Chyba při vytváření admin uživatele: ' + error.message + '</div>';
            });
        }
        
        function testAPI() {
            const resultDiv = document.getElementById('diagnostic-result');
            resultDiv.innerHTML = '<div class="text-primary">Testování API...</div>';
            
            fetch('api/simple_auth.php?action=test')
            .then(response => response.json())
            .then(data => {
                resultDiv.innerHTML = `
                    <h6>Test API:</h6>
                    <pre style="background: #f5f5f5; padding: 10px; border-radius: 5px;">${JSON.stringify(data, null, 2)}</pre>
                `;
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="text-danger">Chyba při testování API: ' + error.message + '</div>';
            });
        }
    </script>
</body>
</html>
