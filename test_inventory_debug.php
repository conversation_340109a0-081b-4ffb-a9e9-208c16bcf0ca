<?php
/**
 * Debug skript pro testování aktualizace zadaného množství v celkové inventuře
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Debug testování aktualizace zadaného množství v celkové inventuře</h1>";
    
    // Krok 1: Kontrola aktivní inventurní relace
    $stmt = $pdo->query("
        SELECT id, title, status
        FROM inventory_sessions
        WHERE status = 'active'
        LIMIT 1
    ");
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        logMessage("CHYBA: Nebyla nalezena žádná aktivní inventurní relace", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    $sessionId = $session['id'];
    logMessage("✓ Nalezena aktivní inventurní relace: " . $session['title'] . " (ID: " . $sessionId . ")");
    
    // Krok 2: Výběr produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logMessage("CHYBA: Nebyl nalezen žádný produkt s kladným množstvím na skladě", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    $productId = $product['id'];
    $productName = $product['name'];
    $currentStock = $product['units'];
    
    logMessage("✓ Vybrán produkt: " . $productName . " (ID: " . $productId . ")");
    logMessage("✓ Aktuální množství na skladě: " . $currentStock);
    
    // Krok 3: Kontrola záznamu v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT id, total_zadane_mnozstvi, last_updated
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$productId, $sessionId]);
    $inventoryTotal = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$inventoryTotal) {
        logMessage("⚠ Produkt nemá záznam v celkové inventuře, vytvářím nový...");
        
        // Vytvoření nového záznamu
        $stmt = $pdo->prepare("
            INSERT INTO inventory_totals (product_id, session_id, total_zadane_mnozstvi, created_at, last_updated)
            VALUES (?, ?, 10, NOW(), NOW())
        ");
        $stmt->execute([$productId, $sessionId]);
        
        // Načtení nově vytvořeného záznamu
        $stmt = $pdo->prepare("
            SELECT id, total_zadane_mnozstvi, last_updated
            FROM inventory_totals
            WHERE product_id = ? AND session_id = ?
        ");
        $stmt->execute([$productId, $sessionId]);
        $inventoryTotal = $stmt->fetch(PDO::FETCH_ASSOC);
        
        logMessage("✓ Vytvořen nový záznam v celkové inventuře s množstvím 10");
    }
    
    $totalId = $inventoryTotal['id'];
    $currentTotal = $inventoryTotal['total_zadane_mnozstvi'];
    $lastUpdated = $inventoryTotal['last_updated'];
    
    logMessage("✓ Aktuální zadané množství v celkové inventuře: " . $currentTotal);
    logMessage("✓ Poslední aktualizace: " . $lastUpdated);
    
    // Krok 4: Simulace prodeje
    $unitsToSell = 1;
    $newStock = $currentStock - $unitsToSell;
    
    logMessage("🔄 Simuluji prodej " . $unitsToSell . " kusů...");
    
    // Aktualizace stockcurrent
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = ?
        WHERE product = ?
    ");
    $stmt->execute([$newStock, $productId]);
    
    logMessage("✓ Aktualizováno množství na skladě na: " . $newStock);
    
    // Krok 5: Ruční aktualizace zadaného množství v celkové inventuře
    $difference = $currentStock - $newStock; // Rozdíl = 1 (prodáno)
    $newTotal = $currentTotal - $difference; // Snížíme zadané množství o prodané množství
    
    logMessage("🔄 Aktualizuji zadané množství v celkové inventuře...");
    logMessage("Rozdíl: " . $difference . ", Nové zadané množství: " . $newTotal);
    
    $stmt = $pdo->prepare("
        UPDATE inventory_totals
        SET total_zadane_mnozstvi = ?,
            last_updated = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$newTotal, $totalId]);
    
    // Krok 6: Kontrola výsledku
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi, last_updated
        FROM inventory_totals
        WHERE id = ?
    ");
    $stmt->execute([$totalId]);
    $updatedTotal = $stmt->fetch(PDO::FETCH_ASSOC);
    
    logMessage("✓ Nové zadané množství v celkové inventuře: " . $updatedTotal['total_zadane_mnozstvi']);
    logMessage("✓ Nová poslední aktualizace: " . $updatedTotal['last_updated']);
    
    if ($updatedTotal['total_zadane_mnozstvi'] == $newTotal) {
        logMessage("🎉 TEST ÚSPĚŠNÝ! Zadané množství bylo správně aktualizováno.");
    } else {
        logMessage("❌ TEST SELHAL! Zadané množství nebylo správně aktualizováno.", true);
    }
    
    // Krok 7: Kontrola triggerů
    echo "<h2>Kontrola triggerů</h2>";
    
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND (EVENT_OBJECT_TABLE = 'stockcurrent' OR EVENT_OBJECT_TABLE = 'ticketlines')
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($triggers)) {
        logMessage("⚠ Nejsou nalezeny žádné triggery na tabulkách stockcurrent nebo ticketlines");
    } else {
        logMessage("✓ Nalezeno " . count($triggers) . " triggerů:");
        foreach ($triggers as $trigger) {
            logMessage("- " . $trigger['TRIGGER_NAME'] . " (" . $trigger['ACTION_TIMING'] . " " . $trigger['EVENT_MANIPULATION'] . " na " . $trigger['EVENT_OBJECT_TABLE'] . ")");
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
