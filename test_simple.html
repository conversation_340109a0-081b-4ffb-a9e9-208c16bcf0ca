<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test přihlášení</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Test přihlášení - Inventurní systém</h1>
    
    <div class="test-section">
        <h2>1. Test API připojení</h2>
        <button onclick="testApiConnection()">Test API</button>
        <div id="api-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test přihlášení</h2>
        <form onsubmit="testLogin(event)">
            <input type="text" id="username" placeholder="Uživatelské jméno" value="admin">
            <input type="password" id="password" placeholder="Heslo" value="admin123">
            <button type="submit">Přihlásit</button>
        </form>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test kontroly přihlášení</h2>
        <button onclick="testCheckAuth()">Kontrola přihlášení</button>
        <div id="check-result"></div>
    </div>

    <script>
        async function testApiConnection() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.innerHTML = '<div class="info">Testování API připojení...</div>';
            
            try {
                const response = await fetch('api/simple_auth.php?action=test');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✓ API připojení úspěšné</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba API: ${error.message}</div>`;
            }
        }
        
        async function testLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('login-result');
            
            resultDiv.innerHTML = '<div class="info">Přihlašování...</div>';
            
            try {
                const response = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Přihlášení úspěšné</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ Přihlášení neúspěšné: ${data.error}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba při přihlašování: ${error.message}</div>`;
            }
        }
        
        async function testCheckAuth() {
            const resultDiv = document.getElementById('check-result');
            resultDiv.innerHTML = '<div class="info">Kontrola přihlášení...</div>';
            
            try {
                const response = await fetch('api/simple_auth.php?action=check');
                const data = await response.json();
                
                if (data.authenticated) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Uživatel je přihlášen</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="info">ℹ Uživatel není přihlášen</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba při kontrole: ${error.message}</div>`;
            }
        }
        
        // Automatický test při načtení stránky
        window.onload = function() {
            testApiConnection();
        };
    </script>
</body>
</html>
