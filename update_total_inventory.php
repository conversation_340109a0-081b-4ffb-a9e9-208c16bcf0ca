<?php
/**
 * Aktualizace zadaného množství v celkové inventuře
 * 
 * Tento skript aktualizuje zadané množství v celkové inventuře na základě aktuálního stavu zásob.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    $results = [];

    // Kontrola, zda je uživatel přihlášen a má oprávnění
    if (!isLoggedIn()) {
        echo "<h1>Neautorizovaný přístup</h1>";
        echo "<p>Pro přístup k této stránce musíte být přihlášeni.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    $user = getCurrentUser();
    if (!isAdminOrManager()) {
        echo "<h1>Nedostatečná oprávnění</h1>";
        echo "<p>Pro přístup k této stránce musíte mít oprávnění administrátora nebo manažera.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    // Získání seznamu aktivních inventurních relací
    $stmt = $pdo->query("
        SELECT id, CONCAT('Inventura #', id) AS name, status
        FROM inventory_sessions
        WHERE status = 'active'
    ");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($sessions)) {
        echo "<h1>Žádné aktivní inventurní relace</h1>";
        echo "<p>Nebyly nalezeny žádné aktivní inventurní relace.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    // Zpracování formuláře pro aktualizaci celkové inventury
    $updateResults = [];
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update'])) {
        $sessionId = $_POST['session_id'] ?? null;
        $updateMode = $_POST['update_mode'] ?? 'base_minus_current';
        $baseValue = $_POST['base_value'] ?? 100;
        
        if (!$sessionId) {
            $updateResults['error'] = 'Nebyla vybrána žádná inventurní relace.';
        } else {
            // Kontrola, zda vybraná relace existuje a je aktivní
            $stmt = $pdo->prepare("
                SELECT id, CONCAT('Inventura #', id) AS name, status
                FROM inventory_sessions
                WHERE id = ? AND status = 'active'
            ");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                $updateResults['error'] = 'Vybraná inventurní relace neexistuje nebo není aktivní.';
            } else {
                // Začátek transakce
                $pdo->beginTransaction();
                
                try {
                    // Získání seznamu všech produktů
                    $stmt = $pdo->query("
                        SELECT product, units
                        FROM stockcurrent
                    ");
                    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    $updateResults['products_count'] = count($products);
                    $updateResults['updated_count'] = 0;
                    $updateResults['products'] = [];
                    
                    if (empty($products)) {
                        $updateResults['message'] = "Nebyly nalezeny žádné produkty.";
                    } else {
                        foreach ($products as $product) {
                            $productId = $product['product'];
                            $currentUnits = $product['units'];
                            
                            // Výpočet nové hodnoty zadaného množství
                            if ($updateMode == 'set_to_base') {
                                // Nastavení na základní hodnotu
                                $newTotal = $baseValue;
                            } elseif ($updateMode == 'set_to_current') {
                                // Nastavení na aktuální stav zásob
                                $newTotal = $currentUnits;
                            } elseif ($updateMode == 'base_minus_current') {
                                // Základní hodnota mínus aktuální stav zásob
                                $newTotal = $baseValue - $currentUnits;
                            } else {
                                // Výchozí: základní hodnota
                                $newTotal = $baseValue;
                            }
                            
                            // Kontrola, zda existuje záznam v inventory_totals
                            $stmt = $pdo->prepare("
                                SELECT id, total_zadane_mnozstvi
                                FROM inventory_totals
                                WHERE product_id = ? AND session_id = ?
                            ");
                            $stmt->execute([$productId, $sessionId]);
                            $total = $stmt->fetch(PDO::FETCH_ASSOC);
                            
                            if ($total) {
                                // Aktualizace existujícího záznamu
                                $stmt = $pdo->prepare("
                                    UPDATE inventory_totals
                                    SET total_zadane_mnozstvi = ?
                                    WHERE id = ?
                                ");
                                $stmt->execute([$newTotal, $total['id']]);
                                
                                $updateResults['products'][] = [
                                    'id' => $productId,
                                    'current_units' => $currentUnits,
                                    'old_total' => $total['total_zadane_mnozstvi'],
                                    'new_total' => $newTotal,
                                    'updated' => true
                                ];
                                
                                $updateResults['updated_count']++;
                            } else {
                                // Vytvoření nového záznamu
                                $stmt = $pdo->prepare("
                                    INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
                                    VALUES (?, ?, ?)
                                ");
                                $stmt->execute([$sessionId, $productId, $newTotal]);
                                
                                $updateResults['products'][] = [
                                    'id' => $productId,
                                    'current_units' => $currentUnits,
                                    'old_total' => null,
                                    'new_total' => $newTotal,
                                    'updated' => true
                                ];
                                
                                $updateResults['updated_count']++;
                            }
                        }
                    }
                    
                    // Commit transakce
                    $pdo->commit();
                    
                    $updateResults['success'] = true;
                    $updateResults['message'] = "Aktualizace celkové inventury byla úspěšně dokončena. Aktualizováno {$updateResults['updated_count']} z {$updateResults['products_count']} produktů.";
                } catch (Exception $e) {
                    // Rollback transakce v případě chyby
                    $pdo->rollBack();
                    $updateResults['error'] = 'Došlo k chybě při aktualizaci: ' . $e->getMessage();
                }
            }
        }
    }

    // Zobrazení HTML
    echo "<!DOCTYPE html>
<html lang='cs'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Aktualizace zadaného množství v celkové inventuře</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input, button { padding: 8px; font-size: 16px; }
        button { background-color: #4CAF50; color: white; border: none; cursor: pointer; padding: 10px 15px; }
        button:hover { background-color: #45a049; }
        .success { color: green; padding: 10px; background-color: #f0fff0; border: 1px solid #d0e9c6; border-radius: 4px; margin: 10px 0; }
        .error { color: red; padding: 10px; background-color: #fff0f0; border: 1px solid #e9c6c6; border-radius: 4px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f1f1f1; }
        .actions { margin: 20px 0; }
        .actions a { 
            display: inline-block; 
            padding: 10px 15px; 
            background-color: #4CAF50; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin-right: 10px; 
        }
        .actions a:hover { background-color: #45a049; }
        .updated { color: green; }
        .not-updated { color: red; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Aktualizace zadaného množství v celkové inventuře</h1>
        
        <div class='actions'>
            <a href='index.html'>Zpět na hlavní stránku</a>
            <a href='sync_inventory.php'>Synchronizace všech produktů</a>
            <a href='auto_sync_after_sale.php'>Synchronizace po prodeji</a>
        </div>";
        
    if (!empty($updateResults)) {
        if (isset($updateResults['success']) && $updateResults['success']) {
            echo "<div class='success'>{$updateResults['message']}</div>";
            
            if (!empty($updateResults['products'])) {
                echo "<h2>Aktualizované produkty</h2>";
                echo "<table>";
                echo "<thead><tr><th>ID</th><th>Aktuální stav</th><th>Staré zadané množství</th><th>Nové zadané množství</th><th>Stav</th></tr></thead>";
                echo "<tbody>";
                
                foreach ($updateResults['products'] as $product) {
                    $statusClass = $product['updated'] ? 'updated' : 'not-updated';
                    $statusText = $product['updated'] ? 'Aktualizováno' : 'Neaktualizováno';
                    
                    echo "<tr>";
                    echo "<td>{$product['id']}</td>";
                    echo "<td>{$product['current_units']}</td>";
                    echo "<td>" . ($product['old_total'] !== null ? $product['old_total'] : 'N/A') . "</td>";
                    echo "<td>{$product['new_total']}</td>";
                    echo "<td class='$statusClass'>$statusText</td>";
                    echo "</tr>";
                }
                
                echo "</tbody></table>";
            }
        } elseif (isset($updateResults['error'])) {
            echo "<div class='error'>{$updateResults['error']}</div>";
        }
    }
        
    echo "<form method='post'>
            <div class='form-group'>
                <label for='session_id'>Vyberte inventurní relaci:</label>
                <select name='session_id' id='session_id' required>";
                
    foreach ($sessions as $session) {
        echo "<option value='{$session['id']}'>{$session['name']}</option>";
    }
                
    echo "</select>
            </div>
            
            <div class='form-group'>
                <label for='update_mode'>Režim aktualizace:</label>
                <select name='update_mode' id='update_mode'>
                    <option value='base_minus_current'>Základní hodnota mínus aktuální stav</option>
                    <option value='set_to_base'>Nastavit na základní hodnotu</option>
                    <option value='set_to_current'>Nastavit na aktuální stav</option>
                </select>
            </div>
            
            <div class='form-group'>
                <label for='base_value'>Základní hodnota:</label>
                <input type='number' name='base_value' id='base_value' value='100' min='0'>
            </div>
            
            <div class='form-group'>
                <button type='submit' name='update'>Aktualizovat celkovou inventuru</button>
            </div>
        </form>
        
        <h2>Aktivní inventurní relace</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Název</th>
                    <th>Stav</th>
                </tr>
            </thead>
            <tbody>";
                
    foreach ($sessions as $session) {
        echo "<tr>";
        echo "<td>{$session['id']}</td>";
        echo "<td>{$session['name']}</td>";
        echo "<td>{$session['status']}</td>";
        echo "</tr>";
    }
                
    echo "</tbody>
        </table>
    </div>
</body>
</html>";

} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
