<?php
/**
 * Test API pro aktualizaci zadaného množství v celkové inventuře
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test API pro aktualizaci zadaného množství v celkové inventuře</h1>";
    
    // Získání seznamu produktů v inventory_totals
    $stmt = $pdo->query("
        SELECT DISTINCT it.product_id, p.name, sc.units as current_stock
        FROM inventory_totals it
        LEFT JOIN products p ON it.product_id = p.id
        LEFT JOIN stockcurrent sc ON it.product_id = sc.product
        ORDER BY p.name
        LIMIT 10
    ");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "<p>Nebyly nalezeny žádné produkty v inventory_totals.</p>";
    } else {
        echo "<h2>Produkty v inventory_totals</h2>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Název</th><th>Aktuální stav</th><th>Akce</th></tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($product['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($product['name'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($product['current_stock'] ?? '0') . "</td>";
            echo "<td>";
            echo "<form method='post' action='api/update_inventory_quantity.php'>";
            echo "<input type='hidden' name='product_id' value='" . htmlspecialchars($product['product_id']) . "'>";
            echo "<input type='hidden' name='quantity' value='" . htmlspecialchars($product['current_stock'] ?? '0') . "'>";
            echo "<button type='submit' class='btn btn-primary'>Aktualizovat zadané množství</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Získání aktivních inventory_sessions
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active'");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Aktivní inventory_sessions</h2>";
    
    if (empty($sessions)) {
        echo "<p>Nejsou žádné aktivní inventory_sessions.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr>";
        
        // Dynamicky vytvoříme hlavičku tabulky podle dostupných sloupců
        foreach (array_keys($sessions[0]) as $key) {
            echo "<th>" . htmlspecialchars($key) . "</th>";
        }
        
        echo "</tr>";
        
        foreach ($sessions as $session) {
            echo "<tr>";
            
            foreach ($session as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? '') . "</td>";
            }
            
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Získání záznamů v inventory_totals pro aktivní sessions
    if (!empty($sessions)) {
        $sessionIds = array_column($sessions, 'id');
        $placeholders = implode(',', array_fill(0, count($sessionIds), '?'));
        
        $stmt = $pdo->prepare("
            SELECT it.*, p.name as product_name
            FROM inventory_totals it
            LEFT JOIN products p ON it.product_id = p.id
            WHERE it.session_id IN ($placeholders)
            ORDER BY p.name
            LIMIT 10
        ");
        $stmt->execute($sessionIds);
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>Záznamy v inventory_totals pro aktivní sessions</h2>";
        
        if (empty($records)) {
            echo "<p>Nejsou žádné záznamy v inventory_totals pro aktivní sessions.</p>";
        } else {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Session ID</th><th>Product ID</th><th>Produkt</th><th>Zadané množství</th><th>Last Updated</th></tr>";
            
            foreach ($records as $record) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($record['id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['session_id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['product_id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['product_name'] ?? '') . "</td>";
                echo "<td>" . htmlspecialchars($record['total_zadane_mnozstvi']) . "</td>";
                echo "<td>" . htmlspecialchars($record['last_updated'] ?? '') . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
