<?php
/**
 * Skript pro testování funkcionality inventury
 */

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Spuštění session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test funkcionality inventury</h1>";
    echo "<p style='color: green;'>✓ Připojení k databázi bylo úspěšné.</p>";
    
    // Kontrola přihlášení
    if (!isLoggedIn()) {
        echo "<p style='color: red;'>✗ Uživatel není přihl<PERSON>en. Pro testování inventury je nutné být př<PERSON>.</p>";
        echo "<p><a href='test_auth.php'>Přejít na stránku pro přihlášení</a></p>";
    } else {
        echo "<p style='color: green;'>✓ Uživatel je přihlášen jako: " . htmlspecialchars($_SESSION['user']['username']) . " (role: " . htmlspecialchars($_SESSION['user']['role']) . ")</p>";
        
        // Kontrola existence tabulek pro inventuru
        $inventoryTables = [
            'inventory_sessions',
            'inventory_entries',
            'inventory_totals',
            'inventory_stock_changes'
        ];
        
        echo "<h2>Kontrola tabulek pro inventuru</h2>";
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Tabulka</th><th>Existuje</th></tr>";
        
        $missingTables = [];
        
        foreach ($inventoryTables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($table) . "</td>";
            echo "<td>" . ($exists ? "<span style='color: green;'>✓</span>" : "<span style='color: red;'>✗</span>") . "</td>";
            echo "</tr>";
            
            if (!$exists) {
                $missingTables[] = $table;
            }
        }
        
        echo "</table>";
        
        if (!empty($missingTables)) {
            echo "<p style='color: red;'>✗ Některé tabulky pro inventuru chybí. Je nutné je vytvořit.</p>";
            
            // Vytvoření chybějících tabulek
            echo "<h3>Vytvoření chybějících tabulek</h3>";
            
            if (in_array('inventory_sessions', $missingTables)) {
                try {
                    $pdo->exec("
                        CREATE TABLE `inventory_sessions` (
                            `id` INT AUTO_INCREMENT PRIMARY KEY,
                            `start_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `end_time` DATETIME NULL,
                            `status` ENUM('active', 'completed', 'cancelled') NOT NULL DEFAULT 'active',
                            `user_id` INT NOT NULL,
                            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            INDEX `idx_inventory_sessions_status` (`status`),
                            INDEX `idx_inventory_sessions_user` (`user_id`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");
                    
                    echo "<p style='color: green;'>✓ Tabulka inventory_sessions byla vytvořena.</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>✗ Chyba při vytváření tabulky inventory_sessions: " . $e->getMessage() . "</p>";
                }
            }
            
            if (in_array('inventory_entries', $missingTables)) {
                try {
                    $pdo->exec("
                        CREATE TABLE `inventory_entries` (
                            `id` INT AUTO_INCREMENT PRIMARY KEY,
                            `session_id` INT NOT NULL,
                            `product_id` VARCHAR(255) NOT NULL,
                            `ean_code` VARCHAR(255) NOT NULL,
                            `user_id` INT NOT NULL,
                            `zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
                            `status` ENUM('active', 'deleted') NOT NULL DEFAULT 'active',
                            `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                            INDEX `idx_inventory_entries_session` (`session_id`),
                            INDEX `idx_inventory_entries_product` (`product_id`),
                            INDEX `idx_inventory_entries_user` (`user_id`),
                            INDEX `idx_inventory_entries_status` (`status`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");
                    
                    echo "<p style='color: green;'>✓ Tabulka inventory_entries byla vytvořena.</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>✗ Chyba při vytváření tabulky inventory_entries: " . $e->getMessage() . "</p>";
                }
            }
            
            if (in_array('inventory_totals', $missingTables)) {
                try {
                    $pdo->exec("
                        CREATE TABLE `inventory_totals` (
                            `id` INT AUTO_INCREMENT PRIMARY KEY,
                            `session_id` INT NOT NULL,
                            `product_id` VARCHAR(255) NOT NULL,
                            `total_zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
                            `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                            UNIQUE KEY `session_product` (`session_id`, `product_id`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");
                    
                    echo "<p style='color: green;'>✓ Tabulka inventory_totals byla vytvořena.</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>✗ Chyba při vytváření tabulky inventory_totals: " . $e->getMessage() . "</p>";
                }
            }
            
            if (in_array('inventory_stock_changes', $missingTables)) {
                try {
                    $pdo->exec("
                        CREATE TABLE `inventory_stock_changes` (
                            `id` INT AUTO_INCREMENT PRIMARY KEY,
                            `session_id` INT,
                            `product_id` VARCHAR(255) NOT NULL,
                            `initial_stock` DECIMAL(10,3) DEFAULT 0,
                            `stock_changes_during_inventory` DECIMAL(10,3) DEFAULT 0,
                            `units` DECIMAL(10,3),
                            `change_type` ENUM('sale', 'update', 'delete', 'restock') DEFAULT NULL,
                            `change_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            UNIQUE KEY `session_product` (`session_id`, `product_id`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ");
                    
                    echo "<p style='color: green;'>✓ Tabulka inventory_stock_changes byla vytvořena.</p>";
                } catch (PDOException $e) {
                    echo "<p style='color: red;'>✗ Chyba při vytváření tabulky inventory_stock_changes: " . $e->getMessage() . "</p>";
                }
            }
        } else {
            echo "<p style='color: green;'>✓ Všechny tabulky pro inventuru existují.</p>";
        }
        
        // Test vytvoření nové inventurní relace
        echo "<h2>Test vytvoření nové inventurní relace</h2>";
        
        if (isset($_POST['create_session'])) {
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_sessions (user_id)
                    VALUES (:user_id)
                ");
                
                $stmt->execute(['user_id' => $_SESSION['user']['id']]);
                $sessionId = $pdo->lastInsertId();
                
                echo "<p style='color: green;'>✓ Nová inventurní relace byla vytvořena. ID: " . $sessionId . "</p>";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Chyba při vytváření nové inventurní relace: " . $e->getMessage() . "</p>";
            }
        }
        
        // Formulář pro vytvoření nové inventurní relace
        echo "<form method='post'>";
        echo "<p><input type='submit' name='create_session' value='Vytvořit novou inventurní relaci'></p>";
        echo "</form>";
        
        // Získání seznamu aktivních inventurních relací
        $stmt = $pdo->query("
            SELECT 
                s.id, 
                s.start_time, 
                s.status, 
                u.username, 
                COUNT(e.id) AS entry_count
            FROM 
                inventory_sessions s
            JOIN 
                inventory_users u ON s.user_id = u.id
            LEFT JOIN 
                inventory_entries e ON s.id = e.session_id AND e.status = 'active'
            WHERE 
                s.status = 'active'
            GROUP BY 
                s.id, s.start_time, s.status, u.username
            ORDER BY 
                s.start_time DESC
        ");
        
        $activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>Aktivní inventurní relace</h2>";
        
        if (empty($activeSessions)) {
            echo "<p>Žádné aktivní inventurní relace nebyly nalezeny.</p>";
        } else {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Čas zahájení</th><th>Stav</th><th>Uživatel</th><th>Počet záznamů</th><th>Akce</th></tr>";
            
            foreach ($activeSessions as $session) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($session['id']) . "</td>";
                echo "<td>" . htmlspecialchars($session['start_time']) . "</td>";
                echo "<td>" . htmlspecialchars($session['status']) . "</td>";
                echo "<td>" . htmlspecialchars($session['username']) . "</td>";
                echo "<td>" . htmlspecialchars($session['entry_count']) . "</td>";
                echo "<td>";
                echo "<form method='post' style='display: inline;'>";
                echo "<input type='hidden' name='session_id' value='" . htmlspecialchars($session['id']) . "'>";
                echo "<input type='submit' name='view_entries' value='Zobrazit záznamy'>";
                echo "</form>";
                echo "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
        
        // Zobrazení záznamů pro vybranou relaci
        if (isset($_POST['view_entries']) || isset($_POST['add_entry'])) {
            $sessionId = $_POST['session_id'] ?? null;
            
            if ($sessionId) {
                // Získání informací o relaci
                $stmt = $pdo->prepare("
                    SELECT 
                        s.id, 
                        s.start_time, 
                        s.status, 
                        u.username
                    FROM 
                        inventory_sessions s
                    JOIN 
                        inventory_users u ON s.user_id = u.id
                    WHERE 
                        s.id = :session_id
                ");
                
                $stmt->execute(['session_id' => $sessionId]);
                $session = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($session) {
                    echo "<h2>Záznamy pro inventurní relaci #" . htmlspecialchars($session['id']) . "</h2>";
                    echo "<p>Čas zahájení: " . htmlspecialchars($session['start_time']) . "</p>";
                    echo "<p>Stav: " . htmlspecialchars($session['status']) . "</p>";
                    echo "<p>Uživatel: " . htmlspecialchars($session['username']) . "</p>";
                    
                    // Formulář pro přidání nového záznamu
                    echo "<h3>Přidat nový záznam</h3>";
                    echo "<form method='post'>";
                    echo "<input type='hidden' name='session_id' value='" . htmlspecialchars($session['id']) . "'>";
                    echo "<p><label>EAN kód: <input type='text' name='ean_code' required></label></p>";
                    echo "<p><label>Zadané množství: <input type='number' name='zadane_mnozstvi' step='0.001' required></label></p>";
                    echo "<p><input type='submit' name='add_entry' value='Přidat záznam'></p>";
                    echo "</form>";
                    
                    // Zpracování přidání nového záznamu
                    if (isset($_POST['add_entry']) && isset($_POST['ean_code']) && isset($_POST['zadane_mnozstvi'])) {
                        $eanCode = $_POST['ean_code'];
                        $zadaneMnozstvi = $_POST['zadane_mnozstvi'];
                        
                        // Získání informací o produktu
                        $stmt = $pdo->prepare("
                            SELECT 
                                id, 
                                name,
                                code
                            FROM 
                                products
                            WHERE 
                                code = :code
                        ");
                        
                        $stmt->execute(['code' => $eanCode]);
                        $product = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($product) {
                            try {
                                // Přidání záznamu do inventory_entries
                                $stmt = $pdo->prepare("
                                    INSERT INTO inventory_entries (
                                        session_id, 
                                        product_id, 
                                        ean_code, 
                                        user_id, 
                                        zadane_mnozstvi
                                    ) VALUES (
                                        :session_id, 
                                        :product_id, 
                                        :ean_code, 
                                        :user_id, 
                                        :zadane_mnozstvi
                                    )
                                ");
                                
                                $stmt->execute([
                                    'session_id' => $sessionId,
                                    'product_id' => $product['id'],
                                    'ean_code' => $eanCode,
                                    'user_id' => $_SESSION['user']['id'],
                                    'zadane_mnozstvi' => $zadaneMnozstvi
                                ]);
                                
                                echo "<p style='color: green;'>✓ Záznam byl přidán.</p>";
                                
                                // Aktualizace celkového zadaného množství v inventory_totals
                                $stmt = $pdo->prepare("
                                    INSERT INTO inventory_totals (
                                        session_id, 
                                        product_id, 
                                        total_zadane_mnozstvi
                                    ) VALUES (
                                        :session_id, 
                                        :product_id, 
                                        :zadane_mnozstvi
                                    ) ON DUPLICATE KEY UPDATE
                                        total_zadane_mnozstvi = total_zadane_mnozstvi + :zadane_mnozstvi
                                ");
                                
                                $stmt->execute([
                                    'session_id' => $sessionId,
                                    'product_id' => $product['id'],
                                    'zadane_mnozstvi' => $zadaneMnozstvi
                                ]);
                                
                                echo "<p style='color: green;'>✓ Celkové zadané množství bylo aktualizováno.</p>";
                            } catch (PDOException $e) {
                                echo "<p style='color: red;'>✗ Chyba při přidávání záznamu: " . $e->getMessage() . "</p>";
                            }
                        } else {
                            echo "<p style='color: red;'>✗ Produkt s EAN kódem " . htmlspecialchars($eanCode) . " nebyl nalezen.</p>";
                        }
                    }
                    
                    // Získání seznamu záznamů pro relaci
                    $stmt = $pdo->prepare("
                        SELECT 
                            e.id,
                            e.product_id,
                            e.ean_code,
                            p.name AS product_name,
                            e.zadane_mnozstvi,
                            COALESCE(s.units, 0) AS current_stock,
                            (e.zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
                            u.username,
                            e.last_updated
                        FROM 
                            inventory_entries e
                        JOIN 
                            products p ON e.product_id = p.id
                        LEFT JOIN 
                            stockcurrent s ON p.id = s.product
                        JOIN 
                            inventory_users u ON e.user_id = u.id
                        WHERE 
                            e.session_id = :session_id
                            AND e.status = 'active'
                        ORDER BY 
                            e.last_updated DESC
                    ");
                    
                    $stmt->execute(['session_id' => $sessionId]);
                    $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (empty($entries)) {
                        echo "<p>Žádné záznamy nebyly nalezeny.</p>";
                    } else {
                        echo "<h3>Seznam záznamů</h3>";
                        echo "<table border='1' cellpadding='5' cellspacing='0'>";
                        echo "<tr><th>ID</th><th>EAN kód</th><th>Název produktu</th><th>Zadané množství</th><th>Aktuální stav</th><th>Rozdíl</th><th>Uživatel</th><th>Poslední aktualizace</th></tr>";
                        
                        foreach ($entries as $entry) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($entry['id']) . "</td>";
                            echo "<td>" . htmlspecialchars($entry['ean_code']) . "</td>";
                            echo "<td>" . htmlspecialchars($entry['product_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($entry['zadane_mnozstvi']) . "</td>";
                            echo "<td>" . htmlspecialchars($entry['current_stock']) . "</td>";
                            echo "<td>" . htmlspecialchars($entry['difference']) . "</td>";
                            echo "<td>" . htmlspecialchars($entry['username']) . "</td>";
                            echo "<td>" . htmlspecialchars($entry['last_updated']) . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                    }
                } else {
                    echo "<p style='color: red;'>✗ Inventurní relace nebyla nalezena.</p>";
                }
            } else {
                echo "<p style='color: red;'>✗ Nebylo zadáno ID inventurní relace.</p>";
            }
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
