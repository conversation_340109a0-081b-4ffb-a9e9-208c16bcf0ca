<?php
/**
 * Aktualizace zadaného množství v celkové inventuře při prodeji i naskladnění
 * 
 * Tento skript upraví trigger, který aktualizuje inventory_totals při změně v stockcurrent,
 * aby reagoval na oba typy změn - snížení při prodeji a zvýšení při naskladnění.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    $results = [];
    
    echo "<h1>Aktualizace zadaného množství v celkové inventuře při prodeji i naskladnění</h1>";
    
    // Odstranění existujícího triggeru
    try {
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_totals_after_stockcurrent_update`");
        $results['drop_trigger'] = 'Trigger odstraněn';
    } catch (PDOException $e) {
        $results['drop_trigger'] = 'Chyba při odstraňování triggeru: ' . $e->getMessage();
    }
    
    // Vytvoření nového triggeru, který bude aktualizovat inventory_totals při změně v stockcurrent
    try {
        $pdo->exec("
            CREATE TRIGGER `update_inventory_totals_after_stockcurrent_update`
            AFTER UPDATE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
        ");
        $results['create_trigger'] = 'Trigger byl vytvořen';
    } catch (PDOException $e) {
        $results['create_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Výpis výsledků
    echo "<h2>Výsledky</h2>";
    echo "<pre>";
    print_r($results);
    echo "</pre>";
    
    echo "<p>Trigger byl upraven tak, aby aktualizoval zadané množství v celkové inventuře při prodeji i naskladnění:</p>";
    echo "<ul>";
    echo "<li>Při prodeji (když se units v stockcurrent sníží) se zadané množství v celkové inventuře také sníží</li>";
    echo "<li>Při naskladnění (když se units v stockcurrent zvýší) se zadané množství v celkové inventuře také zvýší</li>";
    echo "</ul>";
    echo "<p><a href='debug_stockcurrent_changes.php'>Zkontrolovat logy</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
