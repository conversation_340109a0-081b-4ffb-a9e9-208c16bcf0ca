<?php
/**
 * Test připojen<PERSON> k datab<PERSON>zi
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Test připojení k datab<PERSON>zi</h1>";

// Test 1: Načtení konfigurace
echo "<h2>1. Test konfigurace</h2>";
try {
    $config = require __DIR__ . '/config/database.php';
    echo "<p style='color: green;'>✓ Konfigurace načtena</p>";
    echo "<pre>" . print_r($config, true) . "</pre>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba při načítání konfigurace: " . $e->getMessage() . "</p>";
    exit;
}

// Test 2: Základní připojení k MySQL
echo "<h2>2. Test základního připojení k MySQL</h2>";
try {
    $dsn = "mysql:host={$config['host']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "<p style='color: green;'>✓ Připojení k MySQL serveru úspěšné</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Chyba připojení k MySQL: " . $e->getMessage() . "</p>";
    echo "<p><strong>Možné řešení:</strong></p>";
    echo "<ul>";
    echo "<li>Zkontrolujte, zda běží MySQL server</li>";
    echo "<li>Zkontrolujte uživatelské jméno a heslo</li>";
    echo "<li>Zkontrolujte, zda uživatel 'michal' existuje a má správná oprávnění</li>";
    echo "</ul>";
    
    // Zkusíme jiné kombinace
    echo "<h3>Testování různých kombinací:</h3>";
    
    $testConfigs = [
        ['username' => 'root', 'password' => ''],
        ['username' => 'root', 'password' => 'admin'],
        ['username' => 'root', 'password' => 'root'],
        ['username' => 'michal', 'password' => ''],
    ];
    
    foreach ($testConfigs as $testConfig) {
        try {
            $testPdo = new PDO($dsn, $testConfig['username'], $testConfig['password'], $config['options']);
            echo "<p style='color: green;'>✓ Funguje s: {$testConfig['username']} / {$testConfig['password']}</p>";
            $workingConfig = $testConfig;
            break;
        } catch (PDOException $e) {
            echo "<p style='color: orange;'>✗ Nefunguje s: {$testConfig['username']} / {$testConfig['password']}</p>";
        }
    }
    
    if (isset($workingConfig)) {
        echo "<h3>Aktualizace konfigurace:</h3>";
        $newConfig = $config;
        $newConfig['username'] = $workingConfig['username'];
        $newConfig['password'] = $workingConfig['password'];
        
        $configContent = "<?php\n/**\n * Database Configuration\n * \n * This file contains the configuration for connecting to the UniCentaOPOS database.\n */\n\nreturn " . var_export($newConfig, true) . ";\n";
        
        if (file_put_contents(__DIR__ . '/config/database.php', $configContent)) {
            echo "<p style='color: green;'>✓ Konfigurace aktualizována</p>";
            $config = $newConfig;
            $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        }
    } else {
        exit;
    }
}

// Test 3: Připojení k databázi
echo "<h2>3. Test připojení k databázi '{$config['dbname']}'</h2>";
try {
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "<p style='color: green;'>✓ Připojení k databázi '{$config['dbname']}' úspěšné</p>";
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Chyba připojení k databázi '{$config['dbname']}': " . $e->getMessage() . "</p>";
    
    // Zkusíme vytvořit databázi
    echo "<h3>Pokus o vytvoření databáze:</h3>";
    try {
        $dsn = "mysql:host={$config['host']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `{$config['dbname']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✓ Databáze '{$config['dbname']}' byla vytvořena</p>";
        
        // Znovu se připojíme k databázi
        $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
        $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
        echo "<p style='color: green;'>✓ Připojení k nové databázi úspěšné</p>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Nepodařilo se vytvořit databázi: " . $e->getMessage() . "</p>";
        exit;
    }
}

// Test 4: Kontrola tabulek
echo "<h2>4. Test tabulek</h2>";
try {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<p>Počet tabulek v databázi: " . count($tables) . "</p>";
    
    if (count($tables) > 0) {
        echo "<p>Tabulky:</p><ul>";
        foreach ($tables as $table) {
            echo "<li>$table</li>";
        }
        echo "</ul>";
    }
    
    // Kontrola tabulky inventory_users
    if (in_array('inventory_users', $tables)) {
        echo "<p style='color: green;'>✓ Tabulka 'inventory_users' existuje</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM inventory_users");
        $count = $stmt->fetchColumn();
        echo "<p>Počet uživatelů: $count</p>";
        
        if ($count > 0) {
            $stmt = $pdo->query("SELECT username, role FROM inventory_users LIMIT 5");
            $users = $stmt->fetchAll();
            echo "<p>Uživatelé:</p><ul>";
            foreach ($users as $user) {
                echo "<li>{$user['username']} ({$user['role']})</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p style='color: orange;'>⚠ Tabulka 'inventory_users' neexistuje</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Chyba při kontrole tabulek: " . $e->getMessage() . "</p>";
}

echo "<h2>5. Závěr</h2>";
echo "<p style='color: green;'>✓ Test připojení k databázi dokončen</p>";
echo "<p><a href='create_admin.php'>Vytvořit admin uživatele</a></p>";
echo "<p><a href='simple_login.html'>Zkusit přihlášení</a></p>";
?>
