<?php
/**
 * Synchronizace inventury bez použití triggerů
 *
 * Tento skript synchronizuje zadané množství v celkové inventuře s aktuálním stavem prodejů.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();

    echo "<h1>Synchronizace inventury bez použití triggerů</h1>";

    // Kontrola, zda existuje tabulka ticketlines_changes
    $stmt = $pdo->query("SHOW TABLES LIKE 'ticketlines_changes'");
    $tableExists = $stmt->rowCount() > 0;

    if (!$tableExists) {
        // Vytvoření tabulky ticketlines_changes
        $pdo->exec("
            CREATE TABLE `ticketlines_changes` (
              `id` INT AUTO_INCREMENT PRIMARY KEY,
              `last_checked` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");

        // Vložení prvního záznamu
        $pdo->exec("INSERT INTO ticketlines_changes (last_checked) VALUES (NOW())");

        echo "<p>Tabulka ticketlines_changes byla vytvořena.</p>";
    }

    // Získání času poslední kontroly
    $stmt = $pdo->query("SELECT last_checked FROM ticketlines_changes ORDER BY id DESC LIMIT 1");
    $lastChecked = $stmt->fetchColumn();

    echo "<p>Poslední kontrola: " . htmlspecialchars($lastChecked) . "</p>";

    // Získání nových prodejů od poslední kontroly
    $stmt = $pdo->prepare("
        SELECT t.id, t.tickettype, tl.product, tl.units, tl.ticket, tl.line
        FROM tickets t
        JOIN ticketlines tl ON t.id = tl.ticket
        WHERE t.tickettype = 0 -- Prodejní lístky
    ");
    $stmt->execute();
    $newSales = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Nové prodeje od poslední kontroly</h2>";

    if (empty($newSales)) {
        echo "<p>Nebyly nalezeny žádné nové prodeje od poslední kontroly.</p>";
    } else {
        echo "<p>Počet nových prodejů: " . count($newSales) . "</p>";

        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Ticket ID</th><th>Product</th><th>Units</th></tr>";

        foreach ($newSales as $sale) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($sale['ticket']) . "</td>";
            echo "<td>" . htmlspecialchars($sale['product']) . "</td>";
            echo "<td>" . htmlspecialchars($sale['units']) . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    }

    // Aktualizace zadaného množství v celkové inventuře
    if (!empty($newSales)) {
        echo "<h2>Aktualizace zadaného množství v celkové inventuře</h2>";

        $updatedProducts = [];

        foreach ($newSales as $sale) {
            $productId = $sale['product'];
            $units = $sale['units'];

            // Aktualizace zadaného množství v celkové inventuře
            $stmt = $pdo->prepare("
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - ?,
                    last_updated = NOW()
                WHERE product_id = ?
                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
            ");
            $stmt->execute([$units, $productId]);

            // Výpis SQL dotazu a parametrů pro ladění
            echo "<p>SQL dotaz: UPDATE inventory_totals SET total_zadane_mnozstvi = total_zadane_mnozstvi - " . htmlspecialchars($units) . ", last_updated = NOW() WHERE product_id = '" . htmlspecialchars($productId) . "' AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')</p>";
            echo "<p>Počet aktualizovaných řádků: " . $stmt->rowCount() . "</p>";
            $updatedCount = $stmt->rowCount();

            if ($updatedCount > 0) {
                if (!isset($updatedProducts[$productId])) {
                    $updatedProducts[$productId] = 0;
                }
                $updatedProducts[$productId] += $units;
            }
        }

        if (empty($updatedProducts)) {
            echo "<p>Nebyly aktualizovány žádné záznamy v celkové inventuře.</p>";
        } else {
            echo "<p>Byly aktualizovány záznamy v celkové inventuře pro následující produkty:</p>";

            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>Product ID</th><th>Total Units</th></tr>";

            foreach ($updatedProducts as $productId => $units) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($productId) . "</td>";
                echo "<td>" . htmlspecialchars($units) . "</td>";
                echo "</tr>";
            }

            echo "</table>";
        }
    }

    // Aktualizace času poslední kontroly
    $pdo->exec("UPDATE ticketlines_changes SET last_checked = NOW() ORDER BY id DESC LIMIT 1");

    echo "<p>Čas poslední kontroly byl aktualizován.</p>";

    // Získání obsahu tabulky inventory_totals
    $stmt = $pdo->query("
        SELECT it.*, CONCAT('Inventura #', is2.id) as session_name, is2.status as session_status
        FROM inventory_totals it
        JOIN inventory_sessions is2 ON it.session_id = is2.id
        WHERE is2.status = 'active'
        ORDER BY it.session_id, it.product_id
        LIMIT 100
    ");
    $totals = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Záznamy v inventory_totals pro aktivní sessions</h2>";

    if (empty($totals)) {
        echo "<p>Nejsou žádné záznamy v inventory_totals pro aktivní sessions.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Session ID</th><th>Session Name</th><th>Product ID</th><th>Total zadané množství</th><th>Last Updated</th><th>Session Status</th></tr>";

        foreach ($totals as $total) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($total['id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['session_id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['session_name']) . "</td>";
            echo "<td>" . htmlspecialchars($total['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['total_zadane_mnozstvi']) . "</td>";
            echo "<td>" . htmlspecialchars($total['last_updated'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($total['session_status']) . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    }

    // Vytvoření skriptu pro automatickou synchronizaci
    $cronScript = "<?php
// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    \$pdo = getDbConnection();

    // Získání času poslední kontroly
    \$stmt = \$pdo->query(\"SELECT last_checked FROM ticketlines_changes ORDER BY id DESC LIMIT 1\");
    \$lastChecked = \$stmt->fetchColumn();

    // Získání nových prodejů od poslední kontroly
    \$stmt = \$pdo->prepare(\"
        SELECT t.id, t.tickettype, tl.product, tl.units, tl.ticket, tl.line
        FROM tickets t
        JOIN ticketlines tl ON t.id = tl.ticket
        WHERE t.tickettype = 0 -- Prodejní lístky
    \");
    \$stmt->execute();
    \$newSales = \$stmt->fetchAll(PDO::FETCH_ASSOC);

    // Aktualizace zadaného množství v celkové inventuře
    if (!empty(\$newSales)) {
        foreach (\$newSales as \$sale) {
            \$productId = \$sale['product'];
            \$units = \$sale['units'];

            // Aktualizace zadaného množství v celkové inventuře
            \$stmt = \$pdo->prepare(\"
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - ?,
                    last_updated = NOW()
                WHERE product_id = ?
                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
            \");
            \$stmt->execute([\$units, \$productId]);
        }
    }

    // Aktualizace času poslední kontroly
    \$pdo->exec(\"UPDATE ticketlines_changes SET last_checked = NOW() ORDER BY id DESC LIMIT 1\");

    echo \"Synchronizace inventury byla úspěšně dokončena.\";
} catch (PDOException \$e) {
    echo \"Chyba při synchronizaci inventury: \" . \$e->getMessage();
}
?>";

    // Uložení skriptu pro automatickou synchronizaci
    file_put_contents(__DIR__ . '/cron_sync_inventory.php', $cronScript);

    echo "<h2>Automatická synchronizace</h2>";
    echo "<p>Byl vytvořen skript pro automatickou synchronizaci: cron_sync_inventory.php</p>";
    echo "<p>Tento skript můžete spouštět pravidelně (např. každou minutu) pomocí cronu nebo plánovače úloh.</p>";
    echo "<p>Příklad nastavení cronu (každou minutu):</p>";
    echo "<pre>* * * * * php " . __DIR__ . "/cron_sync_inventory.php</pre>";

    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";

} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
