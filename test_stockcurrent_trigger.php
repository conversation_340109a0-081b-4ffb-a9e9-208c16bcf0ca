<?php
/**
 * Test triggeru na tabulce stockcurrent
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test triggeru na tabulce stockcurrent</h1>";
    
    // KROK 1: Kontrola triggeru na stockcurrent
    logMessage("🔍 KROK 1: Kontroluji trigger na tabulce stockcurrent...");
    
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_TIMING, ACTION_STATEMENT
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND EVENT_OBJECT_TABLE = 'stockcurrent'
    ");
    $stockcurrent_triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($stockcurrent_triggers)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ ŽÁDNÝ TRIGGER NA STOCKCURRENT</h3>";
        echo "<p style='color: #721c24; margin: 0;'>Nebyl nalezen žádný trigger na tabulce stockcurrent. To je důvod, proč se zadané množství neaktualizuje při prodeji.</p>";
        echo "</div>";
        
        echo "<p><a href='create_simple_trigger.php'>Vytvořit trigger</a></p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    echo "<h3>Nalezené triggery na stockcurrent:</h3>";
    foreach ($stockcurrent_triggers as $trigger) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>" . htmlspecialchars($trigger['TRIGGER_NAME']) . "</h4>";
        echo "<p><strong>Událost:</strong> " . $trigger['ACTION_TIMING'] . " " . $trigger['EVENT_MANIPULATION'] . "</p>";
        
        // Kontrola, zda trigger obsahuje aktualizaci inventory_totals
        if (stripos($trigger['ACTION_STATEMENT'], 'inventory_totals') !== false) {
            logMessage("✓ Trigger obsahuje aktualizaci inventory_totals");
        } else {
            logMessage("❌ Trigger NEOBSAHUJE aktualizaci inventory_totals", true);
        }
        
        echo "<details>";
        echo "<summary>Zobrazit kód triggeru</summary>";
        echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; border-radius: 3px; overflow-x: auto;'>" . htmlspecialchars($trigger['ACTION_STATEMENT']) . "</pre>";
        echo "</details>";
        echo "</div>";
    }
    
    // Zpracování testu triggeru
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_stockcurrent_trigger'])) {
        $productId = $_POST['product_id'];
        $changeAmount = floatval($_POST['change_amount']);
        
        logMessage("🔄 Testuji trigger na stockcurrent...");
        
        // Najdeme aktivní inventurní relaci
        $stmt = $pdo->query("
            SELECT id, title
            FROM inventory_sessions
            WHERE status = 'active'
            LIMIT 1
        ");
        $session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$session) {
            logMessage("❌ Nebyla nalezena aktivní inventurní relace", true);
        } else {
            $sessionId = $session['id'];
            logMessage("✓ Aktivní inventurní relace: " . $session['title']);
            
            // Získáme aktuální stav PŘED testem
            $stmt = $pdo->prepare("
                SELECT sc.units as stock_units, it.total_zadane_mnozstvi, it.last_updated
                FROM stockcurrent sc
                LEFT JOIN inventory_totals it ON sc.product = it.product_id AND it.session_id = ?
                WHERE sc.product = ?
            ");
            $stmt->execute([$sessionId, $productId]);
            $beforeTest = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$beforeTest) {
                logMessage("❌ Produkt nebyl nalezen v stockcurrent", true);
            } else {
                $stockBefore = $beforeTest['stock_units'];
                $inventoryBefore = $beforeTest['total_zadane_mnozstvi'] ?? 0;
                $lastUpdatedBefore = $beforeTest['last_updated'] ?? 'N/A';
                
                logMessage("✓ Množství na skladě PŘED testem: $stockBefore");
                logMessage("✓ Zadané množství PŘED testem: $inventoryBefore");
                logMessage("✓ Poslední aktualizace PŘED testem: $lastUpdatedBefore");
                
                // Provedeme změnu v stockcurrent (simulace prodeje/naskladnění)
                $newStockAmount = $stockBefore + $changeAmount;
                
                logMessage("🔄 Měním množství na skladě z $stockBefore na $newStockAmount (změna: $changeAmount)...");
                
                $stmt = $pdo->prepare("
                    UPDATE stockcurrent
                    SET units = ?
                    WHERE product = ?
                ");
                $stmt->execute([$newStockAmount, $productId]);
                
                logMessage("✓ Množství na skladě bylo změněno");
                
                // Počkáme chvilku na zpracování triggeru
                sleep(1);
                
                // Zkontrolujeme stav PO testu
                $stmt = $pdo->prepare("
                    SELECT sc.units as stock_units, it.total_zadane_mnozstvi, it.last_updated
                    FROM stockcurrent sc
                    LEFT JOIN inventory_totals it ON sc.product = it.product_id AND it.session_id = ?
                    WHERE sc.product = ?
                ");
                $stmt->execute([$sessionId, $productId]);
                $afterTest = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $stockAfter = $afterTest['stock_units'];
                $inventoryAfter = $afterTest['total_zadane_mnozstvi'] ?? 0;
                $lastUpdatedAfter = $afterTest['last_updated'] ?? 'N/A';
                
                logMessage("✓ Množství na skladě PO testu: $stockAfter");
                logMessage("✓ Zadané množství PO testu: $inventoryAfter");
                logMessage("✓ Poslední aktualizace PO testu: $lastUpdatedAfter");
                
                // Vyhodnocení testu
                $stockChange = $stockAfter - $stockBefore;
                $inventoryChange = $inventoryAfter - $inventoryBefore;
                $expectedInventoryChange = -$stockChange; // Opačný směr
                
                logMessage("📊 Změna množství na skladě: $stockChange");
                logMessage("📊 Změna zadaného množství: $inventoryChange");
                logMessage("📊 Očekávaná změna zadaného množství: $expectedInventoryChange");
                
                if (abs($inventoryChange - $expectedInventoryChange) < 0.001) {
                    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>🎉 TRIGGER FUNGUJE SPRÁVNĚ!</h3>";
                    echo "<p style='color: #155724; margin: 0;'>Trigger na stockcurrent správně aktualizuje zadané množství v inventory_totals.</p>";
                    echo "<p style='color: #155724; margin: 10px 0 0 0;'><strong>Výsledek:</strong></p>";
                    echo "<ul style='color: #155724; margin: 5px 0 0 20px;'>";
                    echo "<li>Změna na skladě: $stockChange</li>";
                    echo "<li>Změna zadaného množství: $inventoryChange</li>";
                    echo "<li>Poslední aktualizace: $lastUpdatedAfter</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                    echo "<div style='background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3 style='color: #0c5460; margin: 0 0 10px 0;'>🔍 Proč se zadané množství neaktualizuje při prodeji v UniCenta?</h3>";
                    echo "<p style='color: #0c5460; margin: 0;'>Trigger funguje, ale možné příčiny problému:</p>";
                    echo "<ul style='color: #0c5460; margin: 10px 0 0 20px;'>";
                    echo "<li><strong>UniCenta neaktualizuje stockcurrent při prodeji</strong> - možná používá jinou tabulku</li>";
                    echo "<li><strong>Chybí záznamy v inventory_totals</strong> - trigger nemá co aktualizovat</li>";
                    echo "<li><strong>UniCenta používá jiný mechanismus</strong> - např. stockdiary nebo ticketlines</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                } else {
                    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ TRIGGER NEFUNGUJE SPRÁVNĚ</h3>";
                    echo "<p style='color: #721c24; margin: 0;'>Trigger na stockcurrent neaktualizuje zadané množství správně.</p>";
                    echo "<p style='color: #721c24; margin: 10px 0 0 0;'><strong>Problém:</strong></p>";
                    echo "<ul style='color: #721c24; margin: 5px 0 0 20px;'>";
                    echo "<li>Očekávaná změna: $expectedInventoryChange</li>";
                    echo "<li>Skutečná změna: $inventoryChange</li>";
                    echo "<li>Rozdíl: " . ($inventoryChange - $expectedInventoryChange) . "</li>";
                    echo "</ul>";
                    echo "</div>";
                }
                
                // Vrátíme původní stav
                $stmt = $pdo->prepare("
                    UPDATE stockcurrent
                    SET units = ?
                    WHERE product = ?
                ");
                $stmt->execute([$stockBefore, $productId]);
                
                logMessage("✓ Vrácen původní stav množství na skladě");
            }
        }
    }
    
    // Formulář pro testování
    echo "<h2>Test triggeru na stockcurrent</h2>";
    
    // Získání seznamu produktů
    $stmt = $pdo->query("
        SELECT DISTINCT sc.product, p.name, sc.units, it.total_zadane_mnozstvi, s.title
        FROM stockcurrent sc
        JOIN products p ON sc.product = p.id
        LEFT JOIN inventory_totals it ON sc.product = it.product_id
        LEFT JOIN inventory_sessions s ON it.session_id = s.id AND s.status = 'active'
        WHERE sc.units > 0
        ORDER BY p.name
        LIMIT 10
    ");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #856404; margin: 0 0 10px 0;'>⚠ Žádné produkty k testování</h3>";
        echo "<p style='color: #856404; margin: 0;'>Nejsou žádné produkty se zásobami pro testování.</p>";
        echo "</div>";
    } else {
        echo "<form method='POST'>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>Vybrat</th><th>Produkt</th><th>Množství na skladě</th><th>Zadané množství</th><th>Inventurní relace</th></tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td><input type='radio' name='product_id' value='" . htmlspecialchars($product['product']) . "' required></td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['units']) . "</td>";
            echo "<td>" . htmlspecialchars($product['total_zadane_mnozstvi'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($product['title'] ?? 'Žádná aktivní') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<p>";
        echo "<label>Změna množství na skladě: <input type='number' name='change_amount' value='-1' step='0.001' required></label>";
        echo "<br><small>Záporné číslo = snížení (simulace prodeje), kladné = zvýšení (simulace naskladnění)</small>";
        echo "</p>";
        
        echo "<p>";
        echo "<button type='submit' name='test_stockcurrent_trigger' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Test triggeru na stockcurrent</button>";
        echo "</p>";
        
        echo "</form>";
    }
    
    echo "<p><a href='simple_trigger_test.php'>Zpět na testy</a></p>";
    echo "<p><a href='create_simple_trigger.php'>Vytvořit/opravit triggery</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
