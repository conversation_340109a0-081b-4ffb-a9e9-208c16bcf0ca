<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test jednoduchého autentifikačního API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .result-area {
            margin-top: 10px;
            padding: 10px;
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 3px;
            min-height: 100px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test jednoduchého autentifikačního API</h1>
        
        <div class="test-section">
            <h2>🧪 Test připojení a stavu</h2>
            <button class="btn" onclick="testAPI('test')">Test stavu API</button>
            <div id="test-result" class="result-area">Klikněte na tlačítko pro test...</div>
        </div>

        <div class="test-section">
            <h2>🔑 Test přihlášení</h2>
            <div class="form-group">
                <label for="username">Uživatelské jméno:</label>
                <input type="text" id="username" value="admin">
            </div>
            <div class="form-group">
                <label for="password">Heslo:</label>
                <input type="password" id="password" value="admin123">
            </div>
            <button class="btn btn-success" onclick="testLogin()">Přihlásit se</button>
            <button class="btn btn-danger" onclick="testLogout()">Odhlásit se</button>
            <button class="btn" onclick="testAPI('check')">Kontrola stavu přihlášení</button>
            <div id="auth-result" class="result-area">Zadejte přihlašovací údaje a klikněte na tlačítko...</div>
        </div>

        <div class="test-section">
            <h2>⚡ Rychlé testy</h2>
            <button class="btn" onclick="quickTest('admin', 'admin123')">Test admin/admin123</button>
            <button class="btn" onclick="quickTest('admin', 'admin')">Test admin/admin</button>
            <button class="btn" onclick="quickTest('testuser', 'test123')">Test testuser/test123</button>
            <button class="btn" onclick="quickTest('manager', 'manager123')">Test manager/manager123</button>
            <div id="quick-result" class="result-area">Klikněte na tlačítko pro rychlý test...</div>
        </div>

        <div class="test-section">
            <h2>🔗 Porovnání API</h2>
            <button class="btn" onclick="compareAPIs()">Porovnat simple_auth vs auth</button>
            <div id="compare-result" class="result-area">Klikněte na tlačítko pro porovnání...</div>
        </div>

        <div class="test-section">
            <h2>🔗 Navigace</h2>
            <a href="debug_login_issue.php" class="btn btn-warning">Debug přihlašování</a>
            <a href="test_login_direct.php" class="btn btn-warning">Přímý test</a>
            <a href="index.html" class="btn">Hlavní stránka</a>
        </div>
    </div>

    <script>
        async function testAPI(action) {
            const resultElement = document.getElementById('test-result');
            resultElement.className = 'result-area loading';
            resultElement.textContent = 'Načítání...';

            try {
                const response = await fetch(`api/simple_auth.php?action=${action}`);
                const text = await response.text();
                
                resultElement.className = response.ok ? 'result-area success' : 'result-area error';
                
                try {
                    const json = JSON.parse(text);
                    resultElement.textContent = JSON.stringify(json, null, 2);
                } catch (e) {
                    resultElement.textContent = text;
                }
                
                resultElement.textContent += '\n\n--- Response Info ---\n';
                resultElement.textContent += `Status: ${response.status} ${response.statusText}\n`;
                resultElement.textContent += `URL: ${response.url}`;
                
            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba: ${error.message}`;
            }
        }

        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultElement = document.getElementById('auth-result');
            
            if (!username || !password) {
                alert('Zadejte uživatelské jméno a heslo');
                return;
            }
            
            resultElement.className = 'result-area loading';
            resultElement.textContent = 'Přihlašování...';

            try {
                const response = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const text = await response.text();
                
                resultElement.className = response.ok ? 'result-area success' : 'result-area error';
                
                try {
                    const json = JSON.parse(text);
                    resultElement.textContent = JSON.stringify(json, null, 2);
                } catch (e) {
                    resultElement.textContent = text;
                }
                
                resultElement.textContent += '\n\n--- Response Info ---\n';
                resultElement.textContent += `Status: ${response.status} ${response.statusText}`;
                
            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba: ${error.message}`;
            }
        }

        async function testLogout() {
            const resultElement = document.getElementById('auth-result');
            resultElement.className = 'result-area loading';
            resultElement.textContent = 'Odhlašování...';

            try {
                const response = await fetch('api/simple_auth.php?action=logout', {
                    method: 'POST'
                });
                
                const text = await response.text();
                
                resultElement.className = response.ok ? 'result-area success' : 'result-area error';
                
                try {
                    const json = JSON.parse(text);
                    resultElement.textContent = JSON.stringify(json, null, 2);
                } catch (e) {
                    resultElement.textContent = text;
                }
                
            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba: ${error.message}`;
            }
        }

        async function quickTest(username, password) {
            const resultElement = document.getElementById('quick-result');
            resultElement.className = 'result-area loading';
            resultElement.textContent = `Testování ${username}/${password}...`;

            try {
                const response = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const text = await response.text();
                
                resultElement.className = response.ok ? 'result-area success' : 'result-area error';
                
                try {
                    const json = JSON.parse(text);
                    resultElement.textContent = `Test ${username}/${password}:\n`;
                    resultElement.textContent += JSON.stringify(json, null, 2);
                } catch (e) {
                    resultElement.textContent = `Test ${username}/${password}:\n${text}`;
                }
                
            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba při testu ${username}/${password}: ${error.message}`;
            }
        }

        async function compareAPIs() {
            const resultElement = document.getElementById('compare-result');
            resultElement.className = 'result-area loading';
            resultElement.textContent = 'Porovnávání API...';

            const testData = {
                username: 'admin',
                password: 'admin123'
            };

            try {
                // Test simple_auth
                const simpleResponse = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const simpleText = await simpleResponse.text();

                // Test auth
                const authResponse = await fetch('api/auth.php?action=login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(testData)
                });
                const authText = await authResponse.text();

                resultElement.className = 'result-area';
                resultElement.textContent = '=== SIMPLE_AUTH API ===\n';
                resultElement.textContent += `Status: ${simpleResponse.status}\n`;
                resultElement.textContent += `Response: ${simpleText}\n\n`;
                
                resultElement.textContent += '=== AUTH API ===\n';
                resultElement.textContent += `Status: ${authResponse.status}\n`;
                resultElement.textContent += `Response: ${authText}`;

            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba při porovnání: ${error.message}`;
            }
        }

        // Automatický test při načtení stránky
        window.onload = function() {
            testAPI('test');
        };
    </script>
</body>
</html>
