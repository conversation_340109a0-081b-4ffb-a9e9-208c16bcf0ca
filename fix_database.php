<?php
/**
 * Oprava databázového připojení
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Oprava databázového připojení</h1>";

if ($_POST) {
    $host = $_POST['host'] ?? 'localhost';
    $dbname = $_POST['dbname'] ?? 'unicentaopos';
    $username = $_POST['username'] ?? 'root';
    $password = $_POST['password'] ?? '';
    
    echo "<h2>Test nové konfigurace</h2>";
    
    try {
        // Test připojení
        $dsn = "mysql:host=$host;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        
        echo "<p style='color: green;'>✓ Připojení k MySQL úspěšné</p>";
        
        // Vytvoření databáze pokud neexistuje
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbname` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        echo "<p style='color: green;'>✓ Databáze '$dbname' je připravena</p>";
        
        // Test připojení k databázi
        $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]);
        
        echo "<p style='color: green;'>✓ Připojení k databázi '$dbname' úspěšné</p>";
        
        // Uložení konfigurace
        $config = [
            'host' => $host,
            'dbname' => $dbname,
            'username' => $username,
            'password' => $password,
            'charset' => 'utf8mb4',
            'options' => [
                'PDO::ATTR_ERRMODE' => 'PDO::ERRMODE_EXCEPTION',
                'PDO::ATTR_DEFAULT_FETCH_MODE' => 'PDO::FETCH_ASSOC',
                'PDO::ATTR_EMULATE_PREPARES' => false,
            ]
        ];
        
        $configContent = "<?php\n/**\n * Database Configuration\n * \n * This file contains the configuration for connecting to the UniCentaOPOS database.\n */\n\nreturn [\n";
        $configContent .= "    'host' => '$host',\n";
        $configContent .= "    'dbname' => '$dbname',\n";
        $configContent .= "    'username' => '$username',\n";
        $configContent .= "    'password' => '$password',\n";
        $configContent .= "    'charset' => 'utf8mb4',\n";
        $configContent .= "    'options' => [\n";
        $configContent .= "        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,\n";
        $configContent .= "        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,\n";
        $configContent .= "        PDO::ATTR_EMULATE_PREPARES => false,\n";
        $configContent .= "    ]\n";
        $configContent .= "];\n";
        
        if (file_put_contents(__DIR__ . '/config/database.php', $configContent)) {
            echo "<p style='color: green;'>✓ Konfigurace uložena do config/database.php</p>";
        } else {
            echo "<p style='color: red;'>✗ Nepodařilo se uložit konfiguraci</p>";
        }
        
        echo "<h3>Další kroky:</h3>";
        echo "<p><a href='create_admin.php' class='btn'>Vytvořit admin uživatele</a></p>";
        echo "<p><a href='simple_login.html' class='btn'>Zkusit přihlášení</a></p>";
        echo "<p><a href='index.html' class='btn'>Přejít do aplikace</a></p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    }
} else {
    // Načtení aktuální konfigurace
    $currentConfig = [
        'host' => 'localhost',
        'dbname' => 'unicentaopos',
        'username' => 'root',
        'password' => ''
    ];
    
    if (file_exists(__DIR__ . '/config/database.php')) {
        try {
            $config = require __DIR__ . '/config/database.php';
            $currentConfig = array_merge($currentConfig, $config);
        } catch (Exception $e) {
            // Použijeme výchozí hodnoty
        }
    }
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .form-group { margin: 15px 0; }
    label { display: block; margin-bottom: 5px; font-weight: bold; }
    input[type="text"], input[type="password"] { width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px; }
    button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
    button:hover { background: #005a87; }
    .btn { display: inline-block; background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px; margin: 5px; }
    .btn:hover { background: #005a87; }
    .preset { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px; }
</style>

<form method="post">
    <h2>Nastavení databázového připojení</h2>
    
    <div class="preset">
        <h3>Běžné konfigurace:</h3>
        <button type="button" onclick="setPreset('localhost', 'unicentaopos', 'root', '')">XAMPP/WAMP (root, bez hesla)</button>
        <button type="button" onclick="setPreset('localhost', 'unicentaopos', 'root', 'root')">MAMP (root/root)</button>
        <button type="button" onclick="setPreset('localhost', 'unicentaopos', 'michal', 'admin')">Aktuální (michal/admin)</button>
    </div>
    
    <div class="form-group">
        <label for="host">Host:</label>
        <input type="text" id="host" name="host" value="<?= htmlspecialchars($currentConfig['host']) ?>" required>
    </div>
    
    <div class="form-group">
        <label for="dbname">Název databáze:</label>
        <input type="text" id="dbname" name="dbname" value="<?= htmlspecialchars($currentConfig['dbname']) ?>" required>
    </div>
    
    <div class="form-group">
        <label for="username">Uživatelské jméno:</label>
        <input type="text" id="username" name="username" value="<?= htmlspecialchars($currentConfig['username']) ?>" required>
    </div>
    
    <div class="form-group">
        <label for="password">Heslo:</label>
        <input type="password" id="password" name="password" value="<?= htmlspecialchars($currentConfig['password']) ?>">
    </div>
    
    <button type="submit">Otestovat a uložit konfiguraci</button>
</form>

<script>
function setPreset(host, dbname, username, password) {
    document.getElementById('host').value = host;
    document.getElementById('dbname').value = dbname;
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
}
</script>

<h3>Další nástroje:</h3>
<p><a href="test_db_connection.php" class="btn">Test připojení</a></p>
<p><a href="create_admin.php" class="btn">Vytvořit admin uživatele</a></p>

<?php } ?>
