<?php
/**
 * Zobrazení debug logu triggerů
 *
 * Tento skript zobrazuje záznamy z tabulky trigger_debug_log, kter<PERSON> obsahuje
 * informace o průběhu triggerů pro aktualizaci inventurních záznamů.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    // Získání parametrů pro filtrování
    $productId = $_GET['product_id'] ?? '';
    $triggerName = $_GET['trigger_name'] ?? '';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
    
    // Omezení limitu na rozumnou hodnotu
    if ($limit <= 0 || $limit > 1000) {
        $limit = 100;
    }
    
    // Sestavení SQL dotazu
    $sql = "
        SELECT * FROM trigger_debug_log
        WHERE 1=1
    ";
    
    $params = [];
    
    if (!empty($productId)) {
        $sql .= " AND product_id = :product_id";
        $params['product_id'] = $productId;
    }
    
    if (!empty($triggerName)) {
        $sql .= " AND trigger_name = :trigger_name";
        $params['trigger_name'] = $triggerName;
    }
    
    $sql .= " ORDER BY id DESC LIMIT :limit";
    $params['limit'] = $limit;
    
    // Provedení dotazu
    $stmt = $pdo->prepare($sql);
    
    // Bindování parametrů
    foreach ($params as $key => $value) {
        if ($key === 'limit') {
            $stmt->bindValue(':' . $key, $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue(':' . $key, $value);
        }
    }
    
    $stmt->execute();
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Získání seznamu produktů pro filtr
    $productStmt = $pdo->query("
        SELECT DISTINCT product_id
        FROM trigger_debug_log
        ORDER BY product_id
    ");
    $products = $productStmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Získání seznamu triggerů pro filtr
    $triggerStmt = $pdo->query("
        SELECT DISTINCT trigger_name
        FROM trigger_debug_log
        ORDER BY trigger_name
    ");
    $triggers = $triggerStmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Zobrazení HTML
    echo "<!DOCTYPE html>
<html lang='cs'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Debug log triggerů</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f2f2f2; }
        .filter-form { margin: 20px 0; padding: 15px; background-color: #f8f8f8; border-radius: 5px; }
        .filter-form select, .filter-form input { padding: 5px; margin-right: 10px; }
        .filter-form button { padding: 5px 10px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }
        .filter-form button:hover { background-color: #45a049; }
        .actions { margin: 20px 0; }
        .actions a { display: inline-block; margin-right: 10px; padding: 8px 16px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; }
        .actions a:hover { background-color: #45a049; }
        .pagination { margin-top: 20px; }
        .pagination a { display: inline-block; padding: 5px 10px; margin-right: 5px; background-color: #f2f2f2; text-decoration: none; color: #333; }
        .pagination a:hover { background-color: #ddd; }
        .pagination .active { background-color: #4CAF50; color: white; }
    </style>
</head>
<body>
    <h1>Debug log triggerů</h1>
    
    <div class='actions'>
        <a href='index.html'>Zpět na hlavní stránku</a>
        <a href='fix_inventory_triggers_debug.php'>Opravit triggery</a>
        <a href='view_stockcurrent_logs.php'>Zobrazit logy stockcurrent</a>
    </div>
    
    <div class='filter-form'>
        <form method='get'>
            <label for='product_id'>Produkt:</label>
            <select name='product_id' id='product_id'>
                <option value=''>-- Všechny produkty --</option>";
    
    foreach ($products as $product) {
        $selected = ($product === $productId) ? 'selected' : '';
        echo "<option value='" . htmlspecialchars($product) . "' $selected>" . htmlspecialchars($product) . "</option>";
    }
    
    echo "</select>
            
            <label for='trigger_name'>Trigger:</label>
            <select name='trigger_name' id='trigger_name'>
                <option value=''>-- Všechny triggery --</option>";
    
    foreach ($triggers as $trigger) {
        $selected = ($trigger === $triggerName) ? 'selected' : '';
        echo "<option value='" . htmlspecialchars($trigger) . "' $selected>" . htmlspecialchars($trigger) . "</option>";
    }
    
    echo "</select>
            
            <label for='limit'>Počet záznamů:</label>
            <input type='number' name='limit' id='limit' value='" . $limit . "' min='1' max='1000'>
            
            <button type='submit'>Filtrovat</button>
            <button type='button' onclick='window.location.href=\"view_trigger_debug_log.php\"'>Zrušit filtr</button>
        </form>
    </div>
    
    <p>Celkem nalezeno záznamů: " . count($logs) . "</p>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Trigger</th>
                <th>Produkt</th>
                <th>Zpráva</th>
                <th>Čas</th>
            </tr>
        </thead>
        <tbody>";
    
    foreach ($logs as $log) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($log['id']) . "</td>";
        echo "<td>" . htmlspecialchars($log['trigger_name']) . "</td>";
        echo "<td>" . htmlspecialchars($log['product_id']) . "</td>";
        echo "<td>" . htmlspecialchars($log['message']) . "</td>";
        echo "<td>" . htmlspecialchars($log['timestamp']) . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>
    </table>
</body>
</html>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
