<?php
/**
 * Skript pro testování reportů
 */

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Spuštění session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test reportů</h1>";
    echo "<p style='color: green;'>✓ Připojení k databázi bylo úspěšné.</p>";
    
    // Kontrola přihlášení
    if (!isLoggedIn()) {
        echo "<p style='color: red;'>✗ Uživatel není přihlášen. Pro testování reportů je nutné být přihlášen.</p>";
        echo "<p><a href='test_auth.php'>Přejít na stránku pro přihlášení</a></p>";
    } else {
        echo "<p style='color: green;'>✓ Uživatel je přihlášen jako: " . htmlspecialchars($_SESSION['user']['username']) . " (role: " . htmlspecialchars($_SESSION['user']['role']) . ")</p>";
        
        // Kontrola, zda je uživatel admin nebo manager
        if (!isAdminOrManager()) {
            echo "<p style='color: red;'>✗ Uživatel nemá oprávnění pro zobrazení reportů. Je nutné být admin nebo manager.</p>";
        } else {
            echo "<p style='color: green;'>✓ Uživatel má oprávnění pro zobrazení reportů.</p>";
            
            // Získání seznamu všech inventurních relací
            $stmt = $pdo->query("
                SELECT 
                    s.id, 
                    s.start_time, 
                    s.end_time,
                    s.status, 
                    u.username, 
                    COUNT(DISTINCT e.user_id) AS user_count,
                    COUNT(e.id) AS entry_count
                FROM 
                    inventory_sessions s
                JOIN 
                    inventory_users u ON s.user_id = u.id
                LEFT JOIN 
                    inventory_entries e ON s.id = e.session_id AND e.status = 'active'
                GROUP BY 
                    s.id, s.start_time, s.end_time, s.status, u.username
                ORDER BY 
                    s.start_time DESC
            ");
            
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h2>Seznam inventurních relací</h2>";
            
            if (empty($sessions)) {
                echo "<p>Žádné inventurní relace nebyly nalezeny.</p>";
            } else {
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>ID</th><th>Čas zahájení</th><th>Čas ukončení</th><th>Stav</th><th>Vytvořil</th><th>Počet uživatelů</th><th>Počet záznamů</th><th>Akce</th></tr>";
                
                foreach ($sessions as $session) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($session['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['start_time']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['end_time'] ?? 'N/A') . "</td>";
                    echo "<td>" . htmlspecialchars($session['status']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['username']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['user_count']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['entry_count']) . "</td>";
                    echo "<td>";
                    echo "<form method='post' style='display: inline;'>";
                    echo "<input type='hidden' name='session_id' value='" . htmlspecialchars($session['id']) . "'>";
                    echo "<input type='submit' name='view_report' value='Zobrazit report'>";
                    echo "</form>";
                    
                    if ($session['status'] === 'active') {
                        echo " ";
                        echo "<form method='post' style='display: inline;'>";
                        echo "<input type='hidden' name='session_id' value='" . htmlspecialchars($session['id']) . "'>";
                        echo "<input type='submit' name='complete_session' value='Dokončit inventuru'>";
                        echo "</form>";
                        
                        echo " ";
                        echo "<form method='post' style='display: inline;'>";
                        echo "<input type='hidden' name='session_id' value='" . htmlspecialchars($session['id']) . "'>";
                        echo "<input type='submit' name='cancel_session' value='Zrušit inventuru'>";
                        echo "</form>";
                    }
                    
                    echo "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            }
            
            // Zpracování dokončení inventury
            if (isset($_POST['complete_session'])) {
                $sessionId = $_POST['session_id'];
                
                try {
                    // Aktualizace stavu inventurní relace
                    $stmt = $pdo->prepare("
                        UPDATE inventory_sessions
                        SET status = 'completed', end_time = NOW()
                        WHERE id = :session_id
                    ");
                    
                    $stmt->execute(['session_id' => $sessionId]);
                    
                    echo "<h2>Inventura byla dokončena</h2>";
                    echo "<p style='color: green;'>✓ Inventura byla úspěšně dokončena.</p>";
                    echo "<p><a href='" . $_SERVER['PHP_SELF'] . "'>Obnovit stránku</a></p>";
                } catch (PDOException $e) {
                    echo "<h2>Chyba při dokončování inventury</h2>";
                    echo "<p style='color: red;'>✗ Došlo k chybě při dokončování inventury: " . $e->getMessage() . "</p>";
                }
            }
            
            // Zpracování zrušení inventury
            if (isset($_POST['cancel_session'])) {
                $sessionId = $_POST['session_id'];
                
                try {
                    // Aktualizace stavu inventurní relace
                    $stmt = $pdo->prepare("
                        UPDATE inventory_sessions
                        SET status = 'cancelled', end_time = NOW()
                        WHERE id = :session_id
                    ");
                    
                    $stmt->execute(['session_id' => $sessionId]);
                    
                    echo "<h2>Inventura byla zrušena</h2>";
                    echo "<p style='color: green;'>✓ Inventura byla úspěšně zrušena.</p>";
                    echo "<p><a href='" . $_SERVER['PHP_SELF'] . "'>Obnovit stránku</a></p>";
                } catch (PDOException $e) {
                    echo "<h2>Chyba při rušení inventury</h2>";
                    echo "<p style='color: red;'>✗ Došlo k chybě při rušení inventury: " . $e->getMessage() . "</p>";
                }
            }
            
            // Zobrazení reportu pro vybranou relaci
            if (isset($_POST['view_report'])) {
                $sessionId = $_POST['session_id'];
                
                // Získání informací o relaci
                $stmt = $pdo->prepare("
                    SELECT 
                        s.id, 
                        s.start_time, 
                        s.end_time,
                        s.status, 
                        u.username
                    FROM 
                        inventory_sessions s
                    JOIN 
                        inventory_users u ON s.user_id = u.id
                    WHERE 
                        s.id = :session_id
                ");
                
                $stmt->execute(['session_id' => $sessionId]);
                $session = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($session) {
                    echo "<h2>Report pro inventurní relaci #" . htmlspecialchars($session['id']) . "</h2>";
                    echo "<p>Čas zahájení: " . htmlspecialchars($session['start_time']) . "</p>";
                    echo "<p>Čas ukončení: " . htmlspecialchars($session['end_time'] ?? 'N/A') . "</p>";
                    echo "<p>Stav: " . htmlspecialchars($session['status']) . "</p>";
                    echo "<p>Vytvořil: " . htmlspecialchars($session['username']) . "</p>";
                    
                    // Získání seznamu uživatelů, kteří se podíleli na inventuře
                    $stmt = $pdo->prepare("
                        SELECT 
                            u.id,
                            u.username,
                            u.full_name,
                            COUNT(e.id) AS entry_count
                        FROM 
                            inventory_users u
                        JOIN 
                            inventory_entries e ON u.id = e.user_id
                        WHERE 
                            e.session_id = :session_id
                            AND e.status = 'active'
                        GROUP BY 
                            u.id, u.username, u.full_name
                        ORDER BY 
                            entry_count DESC
                    ");
                    
                    $stmt->execute(['session_id' => $sessionId]);
                    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<h3>Uživatelé podílející se na inventuře</h3>";
                    
                    if (empty($users)) {
                        echo "<p>Žádní uživatelé se nepodíleli na inventuře.</p>";
                    } else {
                        echo "<table border='1' cellpadding='5' cellspacing='0'>";
                        echo "<tr><th>Uživatelské jméno</th><th>Celé jméno</th><th>Počet záznamů</th></tr>";
                        
                        foreach ($users as $user) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                            echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($user['entry_count']) . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                    }
                    
                    // Získání souhrnných statistik
                    $stmt = $pdo->prepare("
                        SELECT 
                            COUNT(DISTINCT e.product_id) AS product_count,
                            SUM(e.zadane_mnozstvi) AS total_zadane_mnozstvi,
                            SUM(COALESCE(s.units, 0)) AS total_current_stock,
                            SUM(e.zadane_mnozstvi - COALESCE(s.units, 0)) AS total_difference
                        FROM 
                            inventory_entries e
                        JOIN 
                            products p ON e.product_id = p.id
                        LEFT JOIN 
                            stockcurrent s ON p.id = s.product
                        WHERE 
                            e.session_id = :session_id
                            AND e.status = 'active'
                    ");
                    
                    $stmt->execute(['session_id' => $sessionId]);
                    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    echo "<h3>Souhrnné statistiky</h3>";
                    echo "<table border='1' cellpadding='5' cellspacing='0'>";
                    echo "<tr><th>Počet produktů</th><th>Celkové zadané množství</th><th>Celkový aktuální stav</th><th>Celkový rozdíl</th></tr>";
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($stats['product_count']) . "</td>";
                    echo "<td>" . htmlspecialchars($stats['total_zadane_mnozstvi']) . "</td>";
                    echo "<td>" . htmlspecialchars($stats['total_current_stock']) . "</td>";
                    echo "<td>" . htmlspecialchars($stats['total_difference']) . "</td>";
                    echo "</tr>";
                    echo "</table>";
                    
                    // Získání seznamu produktů s největším rozdílem
                    $stmt = $pdo->prepare("
                        SELECT 
                            p.id AS product_id,
                            p.code AS ean_code,
                            p.name AS product_name,
                            c.name AS category,
                            SUM(e.zadane_mnozstvi) AS zadane_mnozstvi,
                            COALESCE(s.units, 0) AS current_stock,
                            (SUM(e.zadane_mnozstvi) - COALESCE(s.units, 0)) AS difference
                        FROM 
                            inventory_entries e
                        JOIN 
                            products p ON e.product_id = p.id
                        LEFT JOIN 
                            categories c ON p.category = c.id
                        LEFT JOIN 
                            stockcurrent s ON p.id = s.product
                        WHERE 
                            e.session_id = :session_id
                            AND e.status = 'active'
                        GROUP BY 
                            p.id, p.code, p.name, c.name, s.units
                        ORDER BY 
                            ABS(difference) DESC
                        LIMIT 10
                    ");
                    
                    $stmt->execute(['session_id' => $sessionId]);
                    $topDifferences = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    echo "<h3>Top 10 produktů s největším rozdílem</h3>";
                    
                    if (empty($topDifferences)) {
                        echo "<p>Žádné produkty nebyly nalezeny.</p>";
                    } else {
                        echo "<table border='1' cellpadding='5' cellspacing='0'>";
                        echo "<tr><th>EAN kód</th><th>Název produktu</th><th>Kategorie</th><th>Zadané množství</th><th>Aktuální stav</th><th>Rozdíl</th></tr>";
                        
                        foreach ($topDifferences as $product) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($product['ean_code']) . "</td>";
                            echo "<td>" . htmlspecialchars($product['product_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($product['category'] ?? '') . "</td>";
                            echo "<td>" . htmlspecialchars($product['zadane_mnozstvi']) . "</td>";
                            echo "<td>" . htmlspecialchars($product['current_stock']) . "</td>";
                            echo "<td>" . htmlspecialchars($product['difference']) . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</table>";
                    }
                    
                    // Odkaz na export do CSV
                    echo "<h3>Export dat</h3>";
                    echo "<p><a href='export_inventory.php?session_id=" . htmlspecialchars($sessionId) . "'>Exportovat data do CSV</a></p>";
                } else {
                    echo "<p style='color: red;'>✗ Inventurní relace nebyla nalezena.</p>";
                }
            }
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
