<?php
/**
 * Sledování změn v tabulkách stockcurrent a inventory_totals při prodeji
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();

    echo "<h1>Sledování změn v tabulkách stockcurrent a inventory_totals při prodeji</h1>";

    // Kontrola, zda existuje trigger update_inventory_entries_after_stockcurrent_update
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND TRIGGER_NAME = 'update_inventory_entries_after_stockcurrent_update'
    ");
    $trigger = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($trigger) {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update existuje");
        logMessage("Kód triggeru: " . $trigger['ACTION_STATEMENT']);
    } else {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update neexistuje", true);
    }

    // Výpis posledních změn v tabulce stockcurrent_log
    $stmt = $pdo->query("
        SELECT * FROM stockcurrent_log
        ORDER BY created DESC
        LIMIT 10
    ");
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Poslední změny v tabulce stockcurrent_log</h2>";

    if (empty($logs)) {
        logMessage("Zatím nebyly zaznamenány žádné změny v tabulce stockcurrent_log.");
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Produkt</th><th>Stará hodnota</th><th>Nová hodnota</th><th>Rozdíl</th><th>Typ změny</th><th>Čas změny</th></tr>";

        foreach ($logs as $log) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($log['id']) . "</td>";
            echo "<td>" . htmlspecialchars($log['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($log['old_units']) . "</td>";
            echo "<td>" . htmlspecialchars($log['new_units']) . "</td>";
            echo "<td>" . htmlspecialchars($log['difference']) . "</td>";
            echo "<td>" . htmlspecialchars($log['change_type'] ?? '') . "</td>";
            echo "<td>" . htmlspecialchars($log['created']) . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    }

    // Výpis posledních změn v tabulce inventory_totals
    $stmt = $pdo->query("
        SELECT it.*, is2.title as session_name, is2.status as session_status
        FROM inventory_totals it
        JOIN inventory_sessions is2 ON it.session_id = is2.id
        WHERE is2.status = 'active'
        ORDER BY it.last_updated DESC
        LIMIT 10
    ");
    $totals = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Poslední změny v tabulce inventory_totals</h2>";

    if (empty($totals)) {
        logMessage("Zatím nebyly zaznamenány žádné změny v tabulce inventory_totals.");
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Session</th><th>Produkt</th><th>Zadané množství</th><th>Poslední aktualizace</th></tr>";

        foreach ($totals as $total) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($total['id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['session_name']) . "</td>";
            echo "<td>" . htmlspecialchars($total['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['total_zadane_mnozstvi']) . "</td>";
            echo "<td>" . htmlspecialchars($total['last_updated']) . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    }

    echo "<p><a href='sledovani_prodeje.php'>Obnovit stránku</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";

} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
