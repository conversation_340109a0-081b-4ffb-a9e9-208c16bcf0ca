<?php
/**
 * Odstranění triggerů
 * 
 * Tento skript odstraní všechny triggery, k<PERSON><PERSON> jsme vyt<PERSON>ř<PERSON>.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Odstranění triggerů</h1>";
    
    // Získání seznamu všech triggerů v databázi
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Seznam triggerů před odstraněním</h2>";
    
    if (empty($triggers)) {
        echo "<p>V datab<PERSON>zi nejsou ž<PERSON> triggery.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Trigger Name</th><th>Event</th><th>Table</th></tr>";
        
        foreach ($triggers as $trigger) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($trigger['TRIGGER_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['EVENT_MANIPULATION']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['EVENT_OBJECT_TABLE']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Odstranění triggerů pro tabulku ticketlines
    try {
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_totals_after_ticketlines_insert`");
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_totals_after_ticketlines_update`");
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_totals_after_ticketlines_delete`");
        echo "<p>Triggery pro tabulku ticketlines byly odstraněny.</p>";
    } catch (PDOException $e) {
        echo "<p>Chyba při odstraňování triggerů pro tabulku ticketlines: " . $e->getMessage() . "</p>";
    }
    
    // Odstranění triggerů pro tabulku stockcurrent
    try {
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_totals_after_stockcurrent_update`");
        echo "<p>Trigger pro tabulku stockcurrent byl odstraněn.</p>";
    } catch (PDOException $e) {
        echo "<p>Chyba při odstraňování triggeru pro tabulku stockcurrent: " . $e->getMessage() . "</p>";
    }
    
    // Získání seznamu všech triggerů v databázi po odstranění
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Seznam triggerů po odstranění</h2>";
    
    if (empty($triggers)) {
        echo "<p>V databázi nejsou žádné triggery.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Trigger Name</th><th>Event</th><th>Table</th></tr>";
        
        foreach ($triggers as $trigger) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($trigger['TRIGGER_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['EVENT_MANIPULATION']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['EVENT_OBJECT_TABLE']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<p>Všechny triggery byly odstraněny. Nyní zkuste provést prodej v UniCentaOPOS a zkontrolujte, zda se chyba stále objevuje.</p>";
    
    echo "<h2>Alternativní řešení</h2>";
    echo "<p>Místo použití triggerů můžeme vytvořit skript, který bude pravidelně kontrolovat změny v tabulce ticketlines a aktualizovat zadané množství v celkové inventuře.</p>";
    
    // Vytvoření tabulky pro sledování změn v ticketlines
    try {
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `ticketlines_changes` (
              `id` INT AUTO_INCREMENT PRIMARY KEY,
              `last_checked` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");
        
        // Kontrola, zda existuje záznam v tabulce ticketlines_changes
        $stmt = $pdo->query("SELECT COUNT(*) FROM ticketlines_changes");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            // Vložení prvního záznamu
            $pdo->exec("INSERT INTO ticketlines_changes (last_checked) VALUES (NOW())");
        }
        
        echo "<p>Tabulka ticketlines_changes byla vytvořena.</p>";
    } catch (PDOException $e) {
        echo "<p>Chyba při vytváření tabulky ticketlines_changes: " . $e->getMessage() . "</p>";
    }
    
    echo "<p><a href='sync_inventory.php'>Přejít na synchronizaci inventury</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
