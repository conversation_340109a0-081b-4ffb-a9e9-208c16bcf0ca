<?php
/**
 * <PERSON>římý test API
 */

// Spuštění session pouze pokud ještě není spuš<PERSON>na
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Přímý test API</h1>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    echo "<h2>1. Přihlášení</h2>";
    
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné</p>";
        echo "<p>Uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
    } else {
        echo "<p style='color: red;'>✗ Přihlášení selhalo</p>";
        exit;
    }
    
    echo "<h2>2. Test databáze</h2>";
    
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
    
    // Test INSERT přímo
    echo "<h3>Test INSERT do inventory_sessions:</h3>";
    
    try {
        $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (?)");
        $result = $stmt->execute([$user['id']]);
        
        if ($result) {
            $sessionId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✓ INSERT úspěšný, ID: $sessionId</p>";
        } else {
            echo "<p style='color: red;'>✗ INSERT neúspěšný</p>";
            echo "<p>Error info: " . print_r($stmt->errorInfo(), true) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Chyba při INSERT: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
    }
    
    echo "<h2>3. Test API funkce přímo</h2>";
    
    // Nastavíme prostředí pro API
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET = [];
    
    // Simulace JSON vstupu
    $GLOBALS['HTTP_RAW_POST_DATA'] = json_encode([]);
    
    echo "<h3>Volání createSession() přímo:</h3>";
    
    // Zachytíme výstup
    ob_start();
    
    try {
        // Načteme API funkce
        require_once __DIR__ . '/api/inventory.php';
        
        // Zavoláme createSession přímo
        createSession();
        
    } catch (Exception $e) {
        echo "Chyba při volání createSession(): " . $e->getMessage() . "<br>";
        echo "File: " . $e->getFile() . "<br>";
        echo "Line: " . $e->getLine() . "<br>";
        echo "Stack trace: <pre>" . $e->getTraceAsString() . "</pre>";
    }
    
    $output = ob_get_clean();
    
    echo "<h4>Výstup z createSession():</h4>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    echo "<h2>4. Test HTTP požadavku</h2>";
    
    // Test skutečného HTTP požadavku
    $url = 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php';
    $data = json_encode([]);
    
    // Získáme cookies z aktuální session
    $sessionName = session_name();
    $sessionId = session_id();
    $cookie = $sessionName . '=' . $sessionId;
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($data),
                'Cookie: ' . $cookie
            ],
            'content' => $data
        ]
    ]);
    
    echo "<h3>HTTP POST požadavek:</h3>";
    echo "<p>URL: $url</p>";
    echo "<p>Data: $data</p>";
    echo "<p>Cookie: $cookie</p>";
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "<p style='color: red;'>✗ HTTP požadavek selhal</p>";
        $error = error_get_last();
        echo "<p>Chyba: " . $error['message'] . "</p>";
    } else {
        echo "<p style='color: green;'>✓ HTTP požadavek úspěšný</p>";
        echo "<h4>Odpověď:</h4>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        
        // Pokusíme se dekódovat JSON
        $jsonResponse = json_decode($response, true);
        if ($jsonResponse) {
            echo "<h4>Dekódovaná odpověď:</h4>";
            echo "<pre>" . print_r($jsonResponse, true) . "</pre>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Celková chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
?>
