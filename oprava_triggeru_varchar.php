<?php
/**
 * Oprava triggeru pro aktualizaci inventory_totals po prodeji
 * 
 * Tento skript opraví trigger, který aktualizuje zadané množství v celkové inventuře
 * po prodeji produktů. Zajistí, že se zadané množství v celkové inventuře bude
 * aktualizovat při každé změně stavu, bez ohledu na to, zda jde o snížení nebo zvýšení stavu.
 * Trigger bude používat sloupec change_type jako varchar.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování do souboru
function logToFile($message) {
    $logFile = __DIR__ . '/oprava_triggeru_varchar.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logToFile("Připojení k databázi úspěšné");
    
    // Kontrola, zda existuje trigger update_inventory_entries_after_stockcurrent_update
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND TRIGGER_NAME = 'update_inventory_entries_after_stockcurrent_update'
    ");
    $trigger = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($trigger) {
        logToFile("Trigger update_inventory_entries_after_stockcurrent_update existuje");
        logToFile("Kód triggeru: " . $trigger['ACTION_STATEMENT']);
        
        // Odstranění existujícího triggeru
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_entries_after_stockcurrent_update`");
        logToFile("Existující trigger byl odstraněn");
    } else {
        logToFile("Trigger update_inventory_entries_after_stockcurrent_update neexistuje");
    }
    
    // Kontrola struktury tabulky stockcurrent_log
    $stmt = $pdo->query("DESCRIBE stockcurrent_log");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    logToFile("Struktura tabulky stockcurrent_log:");
    $hasChangeType = false;
    $changeTypeType = '';
    foreach ($columns as $column) {
        logToFile("  " . $column['Field'] . " (" . $column['Type'] . ")");
        if ($column['Field'] === 'change_type') {
            $hasChangeType = true;
            $changeTypeType = $column['Type'];
        }
    }
    
    if ($hasChangeType) {
        logToFile("Sloupec change_type existuje v tabulce stockcurrent_log a je typu " . $changeTypeType);
    } else {
        logToFile("Sloupec change_type neexistuje v tabulce stockcurrent_log");
        $pdo->exec("ALTER TABLE stockcurrent_log ADD COLUMN change_type VARCHAR(50) DEFAULT 'sale'");
        logToFile("Sloupec change_type byl přidán do tabulky stockcurrent_log jako VARCHAR(50)");
    }
    
    // Vytvoření nového triggeru pro aktualizaci inventory_totals při UPDATE v stockcurrent
    try {
        $pdo->exec("
            CREATE TRIGGER `update_inventory_entries_after_stockcurrent_update`
            AFTER UPDATE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku (s použitím sloupce change_type jako varchar)
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference, change_type)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference, 'sale');

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
        ");
        
        logToFile("Nový trigger byl úspěšně vytvořen");
    } catch (PDOException $e) {
        logToFile("Chyba při vytváření triggeru: " . $e->getMessage());
    }
    
    // Kontrola, zda byl trigger úspěšně vytvořen
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND TRIGGER_NAME = 'update_inventory_entries_after_stockcurrent_update'
    ");
    $newTrigger = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($newTrigger) {
        logToFile("Trigger update_inventory_entries_after_stockcurrent_update byl úspěšně vytvořen");
        logToFile("Kód nového triggeru: " . $newTrigger['ACTION_STATEMENT']);
    } else {
        logToFile("Trigger update_inventory_entries_after_stockcurrent_update nebyl vytvořen");
    }
    
    logToFile("Oprava dokončena");
    
    // Výpis cesty k logovacímu souboru
    echo "Oprava byla dokončena. Logovací soubor: " . __DIR__ . '/oprava_triggeru_varchar.log';
    
} catch (PDOException $e) {
    logToFile("Chyba při připojení k databázi: " . $e->getMessage());
    echo "Došlo k chybě. Zkontrolujte logovací soubor: " . __DIR__ . '/oprava_triggeru_varchar.log';
}
?>
