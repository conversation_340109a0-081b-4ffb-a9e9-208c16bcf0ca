<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventurní systém - FUNGUJÍCÍ VERZE</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        .page { display: none; }
        .page.active { display: block; }
    </style>
</head>
<body>
    <div id="app">
        <!-- Navigační lišta -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary d-none" id="main-navbar">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">Inventurní systém</a>
                <div class="collapse navbar-collapse">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('dashboard')">Dashboard</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showPage('inventory')">Inventura</a>
                        </li>
                        <li class="nav-item admin-only manager-only">
                            <a class="nav-link" href="#" onclick="showPage('users')">Uživatelé</a>
                        </li>
                    </ul>
                    <div class="d-flex">
                        <span class="navbar-text me-3" id="user-info"></span>
                        <button class="btn btn-outline-light" onclick="logout()">Odhlásit</button>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Přihlašovací stránka -->
        <div id="login-page" class="page active">
            <div class="container mt-5">
                <div class="row justify-content-center">
                    <div class="col-md-6 col-lg-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0">Přihlášení do inventurního systému</h4>
                            </div>
                            <div class="card-body">
                                <form id="login-form">
                                    <div class="mb-3">
                                        <label for="username" class="form-label">Uživatelské jméno</label>
                                        <input type="text" class="form-control" id="username" value="admin" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="password" class="form-label">Heslo</label>
                                        <input type="password" class="form-control" id="password" value="admin123" required>
                                    </div>
                                    <div class="alert alert-danger d-none" id="login-error"></div>
                                    <button type="submit" class="btn btn-primary w-100">Přihlásit</button>
                                </form>
                                
                                <hr>
                                <div class="text-center">
                                    <small class="text-muted">
                                        Výchozí přihlášení: admin / admin123<br>
                                        <a href="create_admin.php" target="_blank">Vytvořit admin uživatele</a> |
                                        <a href="simple_login.html" target="_blank">Jednoduchý test</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Dashboard -->
        <div id="dashboard-page" class="page">
            <div class="container mt-4">
                <h1>Dashboard</h1>
                <div class="alert alert-success">
                    <h4>Úspěšně přihlášen!</h4>
                    <p>Vítejte v inventurním systému.</p>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Rychlé akce</h5>
                            </div>
                            <div class="card-body">
                                <button class="btn btn-primary me-2" onclick="showPage('inventory')">Nová inventura</button>
                                <button class="btn btn-secondary" onclick="showPage('users')">Správa uživatelů</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventura -->
        <div id="inventory-page" class="page">
            <div class="container mt-4">
                <h1>Inventura</h1>
                <div class="alert alert-info">
                    <p>Funkce inventury bude implementována v další verzi.</p>
                </div>
            </div>
        </div>

        <!-- Uživatelé -->
        <div id="users-page" class="page">
            <div class="container mt-4">
                <h1>Správa uživatelů</h1>
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Seznam uživatelů</h5>
                    </div>
                    <div class="card-body">
                        <div id="users-list">
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Načítání...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentUser = null;

        // Inicializace
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Aplikace inicializována');
            
            // Event listener pro přihlášení
            document.getElementById('login-form').addEventListener('submit', handleLogin);
        });

        // Přihlášení
        function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('login-error');
            
            console.log('Pokus o přihlášení:', username);
            
            // Skrytí chyby
            errorDiv.classList.add('d-none');
            
            fetch('api/simple_auth.php?action=login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.text();
            })
            .then(text => {
                console.log('Response text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Neplatná odpověď ze serveru: ' + text);
                }
                
                if (data.success) {
                    currentUser = data.user;
                    console.log('Přihlášení úspěšné:', currentUser);
                    showLoggedInUI();
                } else {
                    throw new Error(data.error || 'Přihlášení selhalo');
                }
            })
            .catch(error => {
                console.error('Chyba při přihlašování:', error);
                errorDiv.textContent = error.message;
                errorDiv.classList.remove('d-none');
            });
        }

        // Zobrazení UI po přihlášení
        function showLoggedInUI() {
            // Skrytí přihlašovací stránky
            document.getElementById('login-page').classList.remove('active');
            
            // Zobrazení navigace
            document.getElementById('main-navbar').classList.remove('d-none');
            
            // Nastavení informací o uživateli
            const userInfo = document.getElementById('user-info');
            userInfo.textContent = `${currentUser.username} (${getRoleName(currentUser.role)})`;
            
            // Zobrazení dashboardu
            showPage('dashboard');
            
            // Načtení uživatelů pokud je admin
            if (currentUser.role === 'admin' || currentUser.role === 'manager') {
                loadUsers();
            }
        }

        // Zobrazení stránky
        function showPage(pageName) {
            console.log('Zobrazuji stránku:', pageName);
            
            // Skrytí všech stránek
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
            });
            
            // Zobrazení požadované stránky
            const page = document.getElementById(pageName + '-page');
            if (page) {
                page.classList.add('active');
            }
        }

        // Načtení uživatelů
        function loadUsers() {
            const usersList = document.getElementById('users-list');
            
            fetch('api/users.php')
            .then(response => response.json())
            .then(data => {
                if (data.users) {
                    displayUsers(data.users);
                } else {
                    usersList.innerHTML = '<p class="text-danger">Nepodařilo se načíst uživatele</p>';
                }
            })
            .catch(error => {
                console.error('Chyba při načítání uživatelů:', error);
                usersList.innerHTML = '<p class="text-danger">Chyba: ' + error.message + '</p>';
            });
        }

        // Zobrazení uživatelů
        function displayUsers(users) {
            const usersList = document.getElementById('users-list');
            
            if (!users || users.length === 0) {
                usersList.innerHTML = '<p>Žádní uživatelé</p>';
                return;
            }
            
            let html = '<table class="table table-striped">';
            html += '<thead><tr><th>ID</th><th>Username</th><th>Role</th><th>Aktivní</th></tr></thead><tbody>';
            
            users.forEach(user => {
                html += '<tr>';
                html += `<td>${user.id}</td>`;
                html += `<td>${user.username}</td>`;
                html += `<td>${getRoleName(user.role)}</td>`;
                html += `<td>${user.active ? 'Ano' : 'Ne'}</td>`;
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            usersList.innerHTML = html;
        }

        // Název role
        function getRoleName(role) {
            switch (role) {
                case 'admin': return 'Administrátor';
                case 'manager': return 'Manažer';
                case 'user': return 'Uživatel';
                default: return role;
            }
        }

        // Odhlášení
        function logout() {
            fetch('api/simple_auth.php?action=logout', { method: 'POST' })
            .then(() => {
                currentUser = null;
                document.getElementById('main-navbar').classList.add('d-none');
                document.querySelectorAll('.page').forEach(page => page.classList.remove('active'));
                document.getElementById('login-page').classList.add('active');
                document.getElementById('username').value = '';
                document.getElementById('password').value = '';
            })
            .catch(error => {
                console.error('Chyba při odhlašování:', error);
                // Odhlásíme lokálně i při chybě
                location.reload();
            });
        }
    </script>
</body>
</html>
