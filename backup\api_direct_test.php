<?php
/**
 * API Direct Test
 *
 * Jednoduchý skript pro přímé testování API.
 */

// Zapnutí zobrazování chyb pro ladění
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Nastavení typu obsahu na HTML
header('Content-Type: text/html; charset=utf-8');

// Načtení utility pro práci s databází
require_once __DIR__ . '/utils/database.php';

// Kontrola připojení k databázi
try {
    $pdo = getDbConnection();
    $dbStatus = [
        'connected' => true,
        'message' => 'Připojení k databázi je funkční'
    ];
    
    // Získání informací o databázi
    $stmt = $pdo->query("SELECT VERSION() AS version");
    $version = $stmt->fetch();
    
    // Získání seznamu tabulek
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $dbStatus['version'] = $version['version'];
    $dbStatus['tables'] = $tables;
    $dbStatus['tables_count'] = count($tables);
} catch (PDOException $e) {
    $dbStatus = [
        'connected' => false,
        'message' => 'Chyba připojení k databázi: ' . $e->getMessage()
    ];
}

// Kontrola existence tabulek
$customTables = [
    'inventory_sessions',
    'inventory_entries',
    'inventory_stock_changes',
    'previous_stock',
    'inventory_users'
];

$tableStatus = [];

if ($dbStatus['connected']) {
    foreach ($customTables as $table) {
        try {
            $stmt = $pdo->prepare("SHOW TABLES LIKE :table");
            $stmt->execute(['table' => $table]);
            $tableStatus[$table] = $stmt->rowCount() > 0;
        } catch (PDOException $e) {
            $tableStatus[$table] = false;
        }
    }
}

// Kontrola existence výchozího administrátorského účtu
$adminExists = false;
if ($dbStatus['connected'] && ($tableStatus['inventory_users'] ?? false)) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM inventory_users WHERE username = 'admin'");
        $stmt->execute();
        $adminExists = $stmt->fetchColumn() > 0;
    } catch (PDOException $e) {
        $adminExists = false;
    }
}

// Získání informací o serveru
$serverInfo = [
    'php_version' => phpversion(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown',
    'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
    'script_name' => $_SERVER['SCRIPT_NAME'] ?? 'Unknown',
    'http_host' => $_SERVER['HTTP_HOST'] ?? 'Unknown',
    'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
    'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'Unknown',
    'query_string' => $_SERVER['QUERY_STRING'] ?? '',
    'path_info' => $_SERVER['PATH_INFO'] ?? '/',
    'session_status' => session_status() == PHP_SESSION_ACTIVE ? 'Active' : 'Inactive'
];

// Sestavení odpovědi
$response = [
    'success' => true,
    'message' => 'API Direct Test',
    'timestamp' => date('Y-m-d H:i:s'),
    'database' => [
        'status' => $dbStatus,
        'custom_tables' => $tableStatus,
        'admin_exists' => $adminExists
    ],
    'server' => $serverInfo
];

// Zobrazení výsledků
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Direct Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2 {
            color: #2c3e50;
        }
        .section {
            margin-bottom: 30px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .section h2 {
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .status {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
        }
        pre {
            background-color: #f8f9fa;
            border: 1px solid #eee;
            border-radius: 3px;
            padding: 10px;
            overflow: auto;
            max-height: 400px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        table, th, td {
            border: 1px solid #ddd;
        }
        th, td {
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>API Direct Test</h1>
    
    <div class="section">
        <h2>Databáze</h2>
        <p>
            Status: 
            <span class="status <?= $dbStatus['connected'] ? 'status-success' : 'status-error' ?>">
                <?= $dbStatus['connected'] ? 'Připojeno' : 'Nepřipojeno' ?>
            </span>
        </p>
        
        <?php if ($dbStatus['connected']): ?>
            <p>Verze: <?= htmlspecialchars($dbStatus['version']) ?></p>
            <p>Počet tabulek: <?= count($dbStatus['tables']) ?></p>
            
            <h3>Vlastní tabulky:</h3>
            <table>
                <tr>
                    <th>Tabulka</th>
                    <th>Existuje</th>
                </tr>
                <?php foreach ($tableStatus as $table => $exists): ?>
                    <tr>
                        <td><?= htmlspecialchars($table) ?></td>
                        <td>
                            <span class="status <?= $exists ? 'status-success' : 'status-error' ?>">
                                <?= $exists ? 'Ano' : 'Ne' ?>
                            </span>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>
            
            <p>
                Admin účet existuje: 
                <span class="status <?= $adminExists ? 'status-success' : 'status-error' ?>">
                    <?= $adminExists ? 'Ano' : 'Ne' ?>
                </span>
            </p>
        <?php else: ?>
            <p>Chyba: <?= htmlspecialchars($dbStatus['message']) ?></p>
        <?php endif; ?>
    </div>
    
    <div class="section">
        <h2>Server</h2>
        <table>
            <?php foreach ($serverInfo as $key => $value): ?>
                <tr>
                    <td><?= htmlspecialchars($key) ?></td>
                    <td><?= htmlspecialchars($value) ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>
    
    <div class="section">
        <h2>Kompletní odpověď</h2>
        <pre><?= json_encode($response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) ?></pre>
    </div>
</body>
</html>
