<?php
/**
 * Skript pro testování vyhledávání produktů
 */

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Spuštění session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test vyhledávání produktů</h1>";
    echo "<p style='color: green;'>✓ Připojení k databázi bylo úspěšné.</p>";
    
    // Kontrola přihlášení
    if (!isLoggedIn()) {
        echo "<p style='color: red;'>✗ Uživatel není přihl<PERSON>en. Pro testování vyhledávání produktů je nutné být přihlášen.</p>";
        echo "<p><a href='test_auth.php'>Přejít na stránku pro přihlášení</a></p>";
    } else {
        echo "<p style='color: green;'>✓ Uživatel je přihlášen jako: " . htmlspecialchars($_SESSION['user']['username']) . " (role: " . htmlspecialchars($_SESSION['user']['role']) . ")</p>";
        
        // Formulář pro vyhledávání produktů
        echo "<h2>Vyhledávání produktů</h2>";
        echo "<form method='get'>";
        echo "<p><label>Hledat: <input type='text' name='search' value='" . htmlspecialchars($_GET['search'] ?? '') . "'></label></p>";
        echo "<p>Hledat podle:</p>";
        echo "<p>";
        echo "<label><input type='checkbox' name='search_by[]' value='code' " . (isset($_GET['search_by']) && in_array('code', $_GET['search_by']) ? 'checked' : '') . "> EAN kód</label> ";
        echo "<label><input type='checkbox' name='search_by[]' value='name' " . (isset($_GET['search_by']) && in_array('name', $_GET['search_by']) ? 'checked' : '') . "> Název</label> ";
        echo "<label><input type='checkbox' name='search_by[]' value='category' " . (isset($_GET['search_by']) && in_array('category', $_GET['search_by']) ? 'checked' : '') . "> Kategorie</label>";
        echo "</p>";
        echo "<p><input type='submit' value='Hledat'></p>";
        echo "</form>";
        
        // Zpracování vyhledávání
        if (isset($_GET['search']) && !empty($_GET['search'])) {
            $search = $_GET['search'];
            $searchBy = $_GET['search_by'] ?? ['code', 'name'];
            
            // Sestavení SQL dotazu
            $sql = "
                SELECT 
                    p.id,
                    p.code AS ean_code,
                    p.name AS product_name,
                    c.name AS category,
                    p.pricebuy,
                    t.rate AS tax_rate,
                    p.pricesell,
                    COALESCE(s.units, 0) AS current_stock
                FROM 
                    products p
                LEFT JOIN 
                    categories c ON p.category = c.id
                LEFT JOIN 
                    taxes t ON p.taxcat = t.id
                LEFT JOIN 
                    stockcurrent s ON p.id = s.product
                WHERE 
            ";
            
            $conditions = [];
            $params = [];
            
            if (in_array('code', $searchBy)) {
                $conditions[] = "p.code LIKE :code";
                $params['code'] = "%$search%";
            }
            
            if (in_array('name', $searchBy)) {
                $conditions[] = "p.name LIKE :name";
                $params['name'] = "%$search%";
            }
            
            if (in_array('category', $searchBy)) {
                $conditions[] = "c.name LIKE :category";
                $params['category'] = "%$search%";
            }
            
            $sql .= "(" . implode(" OR ", $conditions) . ")";
            $sql .= " ORDER BY p.name";
            
            // Provedení dotazu
            $stmt = $pdo->prepare($sql);
            $stmt->execute($params);
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h2>Výsledky vyhledávání</h2>";
            echo "<p>Hledaný výraz: " . htmlspecialchars($search) . "</p>";
            echo "<p>Hledáno podle: " . htmlspecialchars(implode(", ", $searchBy)) . "</p>";
            
            if (empty($products)) {
                echo "<p>Nebyly nalezeny žádné produkty.</p>";
            } else {
                echo "<p>Nalezeno " . count($products) . " produktů.</p>";
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>EAN kód</th><th>Název produktu</th><th>Kategorie</th><th>Nákupní cena</th><th>DPH</th><th>Prodejní cena</th><th>Aktuální stav</th><th>Akce</th></tr>";
                
                foreach ($products as $product) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($product['ean_code']) . "</td>";
                    echo "<td>" . htmlspecialchars($product['product_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($product['category'] ?? '') . "</td>";
                    echo "<td>" . htmlspecialchars($product['pricebuy']) . "</td>";
                    echo "<td>" . htmlspecialchars($product['tax_rate'] ?? '') . "%</td>";
                    echo "<td>" . htmlspecialchars($product['pricesell']) . "</td>";
                    echo "<td>" . htmlspecialchars($product['current_stock']) . "</td>";
                    echo "<td>";
                    echo "<form method='post'>";
                    echo "<input type='hidden' name='product_id' value='" . htmlspecialchars($product['id']) . "'>";
                    echo "<input type='hidden' name='ean_code' value='" . htmlspecialchars($product['ean_code']) . "'>";
                    echo "<input type='submit' name='add_to_inventory' value='Přidat do inventury'>";
                    echo "</form>";
                    echo "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            }
        }
        
        // Zpracování přidání produktu do inventury
        if (isset($_POST['add_to_inventory'])) {
            $productId = $_POST['product_id'];
            $eanCode = $_POST['ean_code'];
            
            // Získání aktivních inventurních relací
            $stmt = $pdo->prepare("
                SELECT 
                    s.id, 
                    s.start_time, 
                    s.status, 
                    u.username
                FROM 
                    inventory_sessions s
                JOIN 
                    inventory_users u ON s.user_id = u.id
                WHERE 
                    s.status = 'active'
                ORDER BY 
                    s.start_time DESC
            ");
            
            $stmt->execute();
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h2>Přidání produktu do inventury</h2>";
            echo "<p>Produkt: " . htmlspecialchars($eanCode) . "</p>";
            
            if (empty($sessions)) {
                echo "<p>Žádné aktivní inventurní relace nebyly nalezeny. Nejprve vytvořte novou inventurní relaci.</p>";
                echo "<p><a href='test_inventory.php'>Přejít na stránku pro vytvoření inventurní relace</a></p>";
            } else {
                echo "<h3>Vyberte inventurní relaci</h3>";
                echo "<form method='post'>";
                echo "<input type='hidden' name='product_id' value='" . htmlspecialchars($productId) . "'>";
                echo "<input type='hidden' name='ean_code' value='" . htmlspecialchars($eanCode) . "'>";
                echo "<p><select name='session_id'>";
                
                foreach ($sessions as $session) {
                    echo "<option value='" . htmlspecialchars($session['id']) . "'>" . htmlspecialchars($session['id']) . " - " . htmlspecialchars($session['start_time']) . " (" . htmlspecialchars($session['username']) . ")</option>";
                }
                
                echo "</select></p>";
                echo "<p><label>Zadané množství: <input type='number' name='zadane_mnozstvi' step='0.001' required></label></p>";
                echo "<p><input type='submit' name='confirm_add_to_inventory' value='Přidat do inventury'></p>";
                echo "</form>";
            }
        }
        
        // Zpracování potvrzení přidání produktu do inventury
        if (isset($_POST['confirm_add_to_inventory'])) {
            $productId = $_POST['product_id'];
            $eanCode = $_POST['ean_code'];
            $sessionId = $_POST['session_id'];
            $zadaneMnozstvi = $_POST['zadane_mnozstvi'];
            
            try {
                // Přidání záznamu do inventory_entries
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_entries (
                        session_id, 
                        product_id, 
                        ean_code, 
                        user_id, 
                        zadane_mnozstvi
                    ) VALUES (
                        :session_id, 
                        :product_id, 
                        :ean_code, 
                        :user_id, 
                        :zadane_mnozstvi
                    )
                ");
                
                $stmt->execute([
                    'session_id' => $sessionId,
                    'product_id' => $productId,
                    'ean_code' => $eanCode,
                    'user_id' => $_SESSION['user']['id'],
                    'zadane_mnozstvi' => $zadaneMnozstvi
                ]);
                
                echo "<h2>Produkt byl přidán do inventury</h2>";
                echo "<p style='color: green;'>✓ Produkt byl úspěšně přidán do inventury.</p>";
                
                // Aktualizace celkového zadaného množství v inventory_totals
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_totals (
                        session_id, 
                        product_id, 
                        total_zadane_mnozstvi
                    ) VALUES (
                        :session_id, 
                        :product_id, 
                        :zadane_mnozstvi
                    ) ON DUPLICATE KEY UPDATE
                        total_zadane_mnozstvi = total_zadane_mnozstvi + :zadane_mnozstvi
                ");
                
                $stmt->execute([
                    'session_id' => $sessionId,
                    'product_id' => $productId,
                    'zadane_mnozstvi' => $zadaneMnozstvi
                ]);
                
                echo "<p style='color: green;'>✓ Celkové zadané množství bylo aktualizováno.</p>";
                echo "<p><a href='test_inventory.php'>Přejít na stránku pro správu inventury</a></p>";
            } catch (PDOException $e) {
                echo "<h2>Chyba při přidávání produktu do inventury</h2>";
                echo "<p style='color: red;'>✗ Došlo k chybě při přidávání produktu do inventury: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
