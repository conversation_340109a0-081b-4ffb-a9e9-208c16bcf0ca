<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test opraven<PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .form-group {
            margin: 10px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .result-area {
            margin-top: 10px;
            padding: 10px;
            background: #fff;
            border: 1px solid #ccc;
            border-radius: 3px;
            min-height: 50px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test opraveného přihlašování</h1>
        
        <div class="test-section">
            <h2>ℹ️ Informace o změnách</h2>
            <div class="info result-area">
Aplikace byla upravena pro použití jednoduchého API endpointu:
- api/auth.php → api/simple_auth.php
- Odstraněna složitá validace
- Zjednodušeno zpracování chyb

Testovací přihlašovací údaje:
- admin / admin123 (administrátor)
- manager / manager123 (manažer)  
- user1 / user123 (běžný uživatel)
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 Test API endpointů</h2>
            <button class="btn" onclick="testEndpoint('simple_auth')">Test simple_auth.php</button>
            <button class="btn" onclick="testEndpoint('auth')">Test auth.php (původní)</button>
            <div id="endpoint-result" class="result-area">Klikněte na tlačítko pro test...</div>
        </div>

        <div class="test-section">
            <h2>🔑 Test přihlášení pomocí upraveného API</h2>
            <div class="form-group">
                <label for="test-username">Uživatelské jméno:</label>
                <input type="text" id="test-username" value="admin">
            </div>
            <div class="form-group">
                <label for="test-password">Heslo:</label>
                <input type="password" id="test-password" value="admin123">
            </div>
            <button class="btn btn-success" onclick="testLogin()">Test přihlášení</button>
            <button class="btn" onclick="testCheck()">Test kontroly stavu</button>
            <div id="login-result" class="result-area">Zadejte údaje a klikněte na tlačítko...</div>
        </div>

        <div class="test-section">
            <h2>🌐 Test hlavní aplikace</h2>
            <p>Aplikace byla upravena pro použití simple_auth.php API. Nyní zkuste:</p>
            <a href="index.html" class="btn btn-success">Otevřít hlavní aplikaci</a>
            <div class="info result-area">
Po kliknutí na tlačítko se otevře hlavní aplikace.
Zkuste se přihlásit s údaji: admin / admin123

Pokud se přihlášení nepodaří, zkontrolujte:
1. Konzoli prohlížeče (F12) pro chybové zprávy
2. Zda se volá správné API (simple_auth.php)
3. Zda jsou v databázi správní uživatelé
            </div>
        </div>

        <div class="test-section">
            <h2>🔗 Další nástroje</h2>
            <a href="fix_login_issue.php" class="btn btn-warning">Oprava uživatelů</a>
            <a href="test_simple_auth.php" class="btn btn-warning">Test simple API</a>
            <a href="debug_login_issue.php" class="btn btn-warning">Debug přihlašování</a>
        </div>
    </div>

    <script>
        async function testEndpoint(type) {
            const resultElement = document.getElementById('endpoint-result');
            resultElement.className = 'result-area';
            resultElement.textContent = `Testování ${type} endpointu...`;

            const url = type === 'simple_auth' ? 'api/simple_auth.php?action=test' : 'api/auth.php?action=check';

            try {
                const response = await fetch(url);
                const text = await response.text();
                
                resultElement.className = response.ok ? 'result-area success' : 'result-area error';
                
                try {
                    const json = JSON.parse(text);
                    resultElement.textContent = `${type.toUpperCase()} API Test:\n`;
                    resultElement.textContent += `Status: ${response.status}\n`;
                    resultElement.textContent += JSON.stringify(json, null, 2);
                } catch (e) {
                    resultElement.textContent = `${type.toUpperCase()} API Test:\n`;
                    resultElement.textContent += `Status: ${response.status}\n`;
                    resultElement.textContent += text;
                }
                
            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba při testování ${type}: ${error.message}`;
            }
        }

        async function testLogin() {
            const username = document.getElementById('test-username').value;
            const password = document.getElementById('test-password').value;
            const resultElement = document.getElementById('login-result');
            
            if (!username || !password) {
                alert('Zadejte uživatelské jméno a heslo');
                return;
            }
            
            resultElement.className = 'result-area';
            resultElement.textContent = 'Testování přihlášení...';

            try {
                const response = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                const text = await response.text();
                
                resultElement.className = response.ok ? 'result-area success' : 'result-area error';
                
                try {
                    const json = JSON.parse(text);
                    resultElement.textContent = `Přihlášení ${username}/${password}:\n`;
                    resultElement.textContent += `Status: ${response.status}\n`;
                    resultElement.textContent += JSON.stringify(json, null, 2);
                } catch (e) {
                    resultElement.textContent = `Přihlášení ${username}/${password}:\n`;
                    resultElement.textContent += `Status: ${response.status}\n`;
                    resultElement.textContent += text;
                }
                
            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba při přihlášení: ${error.message}`;
            }
        }

        async function testCheck() {
            const resultElement = document.getElementById('login-result');
            resultElement.className = 'result-area';
            resultElement.textContent = 'Kontrola stavu přihlášení...';

            try {
                const response = await fetch('api/simple_auth.php?action=check');
                const text = await response.text();
                
                resultElement.className = response.ok ? 'result-area success' : 'result-area error';
                
                try {
                    const json = JSON.parse(text);
                    resultElement.textContent = `Kontrola stavu:\n`;
                    resultElement.textContent += `Status: ${response.status}\n`;
                    resultElement.textContent += JSON.stringify(json, null, 2);
                } catch (e) {
                    resultElement.textContent = `Kontrola stavu:\n`;
                    resultElement.textContent += `Status: ${response.status}\n`;
                    resultElement.textContent += text;
                }
                
            } catch (error) {
                resultElement.className = 'result-area error';
                resultElement.textContent = `Chyba při kontrole: ${error.message}`;
            }
        }

        // Automatický test při načtení stránky
        window.onload = function() {
            testEndpoint('simple_auth');
        };
    </script>
</body>
</html>
