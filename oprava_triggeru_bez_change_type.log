[2025-05-22 19:46:20] Připojení k <PERSON>b<PERSON>zi úspěšn<PERSON>
[2025-05-22 19:46:23] Trigger update_inventory_entries_after_stockcurrent_update existuje
[2025-05-22 19:46:23] Kód triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference, change_type)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference, 'UPDATE');

                -- Pokud je roz<PERSON><PERSON><PERSON> (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- <PERSON><PERSON><PERSON> prode<PERSON> (difference > 0) ode<PERSON><PERSON><PERSON><PERSON><PERSON> zadan<PERSON> množství
                    -- <PERSON><PERSON><PERSON> na<PERSON> (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 19:46:23] Existující trigger byl odstraněn
[2025-05-22 19:46:23] Struktura tabulky stockcurrent_log:
[2025-05-22 19:46:23]   id (int(11))
[2025-05-22 19:46:23]   product_id (varchar(50))
[2025-05-22 19:46:23]   old_units (double)
[2025-05-22 19:46:23]   new_units (double)
[2025-05-22 19:46:23]   difference (double)
[2025-05-22 19:46:23]   original_difference (double)
[2025-05-22 19:46:23]   created (timestamp)
[2025-05-22 19:46:23]   change_type (varchar(50))
[2025-05-22 19:46:24] Nový trigger byl úspěšně vytvořen
[2025-05-22 19:46:24] Trigger update_inventory_entries_after_stockcurrent_update byl úspěšně vytvořen
[2025-05-22 19:46:24] Kód nového triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 19:46:24] Oprava dokončena
