/**
 * Funkce pro aktualizaci zadaného množství v celkové inventuře
 */

// Funkce pro aktualizaci zadaného množství podle aktuálního stavu
function updateInventoryQuantity(productId, quantity) {
    // Zobrazení potvrzovacího dialogu
    if (!confirm('Opravdu chcete aktualizovat zadané množství pro produkt ' + productId + ' na ' + quantity + '?')) {
        return;
    }
    
    // Vytvoření dat pro požadavek
    const data = {
        product_id: productId,
        quantity: quantity
    };
    
    // Odeslání požadavku na server
    fetch('api/update_inventory_quantity.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Zadané množství bylo úspěšně aktualizováno.');
            // Obnovení stránky
            location.reload();
        } else {
            alert('Chyba: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Chyba:', error);
        alert('Došlo k chybě při komunikaci se serverem.');
    });
}

// Funkce pro aktualizaci zadaného množství podle aktuálního stavu pro všechny produkty
function updateAllInventoryQuantities() {
    // Zobrazení potvrzovacího dialogu
    if (!confirm('Opravdu chcete aktualizovat zadané množství pro všechny produkty podle aktuálního stavu?')) {
        return;
    }
    
    // Získání všech produktů v tabulce
    const rows = document.querySelectorAll('table.inventory-table tbody tr');
    
    // Počítadlo úspěšně aktualizovaných produktů
    let successCount = 0;
    let totalCount = rows.length;
    let currentIndex = 0;
    
    // Funkce pro aktualizaci jednoho produktu
    function updateNextProduct() {
        if (currentIndex >= totalCount) {
            // Všechny produkty byly aktualizovány
            alert('Aktualizace dokončena. Úspěšně aktualizováno ' + successCount + ' z ' + totalCount + ' produktů.');
            // Obnovení stránky
            location.reload();
            return;
        }
        
        const row = rows[currentIndex];
        const productId = row.getAttribute('data-product-id');
        const currentStock = row.querySelector('.current-stock').textContent.trim();
        
        // Aktualizace produktu
        fetch('api/update_inventory_quantity.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: currentStock
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                successCount++;
            }
            
            // Pokračování na další produkt
            currentIndex++;
            updateNextProduct();
        })
        .catch(error => {
            console.error('Chyba:', error);
            
            // Pokračování na další produkt i v případě chyby
            currentIndex++;
            updateNextProduct();
        });
    }
    
    // Spuštění aktualizace
    updateNextProduct();
}

// Přidání posluchače události pro tlačítko "Aktualizovat zadané množství podle aktuálního stavu"
document.addEventListener('DOMContentLoaded', function() {
    // Přidání posluchače události pro tlačítko "Aktualizovat zadané množství podle aktuálního stavu"
    const updateButton = document.querySelector('.update-inventory-button');
    if (updateButton) {
        updateButton.addEventListener('click', function(event) {
            event.preventDefault();
            updateAllInventoryQuantities();
        });
    }
    
    // Přidání posluchačů událostí pro tlačítka "Aktualizovat" u jednotlivých produktů
    const updateProductButtons = document.querySelectorAll('.update-product-button');
    updateProductButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            event.preventDefault();
            const productId = this.getAttribute('data-product-id');
            const currentStock = this.getAttribute('data-current-stock');
            updateInventoryQuantity(productId, currentStock);
        });
    });
});
