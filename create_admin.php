<?php
/**
 * Vytvoření admin uživatele
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Vytvoř<PERSON>í admin uživatele</h1>";

try {
    // Načtení potřebných souborů
    require_once __DIR__ . '/utils/database.php';
    require_once __DIR__ . '/utils/auth.php';
    
    echo "<h2>1. Připojení k <PERSON>b<PERSON></h2>";
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení úspěšné</p>";
    
    echo "<h2>2. Kontrola tabulek</h2>";
    ensureTablesExist();
    echo "<p style='color: green;'>✓ Tabulky existují</p>";
    
    echo "<h2>3. Kontrola existujícího admin uživatele</h2>";
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
    $stmt->execute();
    $existingAdmin = $stmt->fetch();
    
    if ($existingAdmin) {
        echo "<p style='color: orange;'>⚠ Admin uživatel již existuje</p>";
        echo "<pre>" . print_r($existingAdmin, true) . "</pre>";
        
        echo "<h3>Aktualizace hesla admin uživatele</h3>";
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("UPDATE inventory_users SET password = ? WHERE username = 'admin'");
        $result = $stmt->execute([$hashedPassword]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Heslo admin uživatele bylo aktualizováno</p>";
        } else {
            echo "<p style='color: red;'>✗ Nepodařilo se aktualizovat heslo</p>";
        }
    } else {
        echo "<p style='color: blue;'>ℹ Admin uživatel neexistuje, vytvářím...</p>";
        
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO inventory_users (username, password, role, full_name, email, active) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $result = $stmt->execute([
            'admin',
            $hashedPassword,
            'admin',
            'Administrátor',
            '<EMAIL>',
            1
        ]);
        
        if ($result) {
            echo "<p style='color: green;'>✓ Admin uživatel byl vytvořen</p>";
        } else {
            echo "<p style='color: red;'>✗ Nepodařilo se vytvořit admin uživatele</p>";
        }
    }
    
    echo "<h2>4. Finální kontrola</h2>";
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✓ Admin uživatel existuje</p>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Username</th><th>Role</th><th>Full Name</th><th>Active</th></tr>";
        echo "<tr>";
        echo "<td>{$admin['id']}</td>";
        echo "<td>{$admin['username']}</td>";
        echo "<td>{$admin['role']}</td>";
        echo "<td>{$admin['full_name']}</td>";
        echo "<td>" . ($admin['active'] ? 'Ano' : 'Ne') . "</td>";
        echo "</tr>";
        echo "</table>";
    }
    
    echo "<h2>5. Test přihlášení</h2>";
    $user = authenticateUser('admin', 'admin123');
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení admin/admin123 funguje!</p>";
        echo "<pre>" . print_r($user, true) . "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Přihlášení admin/admin123 nefunguje</p>";
        
        // Zkusíme různé kombinace
        echo "<h3>Testování různých hesel:</h3>";
        
        $testPasswords = ['admin123', 'admin', '', '123'];
        foreach ($testPasswords as $testPassword) {
            $testUser = authenticateUser('admin', $testPassword);
            $status = $testUser ? "✓ funguje" : "✗ nefunguje";
            echo "<p>admin + '{$testPassword}': {$status}</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
