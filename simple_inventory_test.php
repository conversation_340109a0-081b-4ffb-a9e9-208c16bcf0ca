<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> test inventury
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Jednoduchý test inventury</h1>";

// Načtení potřebných souborů
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    echo "<h2>1. Připojení k datab<PERSON>zi</h2>";
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení úspěšné</p>";
    
    echo "<h2>2. Kontrola tabulek</h2>";
    ensureTablesExist();
    echo "<p style='color: green;'>✓ Tabulky existují</p>";
    
    echo "<h2>3. Kontrola admin uživatele</h2>";
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "<p style='color: orange;'>⚠ Vytvářím admin uživatele...</p>";
        $hashedPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO inventory_users (username, password, role, full_name, email, active) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute(['admin', $hashedPassword, 'admin', 'Administrátor', '<EMAIL>', 1]);
        $adminId = $pdo->lastInsertId();
        echo "<p style='color: green;'>✓ Admin uživatel vytvořen s ID: $adminId</p>";
    } else {
        echo "<p style='color: green;'>✓ Admin uživatel existuje s ID: {$admin['id']}</p>";
        $adminId = $admin['id'];
    }
    
    echo "<h2>4. Simulace přihlášení</h2>";
    session_start();
    $_SESSION['user_id'] = $adminId;
    $_SESSION['username'] = 'admin';
    $_SESSION['role'] = 'admin';
    echo "<p style='color: green;'>✓ Uživatel simulovaně přihlášen</p>";
    
    echo "<h2>5. Test vytvoření inventury</h2>";
    $stmt = $pdo->prepare("
        INSERT INTO inventory_sessions (user_id) 
        VALUES (?)
    ");
    $stmt->execute([$adminId]);
    $sessionId = $pdo->lastInsertId();
    
    if ($sessionId) {
        echo "<p style='color: green;'>✓ Inventura vytvořena s ID: $sessionId</p>";
    } else {
        echo "<p style='color: red;'>✗ Nepodařilo se vytvořit inventuru</p>";
    }
    
    echo "<h2>6. Test API volání</h2>";
    
    // Simulace GET požadavku
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET = [];
    
    ob_start();
    include __DIR__ . '/api/inventory.php';
    $getResponse = ob_get_clean();
    
    echo "<h3>GET api/inventory.php:</h3>";
    echo "<pre>" . htmlspecialchars($getResponse) . "</pre>";
    
    // Simulace POST požadavku
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET = [];
    
    // Simulace JSON vstupu
    $jsonInput = json_encode([]);
    file_put_contents('php://temp', $jsonInput);
    
    ob_start();
    include __DIR__ . '/api/inventory.php';
    $postResponse = ob_get_clean();
    
    echo "<h3>POST api/inventory.php:</h3>";
    echo "<pre>" . htmlspecialchars($postResponse) . "</pre>";
    
    echo "<h2>7. Aktuální inventury</h2>";
    $stmt = $pdo->query("
        SELECT 
            s.id, 
            s.start_time, 
            s.status, 
            u.username 
        FROM inventory_sessions s 
        JOIN inventory_users u ON s.user_id = u.id 
        ORDER BY s.id DESC 
        LIMIT 10
    ");
    $sessions = $stmt->fetchAll();
    
    if ($sessions) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Čas vytvoření</th><th>Stav</th><th>Uživatel</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['id']}</td>";
            echo "<td>{$session['start_time']}</td>";
            echo "<td>{$session['status']}</td>";
            echo "<td>{$session['username']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Žádné inventury</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='test_create_inventory.html'>Test přes JavaScript</a></p>";
echo "<p><a href='index.html'>Hlavní aplikace</a></p>";
?>
