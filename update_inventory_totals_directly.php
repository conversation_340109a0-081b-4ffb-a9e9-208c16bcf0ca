<?php
/**
 * Přímá aktualizace zadaného množství v celkové inventuře
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Přímá aktualizace zadaného množství v celkové inventuře</h1>";
    
    // Výběr náhodného produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logMessage("Nebyl nalezen žádný produkt s kladným množstvím na skladě", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    logMessage("Vybrán produkt pro test: " . $product['name'] . " (ID: " . $product['id'] . ", Množství na skladě: " . $product['units'] . ")");
    
    // Nalezení aktivní inventurní relace
    $stmt = $pdo->query("
        SELECT id
        FROM inventory_sessions
        WHERE status = 'active'
        LIMIT 1
    ");
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        logMessage("Nebyla nalezena žádná aktivní inventurní relace", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    logMessage("Nalezena aktivní inventurní relace s ID: " . $session['id']);
    
    // Zjištění aktuálního zadaného množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT id, total_zadane_mnozstvi
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $total = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$total) {
        logMessage("Nebyl nalezen žádný záznam v celkové inventuře pro tento produkt", true);
        
        // Vytvoření nového záznamu v celkové inventuře
        $stmt = $pdo->prepare("
            INSERT INTO inventory_totals (product_id, session_id, total_zadane_mnozstvi, created_at, last_updated)
            VALUES (?, ?, 0, NOW(), NOW())
        ");
        $stmt->execute([$product['id'], $session['id']]);
        
        logMessage("Vytvořen nový záznam v celkové inventuře");
        
        // Zjištění ID nově vytvořeného záznamu
        $stmt = $pdo->prepare("
            SELECT id, total_zadane_mnozstvi
            FROM inventory_totals
            WHERE product_id = ? AND session_id = ?
        ");
        $stmt->execute([$product['id'], $session['id']]);
        $total = $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    $totalId = $total['id'];
    $currentTotal = $total['total_zadane_mnozstvi'];
    
    logMessage("Aktuální zadané množství v celkové inventuře: " . $currentTotal);
    
    // Simulace prodeje produktu (snížení zadaného množství v celkové inventuře)
    $unitsToSell = 1;
    $newTotal = $currentTotal - $unitsToSell;
    
    logMessage("Simuluji prodej " . $unitsToSell . " kusů produktu " . $product['name']);
    
    $stmt = $pdo->prepare("
        UPDATE inventory_totals
        SET total_zadane_mnozstvi = ?,
            last_updated = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$newTotal, $totalId]);
    
    logMessage("Zadané množství v celkové inventuře bylo aktualizováno");
    
    // Kontrola, zda se zadané množství změnilo
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi
        FROM inventory_totals
        WHERE id = ?
    ");
    $stmt->execute([$totalId]);
    $updatedTotal = $stmt->fetch(PDO::FETCH_COLUMN);
    
    logMessage("Nové zadané množství v celkové inventuře: " . $updatedTotal);
    
    if ($updatedTotal == $newTotal) {
        logMessage("Aktualizace byla úspěšná!");
    } else {
        logMessage("Aktualizace selhala!", true);
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
