<?php
/**
 * Kontrola a vytvoření potřebných tabulek
 */

header('Content-Type: application/json');
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Načtení potřebných souborů
    require_once __DIR__ . '/utils/database.php';
    require_once __DIR__ . '/utils/auth.php';
    
    // Kontrola a vytvoření tabulek
    ensureTablesExist();
    
    // Odpověď
    echo json_encode([
        'success' => true,
        'message' => 'Tabulky byly úspěšně zkontrolovány a vytvořeny'
    ]);
    
} catch (Exception $e) {
    error_log("check_tables.php - chyba: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'error' => 'Chyba při kontrole tabulek: ' . $e->getMessage()
    ]);
}
?>
