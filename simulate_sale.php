<?php
/**
 * Simulace prodeje a kontrola aktualizace zadaného množství v celkové inventuře
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování do souboru
function logToFile($message) {
    $logFile = __DIR__ . '/simulate_sale.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logToFile("Připojení k databázi úspěšné");
    
    // Výběr náhodného produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logToFile("Nebyl nalezen žádný produkt s kladným množstvím na skladě");
        echo "Test selhal. Zkontrolujte logovací soubor: " . __DIR__ . '/simulate_sale.log';
        exit;
    }
    
    logToFile("Vybrán produkt pro test: " . $product['name'] . " (ID: " . $product['id'] . ", Množství na skladě: " . $product['units'] . ")");
    
    // Nalezení aktivní inventurní relace
    $stmt = $pdo->query("
        SELECT id
        FROM inventory_sessions
        WHERE status = 'active'
        LIMIT 1
    ");
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        logToFile("Nebyla nalezena žádná aktivní inventurní relace");
        echo "Test selhal. Zkontrolujte logovací soubor: " . __DIR__ . '/simulate_sale.log';
        exit;
    }
    
    logToFile("Nalezena aktivní inventurní relace s ID: " . $session['id']);
    
    // Zjištění aktuálního zadaného množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $total = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $currentTotal = $total ? $total['total_zadane_mnozstvi'] : 0;
    logToFile("Aktuální zadané množství v celkové inventuře: " . $currentTotal);
    
    // Simulace prodeje produktu (vytvoření záznamu v tabulce ticketlines)
    $unitsToSell = 1;
    
    logToFile("Simuluji prodej " . $unitsToSell . " kusů produktu " . $product['name']);
    
    // Vytvoření nového ticketu
    $stmt = $pdo->query("
        INSERT INTO tickets (id, tickettype, ticketid, person, status)
        VALUES (UUID(), 0, 1, 'admin', 0)
    ");
    $ticketId = $pdo->lastInsertId();
    
    if (!$ticketId) {
        // Pokud lastInsertId() nefunguje (např. při použití UUID), získáme ID jiným způsobem
        $stmt = $pdo->query("
            SELECT id
            FROM tickets
            ORDER BY created DESC
            LIMIT 1
        ");
        $ticket = $stmt->fetch(PDO::FETCH_ASSOC);
        $ticketId = $ticket['id'];
    }
    
    logToFile("Vytvořen nový ticket s ID: " . $ticketId);
    
    // Vytvoření nového záznamu v tabulce ticketlines
    $stmt = $pdo->prepare("
        INSERT INTO ticketlines (ticket, line, product, units, price, taxid)
        VALUES (?, 1, ?, ?, 100, '001')
    ");
    $stmt->execute([$ticketId, $product['id'], $unitsToSell]);
    
    logToFile("Vytvořen nový záznam v tabulce ticketlines");
    
    // Aktualizace množství na skladě
    $newUnits = $product['units'] - $unitsToSell;
    
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = ?
        WHERE product = ?
    ");
    $stmt->execute([$newUnits, $product['id']]);
    
    logToFile("Aktualizováno množství na skladě na " . $newUnits);
    
    // Kontrola, zda se změnilo zadané množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $newTotal = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $newTotalValue = $newTotal ? $newTotal['total_zadane_mnozstvi'] : 0;
    logToFile("Nové zadané množství v celkové inventuře: " . $newTotalValue);
    
    // Kontrola, zda se zadané množství změnilo o správnou hodnotu
    $expectedTotal = $currentTotal - $unitsToSell;
    if ($newTotalValue == $expectedTotal) {
        logToFile("Test úspěšný! Zadané množství se změnilo o správnou hodnotu.");
        logToFile("Očekávaná hodnota: " . $expectedTotal . ", Skutečná hodnota: " . $newTotalValue);
    } else {
        logToFile("Test selhal! Zadané množství se nezměnilo o správnou hodnotu.");
        logToFile("Očekávaná hodnota: " . $expectedTotal . ", Skutečná hodnota: " . $newTotalValue);
        
        // Pokud test selhal, zkusíme aktualizovat zadané množství ručně
        logToFile("Zkouším aktualizovat zadané množství ručně");
        
        $stmt = $pdo->prepare("
            UPDATE inventory_totals
            SET total_zadane_mnozstvi = total_zadane_mnozstvi - ?,
                last_updated = NOW()
            WHERE product_id = ? AND session_id = ?
        ");
        $stmt->execute([$unitsToSell, $product['id'], $session['id']]);
        
        // Kontrola, zda se zadané množství změnilo po ruční aktualizaci
        $stmt = $pdo->prepare("
            SELECT total_zadane_mnozstvi
            FROM inventory_totals
            WHERE product_id = ? AND session_id = ?
        ");
        $stmt->execute([$product['id'], $session['id']]);
        $manualTotal = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $manualTotalValue = $manualTotal ? $manualTotal['total_zadane_mnozstvi'] : 0;
        logToFile("Zadané množství po ruční aktualizaci: " . $manualTotalValue);
        
        if ($manualTotalValue == $expectedTotal) {
            logToFile("Ruční aktualizace byla úspěšná!");
        } else {
            logToFile("Ruční aktualizace selhala!");
        }
    }
    
    logToFile("Test dokončen");
    
    // Výpis cesty k logovacímu souboru
    echo "Test byl dokončen. Logovací soubor: " . __DIR__ . '/simulate_sale.log';
    
} catch (PDOException $e) {
    logToFile("Chyba při připojení k databázi: " . $e->getMessage());
    echo "Došlo k chybě. Zkontrolujte logovací soubor: " . __DIR__ . '/simulate_sale.log';
}
?>
