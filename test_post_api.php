<?php
/**
 * Test POST požadavku na API
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🧪 Test POST API</h1>";

echo "<h2>🌐 Test GET požadavku</h2>";

$getUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions";

$getContext = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => 'Content-Type: application/json',
        'timeout' => 10
    ]
]);

echo "<p><strong>URL:</strong> <code>" . htmlspecialchars($getUrl) . "</code></p>";

$getResponse = @file_get_contents($getUrl, false, $getContext);

if ($getResponse === false) {
    $error = error_get_last();
    logMessage("❌ GET požadavek selhal: " . ($error['message'] ?? 'Neznámá chyba'), true);
    
    if (isset($http_response_header)) {
        echo "<h4>HTTP Response Headers:</h4>";
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        foreach ($http_response_header as $header) {
            echo htmlspecialchars($header) . "\n";
        }
        echo "</pre>";
    }
} else {
    $getData = json_decode($getResponse, true);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        if (isset($getData['error'])) {
            logMessage("❌ GET API vrátilo chybu: " . $getData['error'], true);
        } else {
            logMessage("✅ GET API volání úspěšné");
            
            if (isset($getData['sessions'])) {
                logMessage("📊 Vráceno " . count($getData['sessions']) . " relací");
            }
        }
        
        echo "<h4>GET Odpověď:</h4>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(json_encode($getData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "</pre>";
    } else {
        logMessage("❌ GET - Chyba při parsování JSON", true);
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($getResponse);
        echo "</pre>";
    }
}

echo "<hr>";

echo "<h2>🌐 Test POST požadavku</h2>";

$postUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions";
$postData = json_encode([]);

$postContext = stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Content-Length: ' . strlen($postData)
        ],
        'content' => $postData,
        'timeout' => 10
    ]
]);

echo "<p><strong>URL:</strong> <code>" . htmlspecialchars($postUrl) . "</code></p>";
echo "<p><strong>POST data:</strong> <code>" . htmlspecialchars($postData) . "</code></p>";

$postResponse = @file_get_contents($postUrl, false, $postContext);

if ($postResponse === false) {
    $error = error_get_last();
    logMessage("❌ POST požadavek selhal: " . ($error['message'] ?? 'Neznámá chyba'), true);
    
    if (isset($http_response_header)) {
        echo "<h4>HTTP Response Headers:</h4>";
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        foreach ($http_response_header as $header) {
            echo htmlspecialchars($header) . "\n";
        }
        echo "</pre>";
    }
} else {
    $postDataResponse = json_decode($postResponse, true);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        if (isset($postDataResponse['error'])) {
            logMessage("❌ POST API vrátilo chybu: " . $postDataResponse['error'], true);
        } else {
            logMessage("✅ POST API volání úspěšné");
            
            if (isset($postDataResponse['session_id'])) {
                logMessage("📊 Vytvořena relace s ID: " . $postDataResponse['session_id']);
            }
        }
        
        echo "<h4>POST Odpověď:</h4>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
        echo htmlspecialchars(json_encode($postDataResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "</pre>";
    } else {
        logMessage("❌ POST - Chyba při parsování JSON", true);
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars($postResponse);
        echo "</pre>";
    }
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='minimal_api_test.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Minimální test</a>";
echo "<a href='api_test_no_auth.php?action=test' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test bez auth</a>";
echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
echo "</p>";
?>
