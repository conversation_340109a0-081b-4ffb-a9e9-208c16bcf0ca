@echo off
del /f .htaccess
del /f add_last_updated_column.php
del /f add_logging_to_create_entry.php
del /f add_units_column.php
del /f add_units_direct.php
del /f add_units_to_all_tables_in_db.php
del /f add_units_to_all_tables.php
del /f aktualizace_podle_prodeju.php
del /f alternative_solution.php
del /f analyze_database_structure.php
del /f analyze_triggers.php
del /f api_test_dashboard.php
del /f api_test_no_auth.php
del /f auto_sync_after_sale.php
del /f auto_update_inventory_totals.php
del /f capture_sql_queries.php
del /f cleanup.php
del /f complete_diagnosis.php
del /f comprehensive_zadane_mnozstvi_test.php
del /f create_alternative_triggers.php
del /f create_correct_trigger.php
del /f create_correct_triggers.php
del /f create_inventory_stock_changes.php
del /f create_inventory_totals_table.php
del /f create_inventory_triggers.php
del /f create_inventory_update_trigger.php
del /f create_missing_tables.php
del /f create_new_trigger_approach.php
del /f create_previous_stock_table.php
del /f create_simple_trigger.php
del /f create_stock_changes_table.php
del /f create_sync_trigger.php
del /f create_tables.php
del /f create_test_inventory_data.php
del /f create_ticketlines_trigger_new.php
del /f create_ticketlines_trigger.php
del /f create_ticketlines_view.php
del /f create_ticketlines_with_units.php
del /f create_universal_auth.php
del /f create_views_with_units.php
del /f cron_sync_inventory.php
del /f cron_update_inventory.log
del /f cron_update_inventory.php
del /f deactivate_change_type_triggers.log
del /f deactivate_change_type_triggers.php
del /f debug_500_error.php
del /f debug_api_error.php
del /f debug_create_entry.php
del /f debug_inventory_totals_update.php
del /f debug_login_issue.php
del /f debug_login.php
del /f debug_stockcurrent_changes.php
del /f debug_stockcurrent_trigger.php
del /f debug_trigger_execution.php
del /f debug_trigger_file.php
del /f debug_trigger.log
del /f debug_triggers.php
del /f debug_unicenta_sales.php
del /f diagnose_inventory_update.php
del /f diagnose_unicenta_sales.php
del /f direct_sync_inventory_totals.php
del /f direct_update_inventory_quantity.php
del /f direct_update_inventory_totals.php
del /f direct_update_inventory.php
del /f emergency_debug.php
del /f emergency.log
del /f export_inventory.php
del /f final_fix_inventory_update.php
del /f final_solution.php
del /f fix_all_database_objects.php
del /f fix_all_products.php
del /f fix_all_triggers.php
del /f fix_and_test_triggers.php
del /f fix_automatic_update.php
del /f fix_broken_triggers.php
del /f fix_create_entry_complete.php
del /f fix_create_entry.php
del /f fix_create_session.php
del /f fix_diagnose_columns.php
del /f fix_double_deduction_final_solution.php
del /f fix_double_deduction_final.php
del /f fix_double_deduction.php
del /f fix_double_update_trigger.php
del /f fix_full_name_column.php
del /f fix_inventory_sessions_table.php
del /f fix_inventory_sessions.php
del /f fix_inventory_totals_sync.php
del /f fix_inventory_totals_trigger_file.php
del /f fix_inventory_totals_trigger_web.php
del /f fix_inventory_totals_trigger.php
del /f fix_inventory_totals.php
del /f fix_inventory_trigger_correct_update.php
del /f fix_inventory_trigger_no_update.php
del /f fix_inventory_trigger_total_only.php
del /f fix_inventory_triggers.php
del /f fix_inventory_update_triggers.php
del /f fix_log_tables.php
del /f fix_login_issue.php
del /f fix_sales_ticket_issue.php
del /f fix_session_auth.php
del /f fix_session_issues.php
del /f fix_status_column.php
del /f fix_stockcurrent_triggers_correct.php
del /f fix_stockcurrent_triggers.php
del /f fix_tables_direct.php
del /f fix_test_simulation.php
del /f fix_ticketlines_log.php
del /f fix_ticketlines_table.php
del /f fix_ticketlines_triggers.php
del /f fix_total_inventory.php
del /f fix_trigger_error.php
del /f fix_triggers_multiple_updates.php
del /f fix_units_column.php
del /f fix_units.html
del /f fix_updated_column.php
del /f fix_user_passwords.php
del /f fix_validation.php
del /f get_db_info.php
del /f check_all_tables.php
del /f check_all_triggers_final.log
del /f check_all_triggers_final.php
del /f check_all_triggers.php
del /f check_automatic_update.php
del /f check_current_triggers.php
del /f check_database_objects.php
del /f check_database_structure.php
del /f check_database_tables.php
del /f check_database_triggers.php
del /f check_database_views.php
del /f check_db_tables.php
del /f check_duplicate_triggers.php
del /f check_error_log.php
del /f check_change_type_tables.log
del /f check_change_type_tables.php
del /f check_inventory_entries_structure.php
del /f check_inventory_entries_table.php
del /f check_inventory_entries.php
del /f check_inventory_sessions_and_totals.php
del /f check_inventory_sessions_structure.log
del /f check_inventory_sessions_structure.php
del /f check_inventory_sessions.php
del /f check_inventory_stock_changes.php
del /f check_inventory_totals_columns.log
del /f check_inventory_totals_columns.php
del /f check_inventory_totals_detailed.php
del /f check_inventory_totals_structure.php
del /f check_inventory_totals.php
del /f check_multiple_entries.log
del /f check_multiple_entries.php
del /f check_other_tables.php
del /f check_php_files_for_updated.php
del /f check_products_table.php
del /f check_recent_sales.php
del /f check_server_logs.php
del /f check_stockcurrent_log_columns.log
del /f check_stockcurrent_log_columns.php
del /f check_stockcurrent_log.log
del /f check_stockcurrent_log.php
del /f check_stockcurrent_triggers.php
del /f check_table_structure.php
del /f check_tables.php
del /f check_temp_tables.php
del /f check_temporary_tables.php
del /f check_ticketlines_log.php
del /f check_ticketlines_structure.php
del /f check_ticketlines_table.php
del /f check_ticketlines.php
del /f check_tickets_structure.php
del /f check_trigger_status.php
del /f check_trigger.log
del /f check_trigger.php
del /f check_triggers.php
del /f check_unicenta_code.php
del /f check_unicenta_triggers.php
del /f check_updated_column.php
del /f check_user_tables.php
del /f check_user.php
del /f check_users.php
del /f kontrola_triggeru.php
del /f manage_triggers.php
del /f manual_update_inventory_totals.php
del /f minimal_api_test.php
del /f monitor_sales_real_time.php
del /f monitor_sql_queries.php
del /f monitor_ticketlines.php
del /f oprava_aktualizace_mnozstvi.php
del /f OPRAVA_INVENTURY.md
del /f test_api_endpoint.php
del /f test_stockcurrent_update.php
del /f test_trigger_simple.php
del /f view_stockcurrent_logs.php
del /f view_stock_changes.php
del /f install_inventory_triggers.php
del /f INSTALL.md
del /f investigate_double_deduction.php
del /f manual_trigger_test.php
del /f update_inventory_totals.php
del /f fix_units.html
del /f fix_units_column.php
