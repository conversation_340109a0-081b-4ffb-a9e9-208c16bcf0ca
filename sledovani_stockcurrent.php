<?php
/**
 * Sledování změn v tabulce stockcurrent
 * 
 * Tento skript vytvoří trigger, k<PERSON><PERSON> bude sledovat změny v tabulce stockcurrent
 * a zaznamenávat je do logovací tabulky.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Sledování změn v tabulce stockcurrent</h1>";
    
    // Kontrola, zda existuje tabulka pro logování
    $stmt = $pdo->query("SHOW TABLES LIKE 'stockcurrent_changes'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Vytvoření tabulky pro logování
        $pdo->exec("
            CREATE TABLE `stockcurrent_changes` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `product_id` VARCHAR(255) NOT NULL,
                `old_units` DECIMAL(10,3) NULL,
                `new_units` DECIMAL(10,3) NULL,
                `difference` DECIMAL(10,3) NULL,
                `change_type` VARCHAR(50) NOT NULL,
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");
        
        logMessage("Tabulka stockcurrent_changes byla vytvořena.");
    }
    
    // Kontrola, zda existuje trigger pro sledování změn v tabulce stockcurrent
    $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` = 'log_stockcurrent_changes_update'");
    $triggerExists = $stmt->rowCount() > 0;
    
    if (!$triggerExists) {
        // Vytvoření triggeru pro sledování změn v tabulce stockcurrent
        $pdo->exec("
            CREATE TRIGGER `log_stockcurrent_changes_update`
            AFTER UPDATE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;
                
                -- Logování změny
                INSERT INTO stockcurrent_changes (product_id, old_units, new_units, difference, change_type)
                VALUES (NEW.product, OLD.units, NEW.units, difference, 'UPDATE');
            END
        ");
        
        logMessage("Trigger log_stockcurrent_changes_update byl vytvořen.");
    }
    
    // Kontrola, zda existuje trigger pro sledování vkládání do tabulky stockcurrent
    $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` = 'log_stockcurrent_changes_insert'");
    $triggerExists = $stmt->rowCount() > 0;
    
    if (!$triggerExists) {
        // Vytvoření triggeru pro sledování vkládání do tabulky stockcurrent
        $pdo->exec("
            CREATE TRIGGER `log_stockcurrent_changes_insert`
            AFTER INSERT ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Logování změny
                INSERT INTO stockcurrent_changes (product_id, old_units, new_units, difference, change_type)
                VALUES (NEW.product, 0, NEW.units, 0, 'INSERT');
            END
        ");
        
        logMessage("Trigger log_stockcurrent_changes_insert byl vytvořen.");
    }
    
    // Kontrola, zda existuje trigger pro sledování mazání z tabulky stockcurrent
    $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` = 'log_stockcurrent_changes_delete'");
    $triggerExists = $stmt->rowCount() > 0;
    
    if (!$triggerExists) {
        // Vytvoření triggeru pro sledování mazání z tabulky stockcurrent
        $pdo->exec("
            CREATE TRIGGER `log_stockcurrent_changes_delete`
            AFTER DELETE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Logování změny
                INSERT INTO stockcurrent_changes (product_id, old_units, new_units, difference, change_type)
                VALUES (OLD.product, OLD.units, 0, OLD.units, 'DELETE');
            END
        ");
        
        logMessage("Trigger log_stockcurrent_changes_delete byl vytvořen.");
    }
    
    // Výpis posledních změn v tabulce stockcurrent_changes
    $stmt = $pdo->query("
        SELECT * FROM stockcurrent_changes
        ORDER BY created_at DESC
        LIMIT 50
    ");
    $changes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Poslední změny v tabulce stockcurrent</h2>";
    
    if (empty($changes)) {
        logMessage("Zatím nebyly zaznamenány žádné změny.");
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Produkt</th><th>Stará hodnota</th><th>Nová hodnota</th><th>Rozdíl</th><th>Typ změny</th><th>Čas změny</th></tr>";
        
        foreach ($changes as $change) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($change['id']) . "</td>";
            echo "<td>" . htmlspecialchars($change['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($change['old_units']) . "</td>";
            echo "<td>" . htmlspecialchars($change['new_units']) . "</td>";
            echo "<td>" . htmlspecialchars($change['difference']) . "</td>";
            echo "<td>" . htmlspecialchars($change['change_type']) . "</td>";
            echo "<td>" . htmlspecialchars($change['created_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<p><a href='sledovani_stockcurrent.php'>Obnovit stránku</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
