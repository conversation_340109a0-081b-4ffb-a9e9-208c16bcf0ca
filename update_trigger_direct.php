<?php
/**
 * Přímá aktualizace triggeru pro změnu zadaného množství při prodeji
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Aktualizace triggeru pro změnu zadaného množství při prodeji</h1>";
    
    // Odstranění existujícího triggeru
    $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_entries_after_stockcurrent_update`");
    logMessage("Existující trigger byl odstraněn");
    
    // Vytvoření nového triggeru
    $sql = "
        CREATE TRIGGER `update_inventory_entries_after_stockcurrent_update`
        AFTER UPDATE ON `stockcurrent`
        FOR EACH ROW
        BEGIN
            -- Výpočet rozdílu mezi starou a novou hodnotou
            DECLARE difference DECIMAL(10,3);
            SET difference = OLD.units - NEW.units;

            -- Logování změny pro diagnostiku (bez použití sloupce change_type)
            INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
            VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

            -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme celkové součty a zadané množství
            IF difference != 0 THEN
                -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                -- Při prodeji (difference > 0) odečítáme zadané množství
                -- Při naskladnění (difference < 0) přičítáme zadané množství
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                WHERE product_id = NEW.product
                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                
                -- Aktualizace zadaného množství v jednotlivých inventurních záznamech
                -- Tím zajistíme, že se nebude zvětšovat rozdíl mezi zadaným množstvím a aktuálním stavem
                UPDATE inventory_entries
                SET zadane_mnozstvi = zadane_mnozstvi - difference
                WHERE product_id = NEW.product
                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
            END IF;
        END
    ";
    
    $pdo->exec($sql);
    logMessage("Nový trigger byl úspěšně vytvořen");
    
    // Kontrola, zda byl trigger úspěšně vytvořen
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND TRIGGER_NAME = 'update_inventory_entries_after_stockcurrent_update'
    ");
    $trigger = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($trigger) {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update byl úspěšně vytvořen");
        logMessage("Kód triggeru: " . $trigger['ACTION_STATEMENT']);
    } else {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update nebyl vytvořen", true);
    }
    
    echo "<p><a href='test_zadane_mnozstvi_update.php'>Otestovat aktualizaci zadaného množství</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
