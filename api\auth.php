<?php
/**
 * Authentication API
 *
 * This file handles authentication-related API endpoints.
 */

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../utils/validation.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request path
$path = $_SERVER['PATH_INFO'] ?? '/';

// Get endpoint from GET parameter (support both 'endpoint' and 'action' for backward compatibility)
$endpoint = $_GET['endpoint'] ?? $_GET['action'] ?? '';

// Determine endpoint from path or GET parameter
if (!empty($endpoint)) {
    // Use endpoint from GET parameter
    error_log("Using endpoint from GET parameter: " . $endpoint);
} else {
    // Extract endpoint from path
    $endpoint = trim($path, '/');
    error_log("Using endpoint from path: " . $endpoint);
}

// Handle different endpoints
switch ($endpoint) {
    case 'login':
        handleLogin();
        break;

    case 'logout':
        handleLogout();
        break;

    case 'check':
        handleCheckAuth();
        break;

    default:
        sendResponse(['error' => 'Endpoint not found: ' . $endpoint], 404);
        break;
}

/**
 * Handle login request
 */
function handleLogin() {
    global $method;

    if ($method !== 'POST') {
        sendResponse(['error' => 'Method not allowed'], 405);
        return;
    }

    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        sendResponse(['error' => 'Invalid JSON'], 400);
        return;
    }

    // Logování vstupu pro ladění
    error_log("handleLogin - vstupní data: " . print_r($input, true));

    // Validate input
    $schema = [
        'username' => ['type' => 'username', 'required' => true, 'minLength' => 1, 'maxLength' => 50],
        'password' => ['type' => 'password', 'required' => true, 'minLength' => 1]
    ];

    $validation = validateData($input, $schema);

    // Logování výsledku validace pro ladění
    error_log("handleLogin - výsledek validace: " . print_r($validation, true));

    if (!$validation['valid']) {
        error_log("handleLogin - validace selhala: " . print_r($validation['errors'], true));
        sendResponse(['error' => 'Validation failed', 'details' => $validation['errors']], 400);
        return;
    }

    // Ensure tables exist before attempting to authenticate
    try {
        ensureTablesExist();
    } catch (PDOException $e) {
        error_log("Failed to ensure tables exist during login: " . $e->getMessage());
        sendResponse(['error' => 'Database error. Please contact administrator.'], 500);
        return;
    }

    // Authenticate user
    $user = authenticateUser($validation['data']['username'], $validation['data']['password']);

    if (!$user) {
        sendResponse(['error' => 'Invalid username or password'], 401);
        return;
    }

    // Return user data
    sendResponse([
        'success' => true,
        'user' => $user
    ]);
}

/**
 * Handle logout request
 */
function handleLogout() {
    global $method;

    if ($method !== 'POST') {
        sendResponse(['error' => 'Method not allowed'], 405);
        return;
    }

    // Check if user is logged in
    if (!isLoggedIn()) {
        sendResponse(['error' => 'Not logged in'], 401);
        return;
    }

    // Log out user
    logout();

    sendResponse(['success' => true]);
}

/**
 * Handle check auth request
 */
function handleCheckAuth() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Method not allowed'], 405);
        return;
    }

    // Ensure tables exist before checking authentication
    try {
        ensureTablesExist();
    } catch (PDOException $e) {
        error_log("Failed to ensure tables exist during auth check: " . $e->getMessage());
        // Even if table creation fails, we continue with the auth check
    }

    // Check if user is logged in
    if (!isLoggedIn()) {
        sendResponse(['authenticated' => false], 200);
        return;
    }

    // Return user data
    sendResponse([
        'authenticated' => true,
        'user' => getCurrentUser()
    ]);
}

/**
 * Send JSON response
 *
 * @param mixed $data The response data
 * @param int $statusCode HTTP status code
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}
