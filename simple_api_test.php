<?php
/**
 * Jednoduchý test API bez autentifikace pro diagnostiku
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🧪 Jednoduchý API test</h1>";

try {
    // Načtení potřebných souborů
    require_once __DIR__ . '/utils/database.php';
    
    echo "<h2>📊 Test přímého volání getTotalEntries()</h2>";
    
    // Najdeme aktivní inventurní relaci
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active' LIMIT 1");
    $activeSession = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$activeSession) {
        logMessage("❌ Žádná aktivní inventurní relace!", true);
        exit;
    }
    
    $sessionId = $activeSession['id'];
    logMessage("✓ Použiji aktivní relaci ID: $sessionId");
    
    // Přímý test SQL dotazu z getTotalEntries()
    echo "<h3>🗄️ Test SQL dotazu z inventory_totals:</h3>";
    
    $sql = "
        SELECT
            it.id,
            it.product_id,
            p.code AS ean_code,
            p.name AS product_name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock,
            it.total_zadane_mnozstvi AS zadane_mnozstvi,
            (it.total_zadane_mnozstvi - COALESCE(s.units, 0)) AS difference,
            it.last_updated
        FROM
            inventory_totals it
        JOIN
            products p ON it.product_id = p.id
        LEFT JOIN
            categories c ON p.category = c.id
        LEFT JOIN
            taxes t ON p.taxcat = t.id
        LEFT JOIN
            stockcurrent s ON p.id = s.product
        WHERE
            it.session_id = ?
        ORDER BY
            p.name
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$sessionId]);
    $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($entries)) {
        logMessage("⚠️ Žádná data v inventory_totals pro session $sessionId");
        
        // Zkusíme najít jakákoliv data v inventory_totals
        $stmt = $pdo->query("SELECT COUNT(*) FROM inventory_totals");
        $totalCount = $stmt->fetchColumn();
        
        logMessage("ℹ️ Celkový počet záznamů v inventory_totals: $totalCount");
        
        if ($totalCount > 0) {
            $stmt = $pdo->query("SELECT session_id, COUNT(*) as count FROM inventory_totals GROUP BY session_id");
            $sessionCounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h4>📋 Záznamy podle session_id:</h4>";
            echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
            echo "<tr><th>Session ID</th><th>Počet záznamů</th></tr>";
            foreach ($sessionCounts as $row) {
                echo "<tr><td>" . htmlspecialchars($row['session_id']) . "</td><td>" . htmlspecialchars($row['count']) . "</td></tr>";
            }
            echo "</table>";
        }
    } else {
        logMessage("✅ Nalezeno " . count($entries) . " záznamů v inventory_totals");
        
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Produkt</th><th>Název</th><th>Zadané množství</th><th>Aktuální stav</th><th>Rozdíl</th></tr>";
        
        foreach ($entries as $entry) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($entry['id']) . "</td>";
            echo "<td>" . htmlspecialchars(substr($entry['product_id'], 0, 20)) . "...</td>";
            echo "<td>" . htmlspecialchars($entry['product_name']) . "</td>";
            echo "<td><strong>" . htmlspecialchars($entry['zadane_mnozstvi']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($entry['current_stock']) . "</td>";
            echo "<td>" . htmlspecialchars($entry['difference']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Test vytvoření JSON odpovědi
    echo "<h3>📡 Test JSON odpovědi:</h3>";
    
    $responseData = [];
    foreach ($entries as $entry) {
        $responseData[] = [
            'id' => $entry['id'],
            'product_id' => $entry['product_id'],
            'ean_code' => $entry['ean_code'],
            'product_name' => $entry['product_name'],
            'category' => $entry['category'],
            'pricebuy' => $entry['pricebuy'],
            'tax_rate' => $entry['tax_rate'],
            'pricesell' => $entry['pricesell'],
            'price_with_tax' => $entry['pricesell'] * (1 + $entry['tax_rate']),
            'current_stock' => $entry['current_stock'],
            'zadane_mnozstvi' => $entry['zadane_mnozstvi'],
            'difference' => $entry['difference'],
            'users' => 'Test User',
            'last_updated' => $entry['last_updated'],
            'can_edit' => true
        ];
    }
    
    $jsonResponse = json_encode(['entries' => $responseData], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    
    if (json_last_error() === JSON_ERROR_NONE) {
        logMessage("✅ JSON odpověď vytvořena úspěšně");
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
        echo htmlspecialchars($jsonResponse);
        echo "</pre>";
    } else {
        logMessage("❌ Chyba při vytváření JSON: " . json_last_error_msg(), true);
    }
    
    // Test porovnání s inventory_entries
    echo "<h3>⚖️ Porovnání s inventory_entries:</h3>";
    
    $entriesSQL = "
        SELECT 
            ie.product_id,
            p.name as product_name,
            SUM(ie.zadane_mnozstvi) AS entries_sum
        FROM inventory_entries ie
        LEFT JOIN products p ON ie.product_id = p.id
        WHERE ie.session_id = ? 
        AND ie.status = 'active'
        GROUP BY ie.product_id, p.name
        ORDER BY entries_sum DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->prepare($entriesSQL);
    $stmt->execute([$sessionId]);
    $entriesData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($entriesData)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
        echo "<tr><th>Produkt</th><th>inventory_entries (součet)</th><th>inventory_totals (hodnota)</th><th>Rozdíl</th></tr>";
        
        foreach ($entriesData as $entryData) {
            $productId = $entryData['product_id'];
            $entriesSum = $entryData['entries_sum'];
            
            // Najdeme odpovídající záznam v totals
            $totalsValue = 0;
            foreach ($entries as $totalEntry) {
                if ($totalEntry['product_id'] === $productId) {
                    $totalsValue = $totalEntry['zadane_mnozstvi'];
                    break;
                }
            }
            
            $difference = abs($entriesSum - $totalsValue);
            $isConsistent = $difference < 0.001;
            
            echo "<tr style='background: " . ($isConsistent ? '#d4edda' : '#f8d7da') . ";'>";
            echo "<td>" . htmlspecialchars($entryData['product_name']) . "</td>";
            echo "<td>" . htmlspecialchars($entriesSum) . "</td>";
            echo "<td>" . htmlspecialchars($totalsValue) . "</td>";
            echo "<td><strong>" . htmlspecialchars($difference) . "</strong></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    logMessage("❌ Chyba při testu: " . $e->getMessage(), true);
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "Soubor: " . $e->getFile() . "\n";
    echo "Řádek: " . $e->getLine() . "\n";
    echo "Chyba: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
    echo "</pre>";
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='debug_500_error.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Debug 500 chyby</a>";
echo "<a href='test_auth_status.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test autentifikace</a>";
echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
echo "</p>";
?>
