<?php
/**
 * Kontrola struktury tabulek
 */

// Spuštění session pouze pokud ještě nen<PERSON> spu<PERSON>
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Kontrola struktury tabulek</h1>";

require_once __DIR__ . '/utils/database.php';

try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení úspěšné</p>";
    
    echo "<h2>Struktura inventory_sessions</h2>";
    $stmt = $pdo->query("DESCRIBE inventory_sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Struktura inventory_entries</h2>";
    $stmt = $pdo->query("DESCRIBE inventory_entries");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Struktura inventory_users</h2>";
    $stmt = $pdo->query("DESCRIBE inventory_users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>Test INSERT s explicitními hodnotami</h2>";
    
    // Zkusíme INSERT s explicitními hodnotami pro všechna pole
    try {
        $stmt = $pdo->prepare("
            INSERT INTO inventory_sessions (user_id, start_time, status) 
            VALUES (?, NOW(), 'active')
        ");
        $result = $stmt->execute([1]); // Předpokládáme, že admin má ID 1
        
        if ($result) {
            $sessionId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✓ Test INSERT úspěšný, ID: $sessionId</p>";
        } else {
            echo "<p style='color: red;'>✗ Test INSERT neúspěšný</p>";
            echo "<p>Chyba: " . print_r($stmt->errorInfo(), true) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Chyba při INSERT: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
}

echo "<p><a href='index.html'>Zpět na hlavní aplikaci</a></p>";
?>
