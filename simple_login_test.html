<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jednoduchý test přihlášení</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 200px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Jednoduchý test přihlášení</h1>
    
    <div class="test-section">
        <h2>Přihlašovací formulář</h2>
        <form id="loginForm">
            <div>
                <label>Už<PERSON>lské jméno:</label><br>
                <input type="text" id="username" value="admin" required>
            </div>
            <div>
                <label>Heslo:</label><br>
                <input type="password" id="password" value="admin123" required>
            </div>
            <div>
                <button type="submit">Přihlásit se</button>
            </div>
        </form>
        <div id="loginResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Test API endpointů</h2>
        <button onclick="testAuthAPI()">Test auth API</button>
        <button onclick="testInventoryAPI()">Test inventory API</button>
        <div id="apiResult"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            resultDiv.innerHTML = '<div class="info">Přihlašování...</div>';
            
            try {
                console.log('Odesílám přihlašovací požadavek:', { username, password });
                
                const response = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const text = await response.text();
                console.log('Response text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Odpověď není JSON: ' + text);
                }
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Přihlášení úspěšné!</div>
                        <p>Uživatel: ${data.user.username}</p>
                        <p>Role: ${data.user.role}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ Přihlášení selhalo: ${data.error}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                console.error('Chyba při přihlašování:', error);
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        });
        
        async function testAuthAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="info">Testování auth API...</div>';
            
            try {
                const response = await fetch('api/simple_auth.php?action=check');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <h3>Auth API test:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function testInventoryAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.innerHTML = '<div class="info">Testování inventory API...</div>';
            
            try {
                const response = await fetch('api/inventory.php');
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Odpověď není JSON: ' + text);
                }
                
                resultDiv.innerHTML = `
                    <h3>Inventory API test:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        // Automatický test při načtení
        window.onload = function() {
            testAuthAPI();
        };
    </script>
</body>
</html>
