<?php
/**
 * Testovací skript pro API endpoint inventory_entry_create.php s rozšířeným debugováním
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Funkce pro výpis informací o PDO
function getPdoDebugInfo($pdo) {
    $attributes = [
        PDO::ATTR_AUTOCOMMIT,
        PDO::ATTR_CASE,
        PDO::ATTR_CLIENT_VERSION,
        PDO::ATTR_CONNECTION_STATUS,
        PDO::ATTR_DRIVER_NAME,
        PDO::ATTR_ERRMODE,
        PDO::ATTR_ORACLE_NULLS,
        PDO::ATTR_PERSISTENT,
        PDO::ATTR_PREFETCH,
        PDO::ATTR_SERVER_INFO,
        PDO::ATTR_SERVER_VERSION,
        PDO::ATTR_TIMEOUT
    ];
    
    $info = [];
    foreach ($attributes as $attribute) {
        try {
            $info[$attribute] = $pdo->getAttribute($attribute);
        } catch (PDOException $e) {
            $info[$attribute] = 'Not supported';
        }
    }
    
    return $info;
}

// Kontrola, zda byl odeslán formulář
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Získání hodnot z formuláře
    $sessionId = $_POST['session_id'] ?? '';
    $eanCode = $_POST['ean_code'] ?? '';
    $zadaneMnozstvi = $_POST['zadane_mnozstvi'] ?? '';
    
    echo "<h2>Testování API endpointu api/inventory_entry_create.php</h2>";
    
    echo "<p>Odesílané hodnoty:</p>";
    echo "<ul>";
    echo "<li>Session ID: " . htmlspecialchars($sessionId) . "</li>";
    echo "<li>EAN kód: " . htmlspecialchars($eanCode) . "</li>";
    echo "<li>Zadané množství: " . htmlspecialchars($zadaneMnozstvi) . "</li>";
    echo "</ul>";
    
    // Vytvoření dat pro požadavek
    $data = [
        'session_id' => $sessionId,
        'ean_code' => $eanCode,
        'zadane_mnozstvi' => $zadaneMnozstvi
    ];
    
    // Kontrola existence tabulek
    try {
        $pdo = getDbConnection();
        
        echo "<h3>Kontrola existence tabulek</h3>";
        
        $tables = [
            'inventory_sessions',
            'inventory_entries',
            'inventory_totals',
            'inventory_stock_changes'
        ];
        
        foreach ($tables as $table) {
            $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            
            echo "<p>" . htmlspecialchars($table) . ": " . ($exists ? "<span style='color: green;'>existuje</span>" : "<span style='color: red;'>neexistuje</span>") . "</p>";
            
            if ($exists) {
                // Kontrola struktury tabulky
                $stmt = $pdo->query("DESCRIBE $table");
                $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<details>";
                echo "<summary>Struktura tabulky</summary>";
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
                
                foreach ($columns as $column) {
                    echo "<tr>";
                    foreach ($column as $key => $value) {
                        echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
                    }
                    echo "</tr>";
                }
                
                echo "</table>";
                echo "</details>";
            }
        }
        
        // Kontrola existence relace
        if (!empty($sessionId)) {
            $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = ?");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch();
            
            echo "<h3>Kontrola existence relace</h3>";
            
            if ($session) {
                echo "<p style='color: green;'>✓ Relace s ID " . htmlspecialchars($sessionId) . " existuje.</p>";
                echo "<p>Status: " . htmlspecialchars($session['status']) . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Relace s ID " . htmlspecialchars($sessionId) . " neexistuje!</p>";
            }
        }
        
        // Kontrola existence produktu
        if (!empty($eanCode)) {
            $stmt = $pdo->prepare("
                SELECT p.*, COALESCE(s.units, 0) AS current_stock
                FROM products p
                LEFT JOIN stockcurrent s ON p.id = s.product
                WHERE p.code = ?
            ");
            $stmt->execute([$eanCode]);
            $product = $stmt->fetch();
            
            echo "<h3>Kontrola existence produktu</h3>";
            
            if ($product) {
                echo "<p style='color: green;'>✓ Produkt s EAN kódem " . htmlspecialchars($eanCode) . " existuje.</p>";
                echo "<p>ID: " . htmlspecialchars($product['id']) . "</p>";
                echo "<p>Název: " . htmlspecialchars($product['name']) . "</p>";
                echo "<p>Aktuální stav skladu: " . htmlspecialchars($product['current_stock']) . "</p>";
            } else {
                echo "<p style='color: red;'>✗ Produkt s EAN kódem " . htmlspecialchars($eanCode) . " neexistuje!</p>";
            }
        }
        
        // Informace o PDO
        echo "<h3>Informace o PDO</h3>";
        
        $pdoInfo = getPdoDebugInfo($pdo);
        
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Atribut</th><th>Hodnota</th></tr>";
        
        foreach ($pdoInfo as $attribute => $value) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($attribute) . "</td>";
            echo "<td>" . htmlspecialchars(is_array($value) ? print_r($value, true) : $value) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "<h3>Chyba při připojení k databázi</h3>";
        echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    // Přímé volání API endpointu
    echo "<h3>Přímé volání API endpointu</h3>";
    
    echo "<p>Nyní zkusíme přímo volat API endpoint bez použití cURL:</p>";
    
    // Uložení původních hodnot
    $originalMethod = $_SERVER['REQUEST_METHOD'];
    $originalInput = file_get_contents('php://input');
    
    // Nastavení nových hodnot
    $_SERVER['REQUEST_METHOD'] = 'POST';
    
    // Zachycení výstupu
    ob_start();
    
    try {
        // Příprava JSON vstupu
        $jsonInput = json_encode($data);
        
        // Nastavení JSON vstupu
        file_put_contents('php://input', $jsonInput);
        
        // Volání API endpointu
        include __DIR__ . '/api/inventory_entry_create.php';
    } catch (Exception $e) {
        echo "<p style='color: red;'>Došlo k chybě: " . htmlspecialchars($e->getMessage()) . "</p>";
        echo "<p>Stack trace:</p>";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    }
    
    // Získání výstupu
    $output = ob_get_clean();
    
    // Obnovení původních hodnot
    $_SERVER['REQUEST_METHOD'] = $originalMethod;
    file_put_contents('php://input', $originalInput);
    
    echo "<p>Odpověď API:</p>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
    // Pokus o dekódování JSON odpovědi
    $jsonOutput = json_decode($output, true);
    
    if ($jsonOutput !== null) {
        echo "<p>Dekódovaná JSON odpověď:</p>";
        echo "<pre>" . htmlspecialchars(print_r($jsonOutput, true)) . "</pre>";
    }
    
    echo "<p><a href='test_api_endpoint_debug.php'>Zpět na formulář</a></p>";
} else {
    // Formulář pro testování API
    echo "<h1>Testování API endpointu inventory_entry_create.php</h1>";
    
    echo "<p>Tento formulář vám pomůže otestovat funkčnost API endpointu <code>api/inventory_entry_create.php</code>.</p>";
    
    // Získání aktivních relací
    try {
        $pdo = getDbConnection();
        $stmt = $pdo->query("SELECT id, user_id, start_time, status FROM inventory_sessions WHERE status = 'active' ORDER BY id DESC");
        $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $sessions = [];
        echo "<p style='color: red;'>Chyba při získávání relací: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<form method='post'>";
    
    echo "<p>Session ID: ";
    if (!empty($sessions)) {
        echo "<select name='session_id'>";
        foreach ($sessions as $session) {
            echo "<option value='" . htmlspecialchars($session['id']) . "'>" . 
                htmlspecialchars($session['id'] . " - Uživatel: " . $session['user_id'] . " - Vytvořeno: " . $session['start_time']) . 
                "</option>";
        }
        echo "</select>";
    } else {
        echo "<input type='text' name='session_id' value='1' required>";
        echo " <small>(Nebyly nalezeny žádné aktivní relace)</small>";
    }
    echo "</p>";
    
    echo "<p>EAN kód: <input type='text' name='ean_code' required></p>";
    echo "<p>Zadané množství: <input type='number' name='zadane_mnozstvi' step='0.001' required></p>";
    
    echo "<p><button type='submit'>Otestovat API</button></p>";
    
    echo "</form>";
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
