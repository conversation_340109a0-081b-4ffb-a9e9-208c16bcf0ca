<?php
/**
 * Skript pro testování připojení k databázi a kontrolu struktury tabulek
 */

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test připojení k databázi</h1>";
    echo "<p style='color: green;'>✓ Připojení k databázi bylo úspěšné.</p>";
    
    // Získání informací o databázi
    $dbInfo = $pdo->query("SELECT DATABASE() as db, VERSION() as version")->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Informace o databázi</h2>";
    echo "<p>Databáze: " . htmlspecialchars($dbInfo['db']) . "</p>";
    echo "<p>Verze MySQL: " . htmlspecialchars($dbInfo['version']) . "</p>";
    
    // Získání seznamu tabulek
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<h2>Seznam tabulek</h2>";
    echo "<p>Celkový počet tabulek: " . count($tables) . "</p>";
    
    // Rozdělení tabulek na standardní UniCenta tabulky a vlastní tabulky
    $unicentaTables = [];
    $customTables = [];
    
    $customTablePrefixes = ['inventory_', 'previous_', 'ticketlines_log', 'stockcurrent_log'];
    
    foreach ($tables as $table) {
        $isCustom = false;
        
        foreach ($customTablePrefixes as $prefix) {
            if (strpos($table, $prefix) === 0) {
                $isCustom = true;
                break;
            }
        }
        
        if ($isCustom) {
            $customTables[] = $table;
        } else {
            $unicentaTables[] = $table;
        }
    }
    
    echo "<h3>UniCenta tabulky</h3>";
    echo "<ul>";
    foreach ($unicentaTables as $table) {
        echo "<li>" . htmlspecialchars($table) . "</li>";
    }
    echo "</ul>";
    
    echo "<h3>Vlastní tabulky</h3>";
    
    if (empty($customTables)) {
        echo "<p>Nebyly nalezeny žádné vlastní tabulky.</p>";
    } else {
        echo "<ul>";
        foreach ($customTables as $table) {
            echo "<li>" . htmlspecialchars($table) . "</li>";
        }
        echo "</ul>";
    }
    
    // Kontrola existence klíčových tabulek
    $requiredTables = [
        'products',
        'categories',
        'taxes',
        'stockcurrent',
        'tickets',
        'ticketlines',
        'inventory_sessions',
        'inventory_entries',
        'inventory_users',
        'inventory_totals'
    ];
    
    echo "<h2>Kontrola klíčových tabulek</h2>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Tabulka</th><th>Existuje</th><th>Počet záznamů</th></tr>";
    
    foreach ($requiredTables as $table) {
        $exists = in_array($table, $tables);
        $count = 0;
        
        if ($exists) {
            $stmt = $pdo->query("SELECT COUNT(*) FROM `$table`");
            $count = $stmt->fetchColumn();
        }
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($table) . "</td>";
        echo "<td>" . ($exists ? "<span style='color: green;'>✓</span>" : "<span style='color: red;'>✗</span>") . "</td>";
        echo "<td>" . ($exists ? htmlspecialchars($count) : "N/A") . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
    // Kontrola struktury tabulky ticketlines
    if (in_array('ticketlines', $tables)) {
        echo "<h2>Struktura tabulky ticketlines</h2>";
        
        $stmt = $pdo->query("DESCRIBE ticketlines");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $hasUpdatedColumn = false;
        
        foreach ($columns as $column) {
            echo "<tr>";
            foreach ($column as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>";
            
            if (strtolower($column['Field']) === 'updated') {
                $hasUpdatedColumn = true;
            }
        }
        
        echo "</table>";
        
        if (!$hasUpdatedColumn) {
            echo "<p style='color: red;'>✗ Tabulka ticketlines nemá sloupec 'updated', což může způsobovat chybu při řazení.</p>";
            echo "<p>Pro opravu této chyby spusťte skript <a href='fix_updated_column.php'>fix_updated_column.php</a>.</p>";
        } else {
            echo "<p style='color: green;'>✓ Tabulka ticketlines má sloupec 'updated'.</p>";
        }
    }
    
    // Kontrola existence vlastních tabulek
    $customRequiredTables = [
        'inventory_sessions',
        'inventory_entries',
        'inventory_users',
        'inventory_totals'
    ];
    
    $missingCustomTables = [];
    
    foreach ($customRequiredTables as $table) {
        if (!in_array($table, $tables)) {
            $missingCustomTables[] = $table;
        }
    }
    
    if (!empty($missingCustomTables)) {
        echo "<h2>Chybějící vlastní tabulky</h2>";
        echo "<p>Následující vlastní tabulky chybí v databázi:</p>";
        echo "<ul>";
        foreach ($missingCustomTables as $table) {
            echo "<li>" . htmlspecialchars($table) . "</li>";
        }
        echo "</ul>";
        
        echo "<p>Pro vytvoření chybějících tabulek spusťte skript <a href='create_tables.php'>create_tables.php</a>.</p>";
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
