<?php
/**
 * API Debug
 * 
 * Tento soubor slouží k ladění API.
 */

// Zapnutí zobrazování chyb pro ladění
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Získání informací o serveru
$serverInfo = [
    'SCRIPT_NAME' => $_SERVER['SCRIPT_NAME'],
    'SCRIPT_FILENAME' => $_SERVER['SCRIPT_FILENAME'],
    'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'],
    'PHP_SELF' => $_SERVER['PHP_SELF'],
    'REQUEST_URI' => $_SERVER['REQUEST_URI'],
    'HTTP_HOST' => $_SERVER['HTTP_HOST'],
    'REMOTE_ADDR' => $_SERVER['REMOTE_ADDR'],
    'SERVER_SOFTWARE' => $_SERVER['SERVER_SOFTWARE'],
    'SERVER_PROTOCOL' => $_SERVER['SERVER_PROTOCOL'],
    'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'],
    'QUERY_STRING' => $_SERVER['QUERY_STRING'] ?? '',
    'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? ''
];

// Získání informací o PHP
$phpInfo = [
    'version' => phpversion(),
    'extensions' => get_loaded_extensions(),
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time'),
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size')
];

// Testování připojení k databázi
$dbInfo = [];
try {
    // Načtení konfigurace databáze
    $config = require __DIR__ . '/../config/database.php';
    
    // Vytvoření DSN řetězce
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    
    // Pokus o připojení k databázi
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    
    // Získání informací o databázi
    $stmt = $pdo->query("SELECT VERSION() AS version");
    $version = $stmt->fetch();
    
    // Získání seznamu tabulek
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Kontrola existence tabulky inventory_users
    $usersTableExists = in_array('inventory_users', $tables);
    
    // Získání seznamu uživatelů
    $users = [];
    if ($usersTableExists) {
        $stmt = $pdo->query("SELECT id, username, role, full_name, email, active FROM inventory_users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    $dbInfo = [
        'connected' => true,
        'host' => $config['host'],
        'dbname' => $config['dbname'],
        'username' => $config['username'],
        'version' => $version['version'],
        'tables' => $tables,
        'tables_count' => count($tables),
        'users_table_exists' => $usersTableExists,
        'users' => $users
    ];
} catch (PDOException $e) {
    $dbInfo = [
        'connected' => false,
        'error' => $e->getMessage()
    ];
}

// Testování přístupu k souborům
$filesInfo = [];
$filesToCheck = [
    '../api/index.php',
    '../api/users.php',
    '../api/auth.php',
    '../utils/database.php',
    '../utils/auth.php',
    '../utils/validation.php',
    '../config/database.php'
];

foreach ($filesToCheck as $file) {
    $filesInfo[$file] = [
        'exists' => file_exists($file),
        'readable' => is_readable($file),
        'writable' => is_writable($file),
        'size' => file_exists($file) ? filesize($file) : null,
        'modified' => file_exists($file) ? date('Y-m-d H:i:s', filemtime($file)) : null
    ];
}

// Odeslání odpovědi
echo json_encode([
    'success' => true,
    'message' => 'API debug',
    'timestamp' => date('Y-m-d H:i:s'),
    'server' => $serverInfo,
    'php' => $phpInfo,
    'database' => $dbInfo,
    'files' => $filesInfo
], JSON_PRETTY_PRINT);
