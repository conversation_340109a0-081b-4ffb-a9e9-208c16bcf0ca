<?php
/**
 * Test API endpointů pro diagnostiku problémů
 */

require_once __DIR__ . '/utils/database.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

function testApiEndpoint($url, $description) {
    echo "<h3>🌐 Test: $description</h3>";
    echo "<p><strong>URL:</strong> <code>" . htmlspecialchars($url) . "</code></p>";
    
    $context = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Content-Type: application/json',
                'Cookie: ' . $_SERVER['HTTP_COOKIE'] ?? ''
            ],
            'timeout' => 10
        ]
    ]);
    
    $response = @file_get_contents($url, false, $context);
    
    if ($response === false) {
        $error = error_get_last();
        logMessage("❌ Chyba při volání API: " . ($error['message'] ?? 'Neznámá chyba'), true);
        return false;
    }
    
    $data = json_decode($response, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        logMessage("❌ Chyba při parsování JSON: " . json_last_error_msg(), true);
        echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($response) . "</pre>";
        return false;
    }
    
    if (isset($data['error'])) {
        logMessage("❌ API vrátilo chybu: " . $data['error'], true);
        return false;
    }
    
    logMessage("✅ API endpoint funguje správně");
    
    // Zobrazení ukázky dat
    if (isset($data['entries']) && !empty($data['entries'])) {
        $count = count($data['entries']);
        logMessage("📊 Vráceno $count záznamů");
        
        echo "<h4>Ukázka prvního záznamu:</h4>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars(json_encode($data['entries'][0], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "</pre>";
    } elseif (isset($data['sessions']) && !empty($data['sessions'])) {
        $count = count($data['sessions']);
        logMessage("📊 Vráceno $count relací");
    } else {
        logMessage("⚠️ API vrátilo prázdná data");
        echo "<pre style='background: #fff3cd; padding: 10px; border-radius: 5px;'>";
        echo htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        echo "</pre>";
    }
    
    return true;
}

try {
    $pdo = getDbConnection();
    
    echo "<h1>🧪 Test API endpointů</h1>";
    
    // Najdeme aktivní inventurní relaci
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active' LIMIT 1");
    $activeSession = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$activeSession) {
        logMessage("❌ Žádná aktivní inventurní relace pro testování!", true);
        exit;
    }
    
    $sessionId = $activeSession['id'];
    logMessage("✓ Použiji aktivní inventurní relaci ID: $sessionId");
    
    $baseUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5";
    
    // Test různých API endpointů
    $tests = [
        [
            'url' => "$baseUrl/api/inventory.php?action=sessions",
            'description' => 'Získání seznamu inventurních relací'
        ],
        [
            'url' => "$baseUrl/api/inventory.php?action=entries&session_id=$sessionId",
            'description' => 'Získání inventurních záznamů (starý způsob)'
        ],
        [
            'url' => "$baseUrl/api/inventory.php?action=total-entries&session_id=$sessionId",
            'description' => 'Získání celkové inventury (nový způsob)'
        ]
    ];
    
    foreach ($tests as $test) {
        testApiEndpoint($test['url'], $test['description']);
        echo "<hr>";
    }
    
    // Test přímého volání funkce
    echo "<h2>🔧 Test přímého volání funkce getTotalEntries()</h2>";
    
    try {
        // Simulace GET parametrů
        $_GET['session_id'] = $sessionId;
        $_GET['action'] = 'total-entries';
        
        // Zachytíme výstup
        ob_start();
        
        // Načteme API soubor
        require_once __DIR__ . '/utils/auth.php';
        require_once __DIR__ . '/utils/validation.php';
        
        // Simulujeme přihlášeného uživatele
        if (!isset($_SESSION)) {
            session_start();
        }
        
        // Kontrola, zda máme přihlášeného uživatele
        if (!isLoggedIn()) {
            logMessage("⚠️ Uživatel není přihlášen - simulujem přihlášení", false);
            
            // Najdeme nějakého uživatele
            $stmt = $pdo->query("SELECT * FROM inventory_users WHERE status = 'active' LIMIT 1");
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($user) {
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['role'] = $user['role'];
                logMessage("✓ Simulováno přihlášení uživatele: " . $user['username']);
            } else {
                logMessage("❌ Žádný uživatel v databázi!", true);
                exit;
            }
        }
        
        // Nyní zkusíme volat API funkci přímo
        if (function_exists('getTotalEntries')) {
            getTotalEntries();
        } else {
            // Načteme API soubor a zavoláme funkci
            include_once __DIR__ . '/api/inventory.php';
        }
        
        $output = ob_get_clean();
        
        if (!empty($output)) {
            $data = json_decode($output, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                logMessage("✅ Přímé volání funkce úspěšné");
                
                if (isset($data['entries'])) {
                    $count = count($data['entries']);
                    logMessage("📊 Vráceno $count záznamů z inventory_totals");
                } else {
                    logMessage("⚠️ Funkce nevrátila očekávaná data");
                    echo "<pre>" . htmlspecialchars($output) . "</pre>";
                }
            } else {
                logMessage("❌ Chyba při parsování výstupu funkce", true);
                echo "<pre>" . htmlspecialchars($output) . "</pre>";
            }
        } else {
            logMessage("❌ Funkce nevrátila žádný výstup", true);
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        logMessage("❌ Chyba při přímém volání: " . $e->getMessage(), true);
    }
    
    echo "<h2>🔗 Navigace</h2>";
    echo "<p>";
    echo "<a href='test_fixed_total_inventory.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test opravené inventury</a>";
    echo "<a href='comprehensive_zadane_mnozstvi_test.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Komplexní test</a>";
    echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
    echo "</p>";
    
} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
