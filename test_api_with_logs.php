<?php
/**
 * Test API s logováním pro identifikaci 500 chyby
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🧪 Test API s logováním</h1>";

// Spuštění session
if (!isset($_SESSION)) {
    session_start();
}

try {
    $pdo = getDbConnection();
    
    echo "<h2>🔐 Zajištění přihlášení</h2>";
    
    if (!isLoggedIn()) {
        // Najdeme nějakého uživatele a přihlásíme ho
        $stmt = $pdo->query("SELECT * FROM inventory_users WHERE status = 'active' ORDER BY role DESC LIMIT 1");
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            
            logMessage("✅ Automaticky přihlášen uživatel: " . $user['username']);
        } else {
            logMessage("❌ Žádný aktivní uživatel v databázi!", true);
            exit;
        }
    } else {
        $user = getCurrentUser();
        logMessage("✅ Uživatel je přihlášen: " . $user['username']);
    }
    
    echo "<h2>📋 Session informace</h2>";
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr><th>Klíč</th><th>Hodnota</th></tr>";
    foreach ($_SESSION as $key => $value) {
        echo "<tr><td>" . htmlspecialchars($key) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>🍪 Cookies</h2>";
    if (!empty($_COOKIE)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
        echo "<tr><th>Název</th><th>Hodnota</th></tr>";
        foreach ($_COOKIE as $name => $value) {
            echo "<tr><td>" . htmlspecialchars($name) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
        }
        echo "</table>";
    } else {
        logMessage("⚠️ Žádné cookies nejsou nastavené");
    }
    
    echo "<h2>🌐 Test API volání</h2>";
    
    // Nejprve zkusíme GET požadavek na sessions
    echo "<h3>📡 GET /api/inventory.php?action=sessions</h3>";
    
    $getUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions";
    
    // Vytvoření kontextu s cookies
    $cookieHeader = '';
    if (!empty($_COOKIE)) {
        $cookies = [];
        foreach ($_COOKIE as $name => $value) {
            $cookies[] = "$name=$value";
        }
        $cookieHeader = 'Cookie: ' . implode('; ', $cookies);
    }
    
    $getContext = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'Content-Type: application/json',
                $cookieHeader
            ],
            'timeout' => 10
        ]
    ]);
    
    echo "<p><strong>URL:</strong> <code>" . htmlspecialchars($getUrl) . "</code></p>";
    echo "<p><strong>Headers:</strong> <code>" . htmlspecialchars($cookieHeader) . "</code></p>";
    
    $getResponse = @file_get_contents($getUrl, false, $getContext);
    
    if ($getResponse === false) {
        $error = error_get_last();
        logMessage("❌ GET požadavek selhal: " . ($error['message'] ?? 'Neznámá chyba'), true);
        
        // Zkusíme získat HTTP response headers
        if (isset($http_response_header)) {
            echo "<h4>HTTP Response Headers:</h4>";
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            foreach ($http_response_header as $header) {
                echo htmlspecialchars($header) . "\n";
            }
            echo "</pre>";
        }
    } else {
        $getData = json_decode($getResponse, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($getData['error'])) {
                logMessage("❌ GET API vrátilo chybu: " . $getData['error'], true);
            } else {
                logMessage("✅ GET API volání úspěšné");
                
                if (isset($getData['sessions'])) {
                    logMessage("📊 Vráceno " . count($getData['sessions']) . " relací");
                }
            }
        } else {
            logMessage("❌ GET - Chyba při parsování JSON", true);
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars($getResponse);
            echo "</pre>";
        }
    }
    
    echo "<hr>";
    
    // Nyní zkusíme POST požadavek na vytvoření relace
    echo "<h3>📡 POST /api/inventory.php?action=sessions</h3>";
    
    $postUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions";
    $postData = json_encode([]);
    
    $postContext = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($postData),
                $cookieHeader
            ],
            'content' => $postData,
            'timeout' => 10
        ]
    ]);
    
    echo "<p><strong>URL:</strong> <code>" . htmlspecialchars($postUrl) . "</code></p>";
    echo "<p><strong>POST data:</strong> <code>" . htmlspecialchars($postData) . "</code></p>";
    echo "<p><strong>Headers:</strong> <code>" . htmlspecialchars($cookieHeader) . "</code></p>";
    
    $postResponse = @file_get_contents($postUrl, false, $postContext);
    
    if ($postResponse === false) {
        $error = error_get_last();
        logMessage("❌ POST požadavek selhal: " . ($error['message'] ?? 'Neznámá chyba'), true);
        
        // Zkusíme získat HTTP response headers
        if (isset($http_response_header)) {
            echo "<h4>HTTP Response Headers:</h4>";
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            foreach ($http_response_header as $header) {
                echo htmlspecialchars($header) . "\n";
            }
            echo "</pre>";
        }
    } else {
        $postData = json_decode($postResponse, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($postData['error'])) {
                logMessage("❌ POST API vrátilo chybu: " . $postData['error'], true);
            } else {
                logMessage("✅ POST API volání úspěšné");
                
                if (isset($postData['session_id'])) {
                    logMessage("📊 Vytvořena relace s ID: " . $postData['session_id']);
                }
            }
            
            echo "<h4>Odpověď API:</h4>";
            echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars(json_encode($postData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo "</pre>";
        } else {
            logMessage("❌ POST - Chyba při parsování JSON", true);
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars($postResponse);
            echo "</pre>";
        }
    }
    
    echo "<h2>📝 PHP Error Log</h2>";
    
    // Pokusíme se najít a zobrazit error log
    $errorLogPath = ini_get('error_log');
    if ($errorLogPath && file_exists($errorLogPath)) {
        $lines = file($errorLogPath);
        $recentLines = array_slice($lines, -30); // Posledních 30 řádků
        
        echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; max-height: 400px; overflow-y: auto;'>";
        echo htmlspecialchars(implode('', $recentLines));
        echo "</pre>";
    } else {
        logMessage("⚠️ Error log nenalezen nebo není dostupný");
        logMessage("ℹ️ Error log path: " . ($errorLogPath ?: 'není nastaven'));
    }
    
} catch (Exception $e) {
    logMessage("❌ Celková chyba: " . $e->getMessage(), true);
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "Soubor: " . $e->getFile() . "\n";
    echo "Řádek: " . $e->getLine() . "\n";
    echo "Chyba: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
    echo "</pre>";
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='debug_500_error.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Debug 500 chyby</a>";
echo "<a href='test_create_session_api.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test vytváření relace</a>";
echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
echo "</p>";
?>
