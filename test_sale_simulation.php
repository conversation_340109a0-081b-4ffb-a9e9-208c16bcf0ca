<?php
/**
 * Test simulace prodeje pro ov<PERSON><PERSON><PERSON><PERSON> triggerů
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test simulace prodeje</h1>";
    
    // Zpracování formuláře pro simulaci prodeje
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['simulate_sale'])) {
        $productId = $_POST['product_id'];
        $quantity = floatval($_POST['quantity']);
        
        logMessage("🔄 Simuluji prodej $quantity kusů produktu $productId...");
        
        // Najdeme aktivní inventurní relaci
        $stmt = $pdo->query("
            SELECT id, title
            FROM inventory_sessions
            WHERE status = 'active'
            LIMIT 1
        ");
        $session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$session) {
            logMessage("❌ Nebyla nalezena aktivní inventurní relace", true);
        } else {
            $sessionId = $session['id'];
            
            // Získáme aktuální zadané množství PŘED prodejem
            $stmt = $pdo->prepare("
                SELECT total_zadane_mnozstvi
                FROM inventory_totals
                WHERE product_id = ? AND session_id = ?
            ");
            $stmt->execute([$productId, $sessionId]);
            $currentTotal = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $beforeSale = $currentTotal ? $currentTotal['total_zadane_mnozstvi'] : 0;
            logMessage("✓ Zadané množství PŘED prodejem: $beforeSale");
            
            // Simulujeme prodej vložením do tabulky ticketlines
            try {
                // Nejprve vytvoříme ticket (účtenku)
                $ticketId = 'TEST_' . time();
                
                $stmt = $pdo->prepare("
                    INSERT INTO tickets (id, tickettype, ticketid, person, datenew, status)
                    VALUES (?, 0, ?, 'TEST_USER', NOW(), 0)
                ");
                $stmt->execute([$ticketId, rand(1000, 9999)]);
                
                logMessage("✓ Vytvořena testovací účtenka: $ticketId");
                
                // Nyní vložíme položku prodeje
                $stmt = $pdo->prepare("
                    INSERT INTO ticketlines (ticket, line, product, units, price, taxid)
                    VALUES (?, 1, ?, ?, 100.00, '001')
                ");
                $stmt->execute([$ticketId, $productId, $quantity]);
                
                logMessage("✓ Vložena položka prodeje do ticketlines");
                
                // Počkáme chvilku na zpracování triggeru
                sleep(1);
                
                // Získáme zadané množství PO prodeji
                $stmt = $pdo->prepare("
                    SELECT total_zadane_mnozstvi, last_updated
                    FROM inventory_totals
                    WHERE product_id = ? AND session_id = ?
                ");
                $stmt->execute([$productId, $sessionId]);
                $afterTotal = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $afterSale = $afterTotal ? $afterTotal['total_zadane_mnozstvi'] : 0;
                $lastUpdated = $afterTotal ? $afterTotal['last_updated'] : 'N/A';
                
                logMessage("✓ Zadané množství PO prodeji: $afterSale");
                logMessage("✓ Poslední aktualizace: $lastUpdated");
                
                // Vyhodnocení testu
                $expectedAfter = $beforeSale - $quantity;
                
                if (abs($afterSale - $expectedAfter) < 0.001) {
                    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3 style='color: #155724; margin: 0 0 10px 0;'>🎉 TEST ÚSPĚŠNÝ!</h3>";
                    echo "<p style='color: #155724; margin: 0;'>Trigger funguje správně. Zadané množství se automaticky snížilo z $beforeSale na $afterSale (rozdíl: $quantity).</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
                    echo "<h3 style='color: #721c24; margin: 0 0 10px 0;'>❌ TEST SELHAL!</h3>";
                    echo "<p style='color: #721c24; margin: 0;'>Trigger nefunguje správně. Očekáváno: $expectedAfter, Skutečnost: $afterSale</p>";
                    echo "</div>";
                }
                
                // Vyčištění testovacích dat
                $stmt = $pdo->prepare("DELETE FROM ticketlines WHERE ticket = ?");
                $stmt->execute([$ticketId]);
                
                $stmt = $pdo->prepare("DELETE FROM tickets WHERE id = ?");
                $stmt->execute([$ticketId]);
                
                logMessage("✓ Testovací data byla vyčištěna");
                
            } catch (PDOException $e) {
                logMessage("❌ Chyba při simulaci prodeje: " . $e->getMessage(), true);
            }
        }
    }
    
    // Formulář pro simulaci prodeje
    echo "<h2>Simulace prodeje</h2>";
    
    // Získání seznamu produktů s inventory_totals
    $stmt = $pdo->query("
        SELECT DISTINCT it.product_id, p.name, it.total_zadane_mnozstvi, s.title
        FROM inventory_totals it
        JOIN products p ON it.product_id = p.id
        JOIN inventory_sessions s ON it.session_id = s.id
        WHERE s.status = 'active'
        ORDER BY p.name
        LIMIT 20
    ");
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #856404; margin: 0 0 10px 0;'>⚠ Žádné produkty k testování</h3>";
        echo "<p style='color: #856404; margin: 0;'>Nejsou žádné produkty v inventory_totals pro aktivní inventurní relace. Nejprve vytvořte záznamy v celkové inventuře.</p>";
        echo "</div>";
        
        echo "<p><a href='direct_update_inventory_quantity.php'>Vytvořit záznamy v inventory_totals</a></p>";
    } else {
        echo "<form method='POST'>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>Vybrat</th><th>Produkt</th><th>Aktuální zadané množství</th><th>Inventurní relace</th></tr>";
        
        foreach ($products as $product) {
            echo "<tr>";
            echo "<td><input type='radio' name='product_id' value='" . htmlspecialchars($product['product_id']) . "' required></td>";
            echo "<td>" . htmlspecialchars($product['name']) . "</td>";
            echo "<td>" . htmlspecialchars($product['total_zadane_mnozstvi']) . "</td>";
            echo "<td>" . htmlspecialchars($product['title']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<p>";
        echo "<label>Množství k prodeji: <input type='number' name='quantity' value='1' step='0.001' min='0.001' required></label>";
        echo "</p>";
        
        echo "<p>";
        echo "<button type='submit' name='simulate_sale' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Simulovat prodej</button>";
        echo "</p>";
        
        echo "</form>";
    }
    
    // Kontrola aktuálních triggerů
    echo "<h2>Aktuální triggery</h2>";
    
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($triggers)) {
        logMessage("❌ Žádné triggery nenalezeny v databázi", true);
        echo "<p><a href='create_correct_trigger.php'>Vytvořit triggery</a></p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Název</th><th>Událost</th><th>Tabulka</th><th>Timing</th></tr>";
        
        foreach ($triggers as $trigger) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($trigger['TRIGGER_NAME']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['EVENT_MANIPULATION']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['EVENT_OBJECT_TABLE']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['ACTION_TIMING']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<p><a href='debug_unicenta_sales.php'>Zpět na debug</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
