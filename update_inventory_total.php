<?php
/**
 * Aktualizace zadaného množství v celkové inventuře pro konkrétní produkt
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Aktualizace zadaného množství v celkové inventuře</h1>";
    
    // Kontrola, zda existují aktivní inventory_sessions
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active'");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sessions)) {
        echo "<p style='color: red;'>VAROVÁNÍ: Nejsou žádné aktivní inventory_sessions!</p>";
        exit;
    }
    
    // Získání seznamu produktů v inventory_totals pro aktivní sessions
    $sessionIds = array_column($sessions, 'id');
    $placeholders = implode(',', array_fill(0, count($sessionIds), '?'));
    
    $stmt = $pdo->prepare("
        SELECT DISTINCT product_id 
        FROM inventory_totals 
        WHERE session_id IN ($placeholders)
    ");
    $stmt->execute($sessionIds);
    $products = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Formulář pro aktualizaci zadaného množství
    if (isset($_POST['update_inventory'])) {
        $productId = $_POST['product_id'];
        $units = $_POST['units'];
        
        // Kontrola, zda produkt existuje v inventory_totals
        $stmt = $pdo->prepare("
            SELECT COUNT(*) 
            FROM inventory_totals 
            WHERE product_id = ? 
            AND session_id IN ($placeholders)
        ");
        $params = array_merge([$productId], $sessionIds);
        $stmt->execute($params);
        $exists = $stmt->fetchColumn() > 0;
        
        if (!$exists) {
            echo "<p style='color: red;'>VAROVÁNÍ: Produkt " . htmlspecialchars($productId) . " nebyl nalezen v inventory_totals pro aktivní inventory_sessions!</p>";
        } else {
            // Získání aktuálního zadaného množství
            $stmt = $pdo->prepare("
                SELECT * 
                FROM inventory_totals 
                WHERE product_id = ? 
                AND session_id IN ($placeholders)
            ");
            $params = array_merge([$productId], $sessionIds);
            $stmt->execute($params);
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h2>Aktuální zadané množství pro produkt " . htmlspecialchars($productId) . "</h2>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Session ID</th><th>Product ID</th><th>Total zadané množství</th></tr>";
            
            foreach ($records as $record) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($record['id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['session_id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['product_id']) . "</td>";
                echo "<td>" . htmlspecialchars($record['total_zadane_mnozstvi']) . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
            // Aktualizace zadaného množství
            $stmt = $pdo->prepare("
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - ?,
                    last_updated = NOW()
                WHERE product_id = ?
                AND session_id IN ($placeholders)
            ");
            $params = array_merge([$units, $productId], $sessionIds);
            $stmt->execute($params);
            $updatedCount = $stmt->rowCount();
            
            if ($updatedCount > 0) {
                echo "<p>Zadané množství bylo aktualizováno pro " . htmlspecialchars($updatedCount) . " záznamů.</p>";
                
                // Získání nového zadaného množství
                $stmt = $pdo->prepare("
                    SELECT * 
                    FROM inventory_totals 
                    WHERE product_id = ? 
                    AND session_id IN ($placeholders)
                ");
                $params = array_merge([$productId], $sessionIds);
                $stmt->execute($params);
                $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<h2>Nové zadané množství pro produkt " . htmlspecialchars($productId) . "</h2>";
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>ID</th><th>Session ID</th><th>Product ID</th><th>Total zadané množství</th></tr>";
                
                foreach ($records as $record) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($record['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($record['session_id']) . "</td>";
                    echo "<td>" . htmlspecialchars($record['product_id']) . "</td>";
                    echo "<td>" . htmlspecialchars($record['total_zadane_mnozstvi']) . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p style='color: red;'>VAROVÁNÍ: Zadané množství nebylo aktualizováno!</p>";
                
                // Kontrola SQL dotazu
                echo "<h2>SQL dotaz</h2>";
                echo "<pre>
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = total_zadane_mnozstvi - " . htmlspecialchars($units) . ",
                    last_updated = NOW()
                WHERE product_id = '" . htmlspecialchars($productId) . "'
                AND session_id IN (" . htmlspecialchars(implode(',', $sessionIds)) . ")
                </pre>";
            }
        }
    }
    
    echo "<h2>Aktualizace zadaného množství</h2>";
    echo "<form method='post'>";
    echo "<p>Product ID: <select name='product_id' required>";
    
    foreach ($products as $product) {
        echo "<option value='" . htmlspecialchars($product) . "'>" . htmlspecialchars($product) . "</option>";
    }
    
    echo "</select></p>";
    echo "<p>Units: <input type='text' name='units' value='1' required></p>";
    echo "<p><input type='submit' name='update_inventory' value='Aktualizovat zadané množství'></p>";
    echo "</form>";
    
    // Kontrola SQL dotazu pro aktualizaci zadaného množství
    echo "<h2>SQL dotaz pro aktualizaci zadaného množství</h2>";
    echo "<pre>
    UPDATE inventory_totals
    SET total_zadane_mnozstvi = total_zadane_mnozstvi - ?,
        last_updated = NOW()
    WHERE product_id = ?
    AND session_id IN ($placeholders)
    </pre>";
    
    echo "<p>Parametry:</p>";
    echo "<ul>";
    echo "<li>Units: ?</li>";
    echo "<li>Product ID: ?</li>";
    echo "<li>Session IDs: " . htmlspecialchars(implode(', ', $sessionIds)) . "</li>";
    echo "</ul>";
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
