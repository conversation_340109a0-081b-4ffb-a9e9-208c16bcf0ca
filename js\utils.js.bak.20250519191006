/**
 * Inventurní systém - Pomocn<PERSON>
 */

/**
 * Formátování data
 *
 * @param {string} dateString Datum ve formátu ISO
 * @returns {string} Formátované datum
 */
function formatDate(dateString) {
    if (!dateString) return '-';

    const date = new Date(dateString);
    return date.toLocaleString('cs-CZ');
}

/**
 * Formátování ceny
 *
 * @param {number|string} price Cena
 * @returns {string} Formátovaná cena
 */
function formatPrice(price) {
    // Kontrola, zda je price definováno a je číslo
    if (price === undefined || price === null) {
        console.warn('formatPrice - cena je undefined nebo null');
        price = 0;
    }

    // Převod na číslo, pokud je to string
    if (typeof price === 'string') {
        price = parseFloat(price);
    }

    // Kontrola, zda je price číslo
    if (isNaN(price)) {
        console.warn('formatPrice - cena není č<PERSON>lo:', price);
        price = 0;
    }

    return price.toLocaleString('cs-CZ', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) + ' Kč';
}

/**
 * Formátování množství
 *
 * @param {number|string} quantity Množství
 * @returns {string} Formátované množství
 */
function formatQuantity(quantity) {
    // Kontrola, zda je quantity definováno a je číslo
    if (quantity === undefined || quantity === null) {
        console.warn('formatQuantity - množství je undefined nebo null');
        quantity = 0;
    }

    // Převod na číslo, pokud je to string
    if (typeof quantity === 'string') {
        quantity = parseFloat(quantity);
    }

    // Kontrola, zda je quantity číslo
    if (isNaN(quantity)) {
        console.warn('formatQuantity - množství není číslo:', quantity);
        quantity = 0;
    }

    return quantity.toLocaleString('cs-CZ', { minimumFractionDigits: 3, maximumFractionDigits: 3 });
}

/**
 * Získání názvu role
 *
 * @param {string} role Kód role
 * @returns {string} Název role
 */
function getRoleName(role) {
    switch (role) {
        case 'admin':
            return 'Administrátor';
        case 'manager':
            return 'Manažer';
        case 'user':
            return 'Uživatel';
        default:
            return role;
    }
}

/**
 * Získání názvu stavu
 *
 * @param {string} status Kód stavu
 * @returns {string} Název stavu
 */
function getStatusName(status) {
    switch (status) {
        case 'active':
            return 'Aktivní';
        case 'completed':
            return 'Dokončeno';
        case 'cancelled':
            return 'Zrušeno';
        default:
            return status;
    }
}

/**
 * Zobrazení notifikace
 *
 * @param {string} message Zpráva
 * @param {string} type Typ notifikace (success, error, info)
 */
function showNotification(message, type = 'info') {
    // Odstranění existující notifikace
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // Vytvoření nové notifikace
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Přidání notifikace do dokumentu
    document.body.appendChild(notification);

    // Zobrazení notifikace
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Skrytí notifikace po 3 sekundách
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}

/**
 * Výběr inventurní relace
 *
 * @param {number} sessionId ID relace
 */
function selectSession(sessionId) {
    console.log('selectSession - začátek výběru relace, ID:', sessionId);

    // Přímo načteme konkrétní relaci podle ID
    fetch(`api/inventory.php?action=session&id=${sessionId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.session) {
                console.log('selectSession - nalezena relace s ID:', sessionId);
                // Nastavíme vybranou relaci jako aktivní
                activeSession = data.session;
                showPage('total-inventory');
                showNotification('Inventurní relace byla vybrána', 'success');

                // Načtení inventurních záznamů pro vybranou relaci
                loadInventoryEntries();
            } else {
                console.log('selectSession - relace s ID nenalezena:', sessionId);
                throw new Error('Inventurní relace nebyla nalezena');
            }
        })
        .catch(error => {
            console.error('Chyba při výběru inventurní relace:', error);
            showNotification('Došlo k chybě při výběru inventurní relace: ' + error.message, 'error');
        });
}

/**
 * Dokončení inventurní relace
 *
 * @param {number} sessionId ID relace
 */
function completeSession(sessionId) {
    if (!confirm('Opravdu chcete dokončit tuto inventuru? Tato akce aktualizuje stav zásob v databázi.')) {
        return;
    }

    fetch(`api/inventory.php?action=complete`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ session_id: sessionId })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (activeSession && activeSession.id === sessionId) {
                    activeSession = null;
                    activeSessionEntries = [];
                }

                showPage('dashboard');
                showNotification('Inventurní relace byla úspěšně dokončena', 'success');
            } else {
                showNotification(data.error || 'Došlo k chybě při dokončování inventurní relace', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při dokončování inventurní relace:', error);
            showNotification('Došlo k chybě při dokončování inventurní relace', 'error');
        });
}

/**
 * Zrušení inventurní relace
 *
 * @param {number} sessionId ID relace
 */
function cancelSession(sessionId) {
    if (!confirm('Opravdu chcete zrušit tuto inventuru?')) {
        return;
    }

    fetch(`api/inventory.php?action=session`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id: sessionId, status: 'cancelled', _method: 'PUT' })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (activeSession && activeSession.id === sessionId) {
                    activeSession = null;
                    activeSessionEntries = [];
                }

                showPage('dashboard');
                showNotification('Inventurní relace byla úspěšně zrušena', 'success');
            } else {
                showNotification(data.error || 'Došlo k chybě při rušení inventurní relace', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při rušení inventurní relace:', error);
            showNotification('Došlo k chybě při rušení inventurní relace', 'error');
        });
}

/**
 * Zobrazení detailů inventurní relace
 *
 * @param {number} sessionId ID relace
 */
function viewSessionDetails(sessionId) {
    // Přesměrování na stránku reportů s vybranou relací
    showPage('reports');

    // Nastavení vybrané relace v select boxu
    const exportSession = document.getElementById('export-session');
    if (exportSession) {
        exportSession.value = sessionId;

        // Vyvolání události změny pro načtení dat
        const event = new Event('change');
        exportSession.dispatchEvent(event);
    }
}

/**
 * Zpracování vyhledávání produktů
 *
 * @param {Event} e Událost
 */
function handleProductSearch(e) {
    e.preventDefault();

    // Kontrola, zda je vybrána aktivní relace
    if (!activeSession) {
        showNotification('Nejprve vyberte nebo vytvořte inventurní relaci', 'error');
        return;
    }

    const eanCode = document.getElementById('ean-code').value;

    fetch(`api/products.php?action=search&ean=${eanCode}`)
        .then(response => response.json())
        .then(data => {
            if (data.product) {
                // Zobrazení detailů produktu
                document.getElementById('product-name').textContent = data.product.name;
                document.getElementById('product-ean').textContent = data.product.ean_code;
                document.getElementById('product-category').textContent = data.product.category || '-';
                // Zobrazení nákupní ceny bez DPH
                document.getElementById('product-pricebuy').textContent = formatPrice(data.product.pricebuy);
                document.getElementById('product-price').textContent = formatPrice(data.product.pricesell);
                // Zobrazení DPH
                const taxRate = parseFloat(data.product.tax_rate) || 0;
                document.getElementById('product-tax').textContent = (taxRate * 100).toFixed(0);

                // Zobrazení ceny s DPH
                const priceWithTax = parseFloat(data.product.price_with_tax) || 0;
                document.getElementById('product-price-with-tax').textContent = formatPrice(priceWithTax);

                // Zobrazení ceny s DPH a rabatem
                // Nejprve zkusíme použít hodnoty z databáze, pokud existují
                let priceWithTaxAndRabat = 0;

                if (typeof data.product.pricesellincludingtax !== 'undefined' && data.product.pricesellincludingtax !== null) {
                    priceWithTaxAndRabat = parseFloat(data.product.pricesellincludingtax);
                } else if (typeof data.product.priceretail !== 'undefined' && data.product.priceretail !== null) {
                    priceWithTaxAndRabat = parseFloat(data.product.priceretail);
                } else if (typeof data.product.price_with_tax_and_rabat !== 'undefined' && data.product.price_with_tax_and_rabat !== null) {
                    priceWithTaxAndRabat = parseFloat(data.product.price_with_tax_and_rabat);
                }

                // Pokud nemáme žádnou hodnotu, použijeme cenu s DPH
                if (priceWithTaxAndRabat === 0) {
                    priceWithTaxAndRabat = priceWithTax;
                }

                document.getElementById('product-price-with-tax-and-rabat').textContent = formatPrice(priceWithTaxAndRabat);

                // Přidáme debug výpis do konzole
                console.log('Produkt:', data.product);
                console.log('Nákupní cena bez DPH:', data.product.pricebuy);
                console.log('Cena bez DPH:', data.product.pricesell);
                console.log('Cena s DPH:', priceWithTax);
                console.log('Cena s DPH a rabatem:', priceWithTaxAndRabat);
                document.getElementById('product-stock').textContent = formatQuantity(data.product.current_stock);

                // Zobrazení formuláře pro zadání množství
                document.getElementById('product-details').classList.remove('d-none');
                document.getElementById('zadane-mnozstvi').value = data.product.current_stock;
                document.getElementById('zadane-mnozstvi').focus();

                // Skrytí chybové zprávy
                document.getElementById('product-search-error').classList.add('d-none');
            } else {
                // Zobrazení chybové zprávy
                document.getElementById('product-details').classList.add('d-none');
                document.getElementById('product-search-error').textContent = data.error || 'Produkt nebyl nalezen';
                document.getElementById('product-search-error').classList.remove('d-none');
            }
        })
        .catch(error => {
            console.error('Chyba při vyhledávání produktu:', error);
            document.getElementById('product-details').classList.add('d-none');
            document.getElementById('product-search-error').textContent = 'Došlo k chybě při vyhledávání produktu';
            document.getElementById('product-search-error').classList.remove('d-none');
        });
}

/**
 * Zpracování přidání produktu do inventury
 *
 * @param {Event} e Událost
 */
function handleAddInventoryEntry(e) {
    e.preventDefault();

    // Kontrola, zda je vybrána aktivní relace
    if (!activeSession) {
        showNotification('Nejprve vyberte nebo vytvořte inventurní relaci', 'error');
        return;
    }

    const eanCode = document.getElementById('product-ean').textContent;
    const zadaneMnozstvi = parseFloat(document.getElementById('zadane-mnozstvi').value);

    fetch(`api/inventory.php?action=entries`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            session_id: activeSession.id,
            ean_code: eanCode,
            zadane_mnozstvi: zadaneMnozstvi
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Vyčištění formuláře
                document.getElementById('ean-code').value = '';
                document.getElementById('product-details').classList.add('d-none');

                // Načtení aktualizovaných záznamů
                loadInventoryEntries();

                showNotification('Produkt byl úspěšně přidán do inventury', 'success');
            } else {
                showNotification(data.error || 'Došlo k chybě při přidávání produktu do inventury', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při přidávání produktu do inventury:', error);
            showNotification('Došlo k chybě při přidávání produktu do inventury', 'error');
        });
}

/**
 * Smazání inventurního záznamu
 *
 * @param {number} entryId ID záznamu
 */
function deleteEntry(entryId) {
    if (!confirm('Opravdu chcete smazat tento záznam?')) {
        return;
    }

    console.log('Mazání záznamu s ID:', entryId);

    // Použití jednoduchého PHP souboru pro mazání záznamů
    fetch(`api/simple_delete_entry.php?id=${entryId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
        .then(response => {
            if (!response.ok) {
                console.error('Chyba při mazání záznamu, HTTP status:', response.status);
                return response.json().then(data => {
                    throw new Error(data.error || `HTTP chyba: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                console.log('Záznam byl úspěšně smazán:', data);
                showNotification('Záznam byl úspěšně smazán', 'success');
                loadInventoryEntries();
            } else {
                console.error('Chyba při mazání záznamu:', data.error);
                showNotification(data.error || 'Došlo k chybě při mazání záznamu', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při mazání záznamu:', error);
            showNotification('Došlo k chybě při mazání záznamu: ' + error.message, 'error');
        });
}

/**
 * Zpracování vytvoření nové inventurní relace
 */
function handleCreateSession() {
    fetch(`api/inventory.php?action=sessions`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({})
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Výběr nově vytvořené relace a přesměrování na celkovou inventuru
                fetch(`api/inventory.php?action=session&id=${data.session_id}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(sessionData => {
                        if (sessionData.session) {
                            // Nastavíme vybranou relaci jako aktivní
                            activeSession = sessionData.session;
                            showPage('total-inventory');
                            showNotification('Nová inventurní relace byla vytvořena', 'success');

                            // Načtení inventurních záznamů pro vybranou relaci
                            loadInventoryEntries();
                        } else {
                            throw new Error('Inventurní relace nebyla nalezena');
                        }
                    })
                    .catch(error => {
                        console.error('Chyba při výběru inventurní relace:', error);
                        showNotification('Došlo k chybě při výběru inventurní relace: ' + error.message, 'error');
                    });
            } else {
                showNotification(data.error || 'Došlo k chybě při vytváření inventurní relace', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při vytváření inventurní relace:', error);
            showNotification('Došlo k chybě při vytváření inventurní relace', 'error');
        });
}

/**
 * Smazání dokončené nebo zrušené inventurní relace
 *
 * @param {number} sessionId ID relace
 */
function deleteCompletedSession(sessionId) {
    if (!confirm('Opravdu chcete smazat tuto inventuru? Tato akce je nevratná.')) {
        return;
    }

    fetch(`api/inventory.php?action=session`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ id: sessionId, _method: 'DELETE' })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Inventurní relace byla úspěšně smazána', 'success');

                // Aktualizace seznamu dokončených inventur a dashboardu
                if (window.location.hash === '#reports') {
                    loadReportsData();
                } else {
                    loadDashboardData();
                }
            } else {
                showNotification(data.error || 'Došlo k chybě při mazání inventurní relace', 'error');
            }
        })
        .catch(error => {
            console.error('Chyba při mazání inventurní relace:', error);
            showNotification('Došlo k chybě při mazání inventurní relace', 'error');
        });
}

/**
 * Aktualizace inventurních záznamů
 *
 * @param {number} sessionId ID relace
 */
function refreshInventory(sessionId) {
    if (!confirm('Opravdu chcete aktualizovat zadané množství podle aktuálního stavu zásob? Tato akce přepíše zadané množství.')) {
        return;
    }

    console.log('refreshInventory - začátek aktualizace pro session ID:', sessionId);

    // Zobrazení informace o probíhající aktualizaci
    showNotification('Probíhá aktualizace inventurních záznamů...', 'info');

    fetch(`api/refresh_fix.php`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ session_id: sessionId })
    })
        .then(response => {
            console.log('refreshInventory - odpověď ze serveru:', response);
            console.log('refreshInventory - status odpovědi:', response.status);

            // Pokud odpověď není OK, zkusíme získat text chyby
            if (!response.ok) {
                return response.text().then(text => {
                    console.error('refreshInventory - chybová odpověď:', text);
                    try {
                        // Zkusíme parsovat text jako JSON
                        const errorData = JSON.parse(text);
                        throw new Error(`HTTP error! status: ${response.status}, message: ${errorData.error || 'Neznámá chyba'}`);
                    } catch (e) {
                        // Pokud parsování selže, použijeme původní text
                        throw new Error(`HTTP error! status: ${response.status}, response: ${text}`);
                    }
                });
            }

            return response.json();
        })
        .then(data => {
            console.log('refreshInventory - data z odpovědi:', data);

            if (data.success) {
                showNotification('Inventurní záznamy byly úspěšně aktualizovány', 'success');

                // Aktualizace inventurních záznamů na aktuální stránce
                if (window.location.hash === '#total-inventory' || document.getElementById('total-inventory-page').classList.contains('d-block') || !document.getElementById('total-inventory-page').classList.contains('d-none')) {
                    loadTotalInventoryData();
                } else {
                    loadInventoryEntries();
                }
            } else {
                console.error('refreshInventory - chyba v odpovědi:', data.error);
                showNotification(data.error || 'Došlo k chybě při aktualizaci inventurních záznamů', 'error');
            }
        })
        .catch(error => {
            console.error('refreshInventory - chyba při aktualizaci:', error);
            console.error('refreshInventory - stack trace:', error.stack);
            showNotification('Došlo k chybě při aktualizaci inventurních záznamů: ' + error.message, 'error');
        });
}
