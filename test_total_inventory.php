<?php
/**
 * Skript pro testování celkové inventury
 */

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Spuštění session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test celkové inventury</h1>";
    echo "<p style='color: green;'>✓ Připojení k databázi bylo úspěšné.</p>";
    
    // Kontrola přihlášení
    if (!isLoggedIn()) {
        echo "<p style='color: red;'>✗ Uživatel není př<PERSON>. Pro testování celkové inventury je nutné být přihlá<PERSON>en.</p>";
        echo "<p><a href='test_auth.php'>Přejít na stránku pro přihlášení</a></p>";
    } else {
        echo "<p style='color: green;'>✓ Uživatel je přihlášen jako: " . htmlspecialchars($_SESSION['user']['username']) . " (role: " . htmlspecialchars($_SESSION['user']['role']) . ")</p>";
        
        // Kontrola, zda je uživatel admin nebo manager
        if (!isAdminOrManager()) {
            echo "<p style='color: red;'>✗ Uživatel nemá oprávnění pro zobrazení celkové inventury. Je nutné být admin nebo manager.</p>";
        } else {
            echo "<p style='color: green;'>✓ Uživatel má oprávnění pro zobrazení celkové inventury.</p>";
            
            // Získání seznamu aktivních inventurních relací
            $stmt = $pdo->query("
                SELECT 
                    s.id, 
                    s.start_time, 
                    s.status, 
                    u.username, 
                    COUNT(DISTINCT e.user_id) AS user_count,
                    COUNT(e.id) AS entry_count
                FROM 
                    inventory_sessions s
                JOIN 
                    inventory_users u ON s.user_id = u.id
                LEFT JOIN 
                    inventory_entries e ON s.id = e.session_id AND e.status = 'active'
                WHERE 
                    s.status = 'active'
                GROUP BY 
                    s.id, s.start_time, s.status, u.username
                ORDER BY 
                    s.start_time DESC
            ");
            
            $activeSessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h2>Aktivní inventurní relace</h2>";
            
            if (empty($activeSessions)) {
                echo "<p>Žádné aktivní inventurní relace nebyly nalezeny.</p>";
            } else {
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>ID</th><th>Čas zahájení</th><th>Stav</th><th>Vytvořil</th><th>Počet uživatelů</th><th>Počet záznamů</th><th>Akce</th></tr>";
                
                foreach ($activeSessions as $session) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($session['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['start_time']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['status']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['username']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['user_count']) . "</td>";
                    echo "<td>" . htmlspecialchars($session['entry_count']) . "</td>";
                    echo "<td>";
                    echo "<form method='post' style='display: inline;'>";
                    echo "<input type='hidden' name='session_id' value='" . htmlspecialchars($session['id']) . "'>";
                    echo "<input type='submit' name='view_total' value='Zobrazit celkovou inventuru'>";
                    echo "</form>";
                    echo "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            }
            
            // Zobrazení celkové inventury pro vybranou relaci
            if (isset($_POST['view_total']) || isset($_POST['update_totals'])) {
                $sessionId = $_POST['session_id'] ?? null;
                
                if ($sessionId) {
                    // Získání informací o relaci
                    $stmt = $pdo->prepare("
                        SELECT 
                            s.id, 
                            s.start_time, 
                            s.status, 
                            u.username
                        FROM 
                            inventory_sessions s
                        JOIN 
                            inventory_users u ON s.user_id = u.id
                        WHERE 
                            s.id = :session_id
                    ");
                    
                    $stmt->execute(['session_id' => $sessionId]);
                    $session = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($session) {
                        echo "<h2>Celková inventura pro relaci #" . htmlspecialchars($session['id']) . "</h2>";
                        echo "<p>Čas zahájení: " . htmlspecialchars($session['start_time']) . "</p>";
                        echo "<p>Stav: " . htmlspecialchars($session['status']) . "</p>";
                        echo "<p>Vytvořil: " . htmlspecialchars($session['username']) . "</p>";
                        
                        // Formulář pro aktualizaci celkové inventury
                        echo "<form method='post'>";
                        echo "<input type='hidden' name='session_id' value='" . htmlspecialchars($session['id']) . "'>";
                        echo "<p><input type='submit' name='update_totals' value='Aktualizovat celkovou inventuru'></p>";
                        echo "</form>";
                        
                        // Zpracování aktualizace celkové inventury
                        if (isset($_POST['update_totals'])) {
                            try {
                                // Aktualizace celkového zadaného množství v inventory_totals
                                $stmt = $pdo->prepare("
                                    INSERT INTO inventory_totals (
                                        session_id, 
                                        product_id, 
                                        total_zadane_mnozstvi
                                    )
                                    SELECT 
                                        :session_id, 
                                        e.product_id, 
                                        SUM(e.zadane_mnozstvi)
                                    FROM 
                                        inventory_entries e
                                    WHERE 
                                        e.session_id = :session_id
                                        AND e.status = 'active'
                                    GROUP BY 
                                        e.product_id
                                    ON DUPLICATE KEY UPDATE
                                        total_zadane_mnozstvi = VALUES(total_zadane_mnozstvi)
                                ");
                                
                                $stmt->execute(['session_id' => $sessionId]);
                                
                                echo "<p style='color: green;'>✓ Celková inventura byla aktualizována.</p>";
                            } catch (PDOException $e) {
                                echo "<p style='color: red;'>✗ Chyba při aktualizaci celkové inventury: " . $e->getMessage() . "</p>";
                            }
                        }
                        
                        // Získání seznamu produktů v celkové inventuře
                        $stmt = $pdo->prepare("
                            SELECT 
                                p.id AS product_id,
                                p.code AS ean_code,
                                p.name AS product_name,
                                c.name AS category,
                                p.pricebuy,
                                t.rate AS tax_rate,
                                p.pricesell,
                                COALESCE(s.units, 0) AS current_stock,
                                COALESCE(it.total_zadane_mnozstvi, 0) AS zadane_mnozstvi,
                                (COALESCE(it.total_zadane_mnozstvi, 0) - COALESCE(s.units, 0)) AS difference,
                                GROUP_CONCAT(DISTINCT u.username) AS users,
                                it.last_updated
                            FROM 
                                inventory_totals it
                            JOIN 
                                products p ON it.product_id = p.id
                            LEFT JOIN 
                                categories c ON p.category = c.id
                            LEFT JOIN 
                                taxes t ON p.taxcat = t.id
                            LEFT JOIN 
                                stockcurrent s ON p.id = s.product
                            LEFT JOIN 
                                inventory_entries e ON it.session_id = e.session_id AND it.product_id = e.product_id AND e.status = 'active'
                            LEFT JOIN 
                                inventory_users u ON e.user_id = u.id
                            WHERE 
                                it.session_id = :session_id
                            GROUP BY 
                                p.id, p.code, p.name, c.name, p.pricebuy, t.rate, p.pricesell, s.units, it.total_zadane_mnozstvi, it.last_updated
                            ORDER BY 
                                p.name
                        ");
                        
                        $stmt->execute(['session_id' => $sessionId]);
                        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        // Získání seznamu produktů, které nejsou v inventuře
                        $showMissing = isset($_GET['show_missing']) && $_GET['show_missing'] === '1';
                        
                        if ($showMissing) {
                            $stmt = $pdo->prepare("
                                SELECT 
                                    p.id AS product_id,
                                    p.code AS ean_code,
                                    p.name AS product_name,
                                    c.name AS category,
                                    p.pricebuy,
                                    t.rate AS tax_rate,
                                    p.pricesell,
                                    COALESCE(s.units, 0) AS current_stock,
                                    0 AS zadane_mnozstvi,
                                    (0 - COALESCE(s.units, 0)) AS difference,
                                    NULL AS users,
                                    NULL AS last_updated
                                FROM 
                                    products p
                                LEFT JOIN 
                                    categories c ON p.category = c.id
                                LEFT JOIN 
                                    taxes t ON p.taxcat = t.id
                                LEFT JOIN 
                                    stockcurrent s ON p.id = s.product
                                LEFT JOIN 
                                    inventory_totals it ON p.id = it.product_id AND it.session_id = :session_id
                                WHERE 
                                    it.id IS NULL
                                ORDER BY 
                                    p.name
                            ");
                            
                            $stmt->execute(['session_id' => $sessionId]);
                            $missingProducts = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            // Sloučení seznamů produktů
                            $products = array_merge($products, $missingProducts);
                            
                            // Seřazení podle názvu produktu
                            usort($products, function($a, $b) {
                                return strcmp($a['product_name'], $b['product_name']);
                            });
                        }
                        
                        // Přepínač pro zobrazení chybějících produktů
                        echo "<p>";
                        if ($showMissing) {
                            echo "<a href='?'>Skrýt produkty, které nejsou v inventuře</a>";
                        } else {
                            echo "<a href='?show_missing=1'>Zobrazit produkty, které nejsou v inventuře</a>";
                        }
                        echo "</p>";
                        
                        if (empty($products)) {
                            echo "<p>Žádné produkty nebyly nalezeny.</p>";
                        } else {
                            echo "<h3>Seznam produktů v celkové inventuře</h3>";
                            echo "<p>Celkový počet produktů: " . count($products) . "</p>";
                            echo "<table border='1' cellpadding='5' cellspacing='0'>";
                            echo "<tr><th>EAN kód</th><th>Název produktu</th><th>Kategorie</th><th>Nákupní cena</th><th>DPH</th><th>Prodejní cena</th><th>Aktuální stav</th><th>Zadané množství</th><th>Rozdíl</th><th>Uživatelé</th><th>Poslední aktualizace</th></tr>";
                            
                            foreach ($products as $product) {
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($product['ean_code']) . "</td>";
                                echo "<td>" . htmlspecialchars($product['product_name']) . "</td>";
                                echo "<td>" . htmlspecialchars($product['category'] ?? '') . "</td>";
                                echo "<td>" . htmlspecialchars($product['pricebuy']) . "</td>";
                                echo "<td>" . htmlspecialchars($product['tax_rate'] ?? '') . "%</td>";
                                echo "<td>" . htmlspecialchars($product['pricesell']) . "</td>";
                                echo "<td>" . htmlspecialchars($product['current_stock']) . "</td>";
                                echo "<td>" . htmlspecialchars($product['zadane_mnozstvi']) . "</td>";
                                echo "<td>" . htmlspecialchars($product['difference']) . "</td>";
                                echo "<td>" . htmlspecialchars($product['users'] ?? '') . "</td>";
                                echo "<td>" . htmlspecialchars($product['last_updated'] ?? '') . "</td>";
                                echo "</tr>";
                            }
                            
                            echo "</table>";
                        }
                    } else {
                        echo "<p style='color: red;'>✗ Inventurní relace nebyla nalezena.</p>";
                    }
                } else {
                    echo "<p style='color: red;'>✗ Nebylo zadáno ID inventurní relace.</p>";
                }
            }
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
