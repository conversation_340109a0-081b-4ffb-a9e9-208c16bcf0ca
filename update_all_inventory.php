<?php
/**
 * Aktualizace zadaného množství pro všechny produkty v aktivních inventurních relacích
 *
 * Tento skript aktualizuje zadané množství (zadane_mnozstvi) v tabulce inventory_entries
 * pro všechny produkty v aktivních inventurních relacích na základě aktuálního stavu v tabulce stockcurrent.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    $results = [];

    // Kontrola, zda je uživatel přihlášen a má oprávnění
    if (!isLoggedIn()) {
        echo "<h1>Neautorizovaný přístup</h1>";
        echo "<p>Pro přístup k této stránce musíte být přihláš<PERSON>.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    $user = getCurrentUser();
    if (!isAdminOrManager()) {
        echo "<h1>Nedostatečná oprávnění</h1>";
        echo "<p>Pro přístup k této stránce musíte mít oprávnění administrátora nebo manažera.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    // Zpracování formuláře pro aktualizaci zadaného množství
    $updateResults = [];
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update'])) {
        try {
            // Začátek transakce
            $pdo->beginTransaction();
            
            // Získání seznamu všech produktů v aktivních inventurních relacích
            $stmt = $pdo->query("
                SELECT DISTINCT ie.product_id, ie.session_id
                FROM inventory_entries ie
                JOIN inventory_sessions is2 ON ie.session_id = is2.id
                WHERE is2.status = 'active'
                AND ie.status = 'active'
            ");
            $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $updateResults['products_count'] = count($products);
            $updateResults['updated_count'] = 0;
            $updateResults['products'] = [];
            
            foreach ($products as $product) {
                $productId = $product['product_id'];
                $sessionId = $product['session_id'];
                
                // Získání aktuálního stavu z tabulky stockcurrent
                $stmt = $pdo->prepare("
                    SELECT units
                    FROM stockcurrent
                    WHERE product = ?
                ");
                $stmt->execute([$productId]);
                $stockcurrent = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $currentUnits = $stockcurrent ? $stockcurrent['units'] : 0;
                
                // Získání informací o produktu
                $stmt = $pdo->prepare("
                    SELECT name, code
                    FROM products
                    WHERE id = ?
                ");
                $stmt->execute([$productId]);
                $productInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Získání informací o inventurní relaci
                $stmt = $pdo->prepare("
                    SELECT CONCAT('Inventura #', id) AS name
                    FROM inventory_sessions
                    WHERE id = ?
                ");
                $stmt->execute([$sessionId]);
                $sessionInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Aktualizace zadaného množství pro administrátory a manažery
                $stmt = $pdo->prepare("
                    UPDATE inventory_entries ie
                    JOIN inventory_users iu ON ie.user_id = iu.id
                    SET ie.zadane_mnozstvi = ?
                    WHERE ie.product_id = ?
                    AND ie.session_id = ?
                    AND ie.status = 'active'
                    AND iu.role IN ('admin', 'manager')
                ");
                $stmt->execute([$currentUnits, $productId, $sessionId]);
                
                $rowCount = $stmt->rowCount();
                if ($rowCount > 0) {
                    $updateResults['updated_count']++;
                    
                    $updateResults['products'][] = [
                        'id' => $productId,
                        'name' => $productInfo ? $productInfo['name'] : 'Neznámý produkt',
                        'code' => $productInfo ? $productInfo['code'] : '',
                        'session_id' => $sessionId,
                        'session_name' => $sessionInfo ? $sessionInfo['name'] : "Inventura #$sessionId",
                        'current_units' => $currentUnits,
                        'updated' => true
                    ];
                }
            }
            
            // Commit transakce
            $pdo->commit();
            
            $updateResults['success'] = true;
            $updateResults['message'] = "Aktualizace zadaného množství byla úspěšně dokončena. Aktualizováno {$updateResults['updated_count']} z {$updateResults['products_count']} produktů.";
        } catch (Exception $e) {
            // Rollback transakce v případě chyby
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $updateResults['error'] = 'Došlo k chybě při aktualizaci zadaného množství: ' . $e->getMessage();
        }
    }

    // Zobrazení HTML
    echo "<!DOCTYPE html>
<html lang='cs'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Aktualizace zadaného množství pro všechny produkty</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        button { background-color: #4CAF50; color: white; border: none; cursor: pointer; padding: 10px 15px; font-size: 16px; }
        button:hover { background-color: #45a049; }
        .success { color: green; padding: 10px; background-color: #f0fff0; border: 1px solid #d0e9c6; border-radius: 4px; margin: 10px 0; }
        .error { color: red; padding: 10px; background-color: #fff0f0; border: 1px solid #e9c6c6; border-radius: 4px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f1f1f1; }
        .actions { margin: 20px 0; }
        .actions a { 
            display: inline-block; 
            padding: 10px 15px; 
            background-color: #4CAF50; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin-right: 10px; 
        }
        .actions a:hover { background-color: #45a049; }
        .updated { color: green; }
        .not-updated { color: red; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Aktualizace zadaného množství pro všechny produkty</h1>
        
        <div class='actions'>
            <a href='index.html'>Zpět na hlavní stránku</a>
            <a href='sync_inventory.php'>Synchronizace všech produktů</a>
            <a href='sync_product.php'>Synchronizace konkrétního produktu</a>
            <a href='check_recent_sales.php'>Kontrola posledních prodejů</a>
            <a href='fix_all_products.php'>Oprava zadaného množství pro všechny produkty</a>
        </div>";
        
    if (!empty($updateResults)) {
        if (isset($updateResults['success']) && $updateResults['success']) {
            echo "<div class='success'>{$updateResults['message']}</div>";
            
            if (!empty($updateResults['products'])) {
                echo "<h2>Aktualizované produkty</h2>";
                echo "<table>";
                echo "<thead><tr><th>ID</th><th>Název</th><th>Kód</th><th>Inventurní relace</th><th>Aktuální stav</th></tr></thead>";
                echo "<tbody>";
                
                foreach ($updateResults['products'] as $product) {
                    echo "<tr>";
                    echo "<td>{$product['id']}</td>";
                    echo "<td>{$product['name']}</td>";
                    echo "<td>{$product['code']}</td>";
                    echo "<td>{$product['session_name']}</td>";
                    echo "<td>{$product['current_units']}</td>";
                    echo "</tr>";
                }
                
                echo "</tbody></table>";
            }
        } elseif (isset($updateResults['error'])) {
            echo "<div class='error'>{$updateResults['error']}</div>";
        }
    }
        
    echo "<form method='post'>
            <div class='form-group'>
                <p>Tato akce aktualizuje zadané množství pro všechny produkty ve všech aktivních inventurních relacích na základě aktuálního stavu v tabulce stockcurrent.</p>
                <button type='submit' name='update'>Aktualizovat zadané množství pro všechny produkty</button>
            </div>
        </form>
    </div>
</body>
</html>";

} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
