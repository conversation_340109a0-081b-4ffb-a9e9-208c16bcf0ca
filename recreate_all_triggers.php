<?php
/**
 * Odstranění všech triggerů a jejich opětovné vytvoření
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    $results = [];
    
    // Získání informací o databázi
    $stmt = $pdo->query("SELECT DATABASE() AS db_name");
    $dbInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    $dbName = $dbInfo['db_name'];
    
    echo "<h1>Odstranění všech triggerů a jejich opětovné vytvoření v databázi: " . htmlspecialchars($dbName) . "</h1>";
    
    // Získání všech triggerů v databázi
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Odstranění všech triggerů
    foreach ($triggers as $trigger) {
        try {
            $pdo->exec("DROP TRIGGER IF EXISTS `$trigger`");
            $results['drop_trigger_' . $trigger] = 'Trigger odstraněn';
        } catch (PDOException $e) {
            $results['drop_trigger_' . $trigger] = 'Chyba při odstraňování triggeru: ' . $e->getMessage();
        }
    }
    
    // Vytvoření nových triggerů
    
    // Trigger pro aktualizaci stockcurrent při INSERT do ticketlines
    try {
        $pdo->exec("
            CREATE TRIGGER `update_stockcurrent_after_ticketlines_insert`
            AFTER INSERT ON `ticketlines`
            FOR EACH ROW
            BEGIN
                -- Logování vložení nového záznamu pro diagnostiku
                INSERT INTO ticketlines_log (ticket_id, line, product, units)
                VALUES (NEW.ticket, NEW.line, NEW.product, NEW.units);

                -- Aktualizace stockcurrent - snížení stavu zásob
                UPDATE stockcurrent
                SET units = units - NEW.units
                WHERE product = NEW.product;

                -- Pokud záznam neexistuje, vytvoříme ho
                IF ROW_COUNT() = 0 THEN
                    INSERT INTO stockcurrent (product, location, units)
                    VALUES (NEW.product, 0, -NEW.units);
                END IF;
            END
        ");
        $results['create_ticketlines_insert_trigger'] = 'Trigger byl vytvořen';
    } catch (PDOException $e) {
        $results['create_ticketlines_insert_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Trigger pro aktualizaci stockcurrent při UPDATE v ticketlines
    try {
        $pdo->exec("
            CREATE TRIGGER `update_stockcurrent_after_ticketlines_update`
            AFTER UPDATE ON `ticketlines`
            FOR EACH ROW
            BEGIN
                -- Logování aktualizace záznamu pro diagnostiku
                INSERT INTO ticketlines_log (ticket_id, line, product, units)
                VALUES (NEW.ticket, NEW.line, NEW.product, NEW.units - OLD.units);

                -- Aktualizace stockcurrent - úprava stavu zásob podle změny množství
                UPDATE stockcurrent
                SET units = units - (NEW.units - OLD.units)
                WHERE product = NEW.product;
            END
        ");
        $results['create_ticketlines_update_trigger'] = 'Trigger byl vytvořen';
    } catch (PDOException $e) {
        $results['create_ticketlines_update_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Trigger pro aktualizaci stockcurrent při DELETE v ticketlines
    try {
        $pdo->exec("
            CREATE TRIGGER `update_stockcurrent_after_ticketlines_delete`
            AFTER DELETE ON `ticketlines`
            FOR EACH ROW
            BEGIN
                -- Logování smazání záznamu pro diagnostiku
                INSERT INTO ticketlines_log (ticket_id, line, product, units)
                VALUES (OLD.ticket, OLD.line, OLD.product, -OLD.units);

                -- Aktualizace stockcurrent - vrácení zboží do skladu
                UPDATE stockcurrent
                SET units = units + OLD.units
                WHERE product = OLD.product;

                -- Pokud záznam neexistuje, vytvoříme ho
                IF ROW_COUNT() = 0 THEN
                    INSERT INTO stockcurrent (product, location, units)
                    VALUES (OLD.product, 0, OLD.units);
                END IF;
            END
        ");
        $results['create_ticketlines_delete_trigger'] = 'Trigger byl vytvořen';
    } catch (PDOException $e) {
        $results['create_ticketlines_delete_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Trigger pro aktualizaci inventory_totals při UPDATE v stockcurrent
    try {
        $pdo->exec("
            CREATE TRIGGER `update_inventory_entries_after_stockcurrent_update`
            AFTER UPDATE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals pouze při snížení stavu (prodej)
                    IF difference > 0 THEN
                        -- Aktualizace celkových součtů v inventory_totals
                        UPDATE inventory_totals
                        SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                        WHERE product_id = NEW.product
                        AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                    END IF;
                END IF;
            END
        ");
        $results['create_stockcurrent_update_trigger'] = 'Trigger byl vytvořen';
    } catch (PDOException $e) {
        $results['create_stockcurrent_update_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Trigger pro aktualizaci inventory_totals při INSERT do inventory_entries
    try {
        $pdo->exec("
            CREATE TRIGGER `update_inventory_totals_after_entries_insert`
            AFTER INSERT ON `inventory_entries`
            FOR EACH ROW
            BEGIN
                INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
                VALUES (NEW.session_id, NEW.product_id, NEW.zadane_mnozstvi)
                ON DUPLICATE KEY UPDATE
                    total_zadane_mnozstvi = total_zadane_mnozstvi + NEW.zadane_mnozstvi;
            END
        ");
        $results['create_entries_insert_trigger'] = 'Trigger byl vytvořen';
    } catch (PDOException $e) {
        $results['create_entries_insert_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Trigger pro aktualizaci inventory_totals při DELETE v inventory_entries
    try {
        $pdo->exec("
            CREATE TRIGGER `update_inventory_totals_after_entries_delete`
            AFTER DELETE ON `inventory_entries`
            FOR EACH ROW
            BEGIN
                -- Aktualizace celkového součtu
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = (
                    SELECT COALESCE(SUM(zadane_mnozstvi), 0)
                    FROM inventory_entries
                    WHERE session_id = OLD.session_id
                    AND product_id = OLD.product_id
                    AND status = 'active'
                )
                WHERE session_id = OLD.session_id
                AND product_id = OLD.product_id;

                -- Pokud není žádný záznam, odstraníme záznam z inventory_totals
                DELETE FROM inventory_totals
                WHERE session_id = OLD.session_id
                AND product_id = OLD.product_id
                AND total_zadane_mnozstvi = 0;
            END
        ");
        $results['create_entries_delete_trigger'] = 'Trigger byl vytvořen';
    } catch (PDOException $e) {
        $results['create_entries_delete_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Výpis výsledků
    echo "<h2>Výsledky</h2>";
    echo "<pre>";
    print_r($results);
    echo "</pre>";
    
    echo "<p>Všechny triggery byly odstraněny a znovu vytvořeny.</p>";
    echo "<p><a href='check_duplicate_triggers.php'>Zkontrolovat duplicitní triggery</a></p>";
    echo "<p><a href='debug_stockcurrent_changes.php'>Zkontrolovat logy</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
