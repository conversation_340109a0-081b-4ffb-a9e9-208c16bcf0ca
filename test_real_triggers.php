<?php
/**
 * Test triggerů na skutečných datech UniCenta
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();

    echo "<h1>🧪 Test triggerů na skutečných datech</h1>";

    // KROK 1: Kontrola existence triggerů
    logMessage("🔍 KROK 1: Kontroluji existenci triggerů...");

    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        ORDER BY EVENT_OBJECT_TABLE, TRIGGER_NAME
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($triggers)) {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #721c24;'>❌ ŽÁDNÉ TRIGGERY NEJSOU AKTIVNÍ</h3>";
        echo "<p><a href='create_correct_triggers.php'>Vytvořit triggery</a></p>";
        echo "</div>";
        exit;
    }

    logMessage("✓ Nalezeno " . count($triggers) . " triggerů:");
    foreach ($triggers as $trigger) {
        logMessage("  - " . $trigger['TRIGGER_NAME'] . " (" . $trigger['EVENT_MANIPULATION'] . " na " . $trigger['EVENT_OBJECT_TABLE'] . ")");
    }

    // KROK 2: Kontrola inventory_totals
    logMessage("📋 KROK 2: Kontroluji stav inventory_totals...");

    $stmt = $pdo->query("
        SELECT
            it.session_id,
            COALESCE(s.session_name, CONCAT('Session ', s.id)) as session_name,
            s.status,
            COUNT(*) as product_count,
            SUM(it.total_zadane_mnozstvi) as total_quantity
        FROM inventory_totals it
        LEFT JOIN inventory_sessions s ON it.session_id = s.id
        GROUP BY it.session_id, s.session_name, s.status
        ORDER BY s.status DESC, s.id DESC
    ");
    $sessions_stats = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($sessions_stats)) {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #856404;'>⚠ ŽÁDNÉ ZÁZNAMY V INVENTORY_TOTALS</h3>";
        echo "<p>Triggery nebudou mít co aktualizovat!</p>";
        echo "<p><a href='direct_update_inventory_quantity.php'>Vytvořit záznamy</a></p>";
        echo "</div>";
        exit;
    }

    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Session ID</th><th>Název</th><th>Status</th><th>Počet produktů</th><th>Celkové množství</th></tr>";
    foreach ($sessions_stats as $session) {
        $status_color = $session['status'] === 'active' ? 'green' : 'gray';
        echo "<tr>";
        echo "<td>" . htmlspecialchars($session['session_id']) . "</td>";
        echo "<td>" . htmlspecialchars($session['session_name']) . "</td>";
        echo "<td style='color: $status_color;'><strong>" . htmlspecialchars($session['status']) . "</strong></td>";
        echo "<td>" . htmlspecialchars($session['product_count']) . "</td>";
        echo "<td>" . htmlspecialchars($session['total_quantity']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // KROK 3: Najdeme produkty pro test
    logMessage("🎯 KROK 3: Hledám produkty pro test...");

    $stmt = $pdo->query("
        SELECT
            it.product_id,
            p.name as product_name,
            it.total_zadane_mnozstvi,
            sc.units as current_stock
        FROM inventory_totals it
        LEFT JOIN products p ON it.product_id = p.id
        LEFT JOIN stockcurrent sc ON it.product_id = sc.product
        WHERE it.session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
        AND it.total_zadane_mnozstvi > 0
        ORDER BY it.total_zadane_mnozstvi DESC
        LIMIT 5
    ");
    $test_products = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($test_products)) {
        logMessage("❌ Žádné produkty s kladným zadaným množstvím pro test!", true);
        exit;
    }

    echo "<h3>Produkty pro test:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr><th>Product ID</th><th>Název</th><th>Zadané množství</th><th>Aktuální sklad</th></tr>";
    foreach ($test_products as $product) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($product['product_id']) . "</td>";
        echo "<td>" . htmlspecialchars($product['product_name'] ?? 'N/A') . "</td>";
        echo "<td><strong>" . htmlspecialchars($product['total_zadane_mnozstvi']) . "</strong></td>";
        echo "<td>" . htmlspecialchars($product['current_stock'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // KROK 4: Simulace prodeje
    $test_product = $test_products[0];
    $test_quantity = 1; // Prodáme 1 kus

    logMessage("🛒 KROK 4: Simuluji prodej produktu...");
    logMessage("Produkt: " . ($test_product['product_name'] ?? $test_product['product_id']));
    logMessage("Množství k prodeji: $test_quantity");
    logMessage("Zadané množství před prodejem: " . $test_product['total_zadane_mnozstvi']);

    // Získáme posledního ticket ID
    $stmt = $pdo->query("SELECT MAX(CAST(SUBSTRING(id, 1, 8) AS UNSIGNED)) as max_ticket FROM tickets");
    $max_ticket = $stmt->fetchColumn();
    $new_ticket_id = str_pad($max_ticket + 1, 8, '0', STR_PAD_LEFT) . '-' . uniqid();

    // Získáme posledního line číslo
    $stmt = $pdo->query("SELECT MAX(line) as max_line FROM ticketlines WHERE ticket LIKE '" . substr($new_ticket_id, 0, 8) . "%'");
    $max_line = $stmt->fetchColumn() ?? -1;
    $new_line = $max_line + 1;

    try {
        // Začneme transakci
        $pdo->beginTransaction();

        // Vložíme nový řádek prodeje (tím se spustí trigger)
        $stmt = $pdo->prepare("
            INSERT INTO ticketlines (ticket, line, product, attributesetinstance_id, units, price, taxid, attributes, updated)
            VALUES (?, ?, ?, NULL, ?, 33.25, '001', NULL, NOW())
        ");
        $stmt->execute([$new_ticket_id, $new_line, $test_product['product_id'], $test_quantity]);

        logMessage("✓ Vložen testovací řádek prodeje");

        // Zkontrolujeme, zda se zadané množství snížilo
        $stmt = $pdo->prepare("
            SELECT total_zadane_mnozstvi
            FROM inventory_totals
            WHERE product_id = ? AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
        ");
        $stmt->execute([$test_product['product_id']]);
        $new_quantity = $stmt->fetchColumn();

        $expected_quantity = $test_product['total_zadane_mnozstvi'] - $test_quantity;

        if ($new_quantity == $expected_quantity) {
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3 style='color: #155724;'>✅ TRIGGER FUNGUJE SPRÁVNĚ!</h3>";
            echo "<p style='color: #155724;'>Zadané množství se snížilo z <strong>" . $test_product['total_zadane_mnozstvi'] . "</strong> na <strong>$new_quantity</strong></p>";
            echo "<p style='color: #155724;'>Rozdíl: <strong>-$test_quantity</strong> (očekáváno)</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3 style='color: #721c24;'>❌ TRIGGER NEFUNGUJE!</h3>";
            echo "<p style='color: #721c24;'>Zadané množství: <strong>$new_quantity</strong> (očekáváno: <strong>$expected_quantity</strong>)</p>";
            echo "</div>";
        }

        // Rollback transakce (nechceme skutečně změnit data)
        $pdo->rollBack();
        logMessage("🔄 Transakce byla vrácena zpět (testovací data nebyla uložena)");

    } catch (PDOException $e) {
        $pdo->rollBack();
        logMessage("❌ Chyba při testu: " . $e->getMessage(), true);
    }

    // KROK 5: Kontrola posledních prodejů
    logMessage("📊 KROK 5: Kontroluji posledních 5 skutečných prodejů...");

    $stmt = $pdo->query("
        SELECT
            tl.ticket,
            tl.line,
            tl.product,
            p.name as product_name,
            tl.units,
            tl.price,
            tl.updated
        FROM ticketlines tl
        LEFT JOIN products p ON tl.product = p.id
        ORDER BY tl.updated DESC
        LIMIT 5
    ");
    $recent_sales = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (!empty($recent_sales)) {
        echo "<h3>Posledních 5 prodejů:</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>Ticket</th><th>Line</th><th>Produkt</th><th>Množství</th><th>Cena</th><th>Čas</th></tr>";
        foreach ($recent_sales as $sale) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars(substr($sale['ticket'], 0, 15)) . "...</td>";
            echo "<td>" . htmlspecialchars($sale['line']) . "</td>";
            echo "<td>" . htmlspecialchars($sale['product_name'] ?? substr($sale['product'], 0, 20) . '...') . "</td>";
            echo "<td><strong>" . htmlspecialchars($sale['units']) . "</strong></td>";
            echo "<td>" . htmlspecialchars($sale['price']) . "</td>";
            echo "<td>" . htmlspecialchars($sale['updated']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    // Navigace
    echo "<h2>🔧 Další akce</h2>";
    echo "<p><a href='monitor_sales_real_time.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Monitorovat prodeje v reálném čase</a></p>";
    echo "<p><a href='create_correct_triggers.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Znovu vytvořit triggery</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";

} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
