<?php
/**
 * Synchronizace zadaného množství v celkové inventuře
 * 
 * Tento skript lze spouštět pravidelně (např. pomocí cronu) pro synchronizaci
 * zadaného množství v celkové inventuře s aktuálním stavem zásob.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    // Kontrola, zda existuje tabulka previous_stock
    $stmt = $pdo->query("SHOW TABLES LIKE 'previous_stock'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Vytvoření tabulky previous_stock
        $pdo->exec("
            CREATE TABLE `previous_stock` (
              `product_id` VARCHAR(255) NOT NULL PRIMARY KEY,
              `units` DECIMAL(10,3) NOT NULL,
              `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");
        echo "<p>Tabulka previous_stock byla vytvořena.</p>";
        
        // Inicializace tabulky previous_stock
        $pdo->exec("
            INSERT INTO previous_stock (product_id, units)
            SELECT product, units FROM stockcurrent
        ");
        echo "<p>Tabulka previous_stock byla inicializována.</p>";
    }
    
    echo "<h1>Synchronizace zadaného množství v celkové inventuře</h1>";
    
    // Získání změn v tabulce stockcurrent
    $stmt = $pdo->query("
        SELECT s.product, s.units, ps.units as previous_units
        FROM stockcurrent s
        JOIN previous_stock ps ON s.product = ps.product_id
        WHERE s.units != ps.units
    ");
    $changes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($changes)) {
        echo "<p>Nebyly nalezeny žádné změny v tabulce stockcurrent.</p>";
    } else {
        echo "<p>Počet nalezených změn: " . count($changes) . "</p>";
        
        foreach ($changes as $change) {
            $productId = $change['product'];
            $currentUnits = $change['units'];
            $previousUnits = $change['previous_units'];
            $difference = $previousUnits - $currentUnits;
            
            echo "<p>Produkt: " . htmlspecialchars($productId) . ", Předchozí stav: " . htmlspecialchars($previousUnits) . ", Aktuální stav: " . htmlspecialchars($currentUnits) . ", Rozdíl: " . htmlspecialchars($difference) . "</p>";
            
            // Získání aktivních inventory_sessions
            $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active'");
            $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($sessions as $session) {
                $sessionId = $session['id'];
                
                // Získání aktuální hodnoty zadaného množství
                $stmt = $pdo->prepare("
                    SELECT id, total_zadane_mnozstvi 
                    FROM inventory_totals 
                    WHERE product_id = ? AND session_id = ?
                ");
                $stmt->execute([$productId, $sessionId]);
                $total = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$total) {
                    // Záznam neexistuje, vytvoříme nový
                    $stmt = $pdo->prepare("
                        INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$sessionId, $productId, 100 - $difference]);
                    
                    echo "<p>Byl vytvořen nový záznam pro produkt " . htmlspecialchars($productId) . " a session " . htmlspecialchars($sessionId) . " s hodnotou zadaného množství " . htmlspecialchars(100 - $difference) . ".</p>";
                } else {
                    // Aktualizace existujícího záznamu
                    $currentTotal = $total['total_zadane_mnozstvi'];
                    $newTotal = $currentTotal - $difference;
                    
                    $stmt = $pdo->prepare("
                        UPDATE inventory_totals
                        SET total_zadane_mnozstvi = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$newTotal, $total['id']]);
                    
                    echo "<p>Hodnota zadaného množství pro produkt " . htmlspecialchars($productId) . " a session " . htmlspecialchars($sessionId) . " byla změněna z " . htmlspecialchars($currentTotal) . " na " . htmlspecialchars($newTotal) . ".</p>";
                }
            }
            
            // Aktualizace tabulky previous_stock
            $stmt = $pdo->prepare("
                UPDATE previous_stock
                SET units = ?
                WHERE product_id = ?
            ");
            $stmt->execute([$currentUnits, $productId]);
        }
    }
    
    // Simulace změny v tabulce stockcurrent
    if (isset($_POST['simulate_stockcurrent_change'])) {
        $productId = $_POST['product_id'];
        $difference = $_POST['difference'];
        
        // Získání aktuální hodnoty units
        $stmt = $pdo->prepare("SELECT units FROM stockcurrent WHERE product = ?");
        $stmt->execute([$productId]);
        $currentUnits = $stmt->fetchColumn();
        
        if ($currentUnits === false) {
            echo "<p>Produkt s ID " . htmlspecialchars($productId) . " nebyl nalezen v tabulce stockcurrent.</p>";
        } else {
            // Aktualizace units
            $newUnits = $currentUnits - $difference;
            
            $stmt = $pdo->prepare("
                UPDATE stockcurrent
                SET units = ?
                WHERE product = ?
            ");
            $stmt->execute([$newUnits, $productId]);
            
            echo "<p>Hodnota units v tabulce stockcurrent byla změněna z " . htmlspecialchars($currentUnits) . " na " . htmlspecialchars($newUnits) . ".</p>";
            
            // Přesměrování na stejnou stránku pro zpracování změny
            header("Location: " . $_SERVER['PHP_SELF']);
            exit;
        }
    }
    
    // Formulář pro simulaci změny v tabulce stockcurrent
    echo "<h2>Simulace změny v tabulce stockcurrent</h2>";
    echo "<form method='post'>";
    echo "<p>Product ID: <input type='text' name='product_id' required></p>";
    echo "<p>Difference (kladné číslo = snížení units, záporné číslo = zvýšení units): <input type='text' name='difference' value='1' required></p>";
    echo "<p><input type='submit' name='simulate_stockcurrent_change' value='Simulovat změnu'></p>";
    echo "</form>";
    
    // Získání obsahu tabulky previous_stock
    $stmt = $pdo->query("SELECT * FROM previous_stock ORDER BY product_id ASC LIMIT 100");
    $previousStock = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Obsah tabulky previous_stock</h2>";
    
    if (empty($previousStock)) {
        echo "<p>Tabulka previous_stock je prázdná.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Product ID</th><th>Units</th><th>Updated At</th></tr>";
        
        foreach ($previousStock as $stock) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($stock['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($stock['units']) . "</td>";
            echo "<td>" . htmlspecialchars($stock['updated_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Získání obsahu tabulky inventory_totals
    $stmt = $pdo->query("
        SELECT it.*, is2.name as session_name, is2.status as session_status
        FROM inventory_totals it
        JOIN inventory_sessions is2 ON it.session_id = is2.id
        WHERE is2.status = 'active'
        ORDER BY it.session_id, it.product_id
        LIMIT 100
    ");
    $totals = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Záznamy v inventory_totals pro aktivní sessions</h2>";
    
    if (empty($totals)) {
        echo "<p>Nejsou žádné záznamy v inventory_totals pro aktivní sessions.</p>";
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Session ID</th><th>Session Name</th><th>Product ID</th><th>Total zadané množství</th><th>Session Status</th></tr>";
        
        foreach ($totals as $total) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($total['id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['session_id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['session_name']) . "</td>";
            echo "<td>" . htmlspecialchars($total['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($total['total_zadane_mnozstvi']) . "</td>";
            echo "<td>" . htmlspecialchars($total['session_status']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
