<?php
/**
 * Test triggeru pro aktualizaci inventory_totals
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování do souboru
function logToFile($message) {
    $logFile = __DIR__ . '/test_trigger_bez_change_type.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logToFile("Připojení k databázi úspěšné");
    
    // Získání produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logToFile("Nebyl nalezen žádný produkt s kladným množstvím na skladě");
        exit;
    }
    
    $productId = $product['id'];
    $productName = $product['name'];
    $currentUnits = $product['units'];
    
    logToFile("Vybrán produkt pro test: " . $productName . " (ID: " . $productId . ", Množství na skladě: " . $currentUnits . ")");
    
    // Kontrola, zda existuje aktivní inventurní relace
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active'");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sessions)) {
        logToFile("Neexistuje žádná aktivní inventurní relace");
        exit;
    }
    
    $sessionId = $sessions[0]['id'];
    logToFile("Nalezena aktivní inventurní relace s ID: " . $sessionId);
    
    // Kontrola, zda existuje záznam v inventory_totals pro tento produkt
    $stmt = $pdo->prepare("
        SELECT * FROM inventory_totals
        WHERE product_id = ?
        AND session_id = ?
    ");
    $stmt->execute([$productId, $sessionId]);
    $inventoryTotal = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$inventoryTotal) {
        logToFile("Pro vybraný produkt neexistuje záznam v inventory_totals. Vytvářím nový záznam...");
        
        // Vytvoření záznamu v inventory_totals
        $stmt = $pdo->prepare("
            INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$sessionId, $productId, $currentUnits]);
        
        // Kontrola, zda byl záznam vytvořen
        $stmt = $pdo->prepare("
            SELECT * FROM inventory_totals
            WHERE product_id = ?
            AND session_id = ?
        ");
        $stmt->execute([$productId, $sessionId]);
        $inventoryTotal = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$inventoryTotal) {
            logToFile("Nepodařilo se vytvořit záznam v inventory_totals");
            exit;
        }
        
        logToFile("Záznam v inventory_totals byl úspěšně vytvořen");
    }
    
    $totalZadaneMnozstvi = $inventoryTotal['total_zadane_mnozstvi'];
    logToFile("Aktuální zadané množství v celkové inventuře: " . $totalZadaneMnozstvi);
    
    // Simulace prodeje produktu
    $unitsToSell = 1;
    if ($currentUnits < $unitsToSell) {
        $unitsToSell = $currentUnits;
    }
    
    logToFile("Simuluji prodej " . $unitsToSell . " kusů produktu " . $productName);
    
    // Aktualizace stockcurrent - snížení stavu zásob
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = units - ?
        WHERE product = ?
    ");
    $stmt->execute([$unitsToSell, $productId]);
    
    // Kontrola, zda byla aktualizace úspěšná
    $stmt = $pdo->prepare("
        SELECT units FROM stockcurrent
        WHERE product = ?
    ");
    $stmt->execute([$productId]);
    $newUnits = $stmt->fetchColumn();
    
    logToFile("Nové množství na skladě: " . $newUnits . " (bylo: " . $currentUnits . ")");
    
    // Počkáme chvíli, aby se trigger stihl spustit
    sleep(1);
    
    // Kontrola, zda se aktualizovalo zadané množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi FROM inventory_totals
        WHERE product_id = ?
        AND session_id = ?
    ");
    $stmt->execute([$productId, $sessionId]);
    $newTotalZadaneMnozstvi = $stmt->fetchColumn();
    
    logToFile("Nové zadané množství v celkové inventuře: " . $newTotalZadaneMnozstvi . " (bylo: " . $totalZadaneMnozstvi . ")");
    
    // Kontrola, zda se zadané množství správně aktualizovalo
    $expectedTotalZadaneMnozstvi = $totalZadaneMnozstvi - $unitsToSell;
    
    if (abs($newTotalZadaneMnozstvi - $expectedTotalZadaneMnozstvi) < 0.001) {
        logToFile("TEST ÚSPĚŠNÝ: Zadané množství v celkové inventuře se správně aktualizovalo po prodeji produktu");
    } else {
        logToFile("TEST SELHAL: Zadané množství v celkové inventuře se neaktualizovalo správně po prodeji produktu");
        logToFile("Očekávaná hodnota: " . $expectedTotalZadaneMnozstvi . ", Skutečná hodnota: " . $newTotalZadaneMnozstvi);
    }
    
    // Kontrola, zda se spustil trigger a zalogoval změnu
    $stmt = $pdo->query("
        SELECT * FROM stockcurrent_log
        ORDER BY created DESC
        LIMIT 1
    ");
    $log = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($log) {
        logToFile("Trigger se spustil a zalogoval změnu:");
        logToFile("  Product ID: " . $log['product_id']);
        logToFile("  Old units: " . $log['old_units']);
        logToFile("  New units: " . $log['new_units']);
        logToFile("  Difference: " . $log['difference']);
    } else {
        logToFile("Trigger se nespustil nebo nezalogoval změnu");
    }
    
    // Vrácení stavu zásob na původní hodnotu
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = ?
        WHERE product = ?
    ");
    $stmt->execute([$currentUnits, $productId]);
    
    logToFile("Stav zásob byl vrácen na původní hodnotu: " . $currentUnits);
    
    // Vrácení zadaného množství v celkové inventuře na původní hodnotu
    $stmt = $pdo->prepare("
        UPDATE inventory_totals
        SET total_zadane_mnozstvi = ?
        WHERE product_id = ?
        AND session_id = ?
    ");
    $stmt->execute([$totalZadaneMnozstvi, $productId, $sessionId]);
    
    logToFile("Zadané množství v celkové inventuře bylo vráceno na původní hodnotu: " . $totalZadaneMnozstvi);
    
    logToFile("Test dokončen");
    
    // Výpis cesty k logovacímu souboru
    echo "Test byl dokončen. Logovací soubor: " . __DIR__ . '/test_trigger_bez_change_type.log';
    
} catch (PDOException $e) {
    logToFile("Chyba při připojení k databázi: " . $e->getMessage());
    echo "Došlo k chybě. Zkontrolujte logovací soubor: " . __DIR__ . '/test_trigger_bez_change_type.log';
}
?>
