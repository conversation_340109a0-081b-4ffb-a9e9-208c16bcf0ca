<?php
/**
 * API Index
 *
 * Tento soubor slouží jako vstupní bod pro všechny API požadavky.
 */

// Načtení utility pro práci s databází
require_once __DIR__ . '/../utils/database.php';

// Kontrola a vytvoření potřebných tabulek a výchozího administrátorského účtu
try {
    ensureTablesExist();
} catch (PDOException $e) {
    // Pokud se nepodaří vytvořit tabulky, zalogujeme chybu
    error_log("Failed to ensure tables exist: " . $e->getMessage());
}

// Nastavení hlaviček pro CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Zpracování OPTIONS požadavku pro CORS preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Zapnutí zobrazování chyb pro ladění
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Získání cesty požadavku
$requestUri = $_SERVER['REQUEST_URI'];

// Logování požadavku pro ladění
error_log("API Request: " . $requestUri);

// Detekce základní cesty
$scriptName = $_SERVER['SCRIPT_NAME'];
$scriptDir = dirname($scriptName);
$basePath = '/api';

// Pokud je aplikace v podadresáři, upravíme základní cestu
if ($scriptDir !== '/' && strpos($requestUri, $scriptDir) === 0) {
    $basePath = $scriptDir . $basePath;
}

// Logování základní cesty pro ladění
error_log("Base Path: " . $basePath);
error_log("Request URI: " . $requestUri);

// Odstranění query stringu z URI
$requestUri = strtok($requestUri, '?');

// Získání endpointu z GET parametru (pro přímé volání index.php)
$endpoint = $_GET['endpoint'] ?? '';

// Pokud je endpoint zadán jako GET parametr, použijeme ho
if (!empty($endpoint)) {
    error_log("Using endpoint from GET parameter: " . $endpoint);
    $segments = [$endpoint];
    $_SERVER['PATH_INFO'] = '/';
} else {
    // Kontrola, zda URI obsahuje základní cestu
    if (strpos($requestUri, $basePath) === false) {
        http_response_code(404);
        echo json_encode([
            'error' => 'Endpoint nenalezen',
            'request_uri' => $requestUri,
            'base_path' => $basePath,
            'script_name' => $scriptName,
            'script_dir' => $scriptDir
        ]);
        exit;
    }

    // Odstranění základní cesty z URI
    $path = substr($requestUri, strpos($requestUri, $basePath) + strlen($basePath));

    // Rozdělení cesty na segmenty
    $segments = explode('/', trim($path, '/'));

    // Získání názvu API endpointu
    $endpoint = $segments[0] ?? '';

    // Nastavení PATH_INFO pro API endpointy
    $_SERVER['PATH_INFO'] = '/' . implode('/', array_slice($segments, 1));
}

// Logování endpointu pro ladění
error_log("Endpoint: " . $endpoint);
error_log("PATH_INFO: " . $_SERVER['PATH_INFO']);

// Přidání endpointu pro testování
if ($endpoint === 'test') {
    echo json_encode([
        'success' => true,
        'message' => 'API je funkční',
        'request_uri' => $requestUri,
        'base_path' => $basePath,
        'endpoint' => $endpoint,
        'path_info' => $_SERVER['PATH_INFO'],
        'segments' => $segments,
        'method' => $_SERVER['REQUEST_METHOD'],
        'query' => $_GET,
        'server' => [
            'SCRIPT_NAME' => $_SERVER['SCRIPT_NAME'],
            'SCRIPT_FILENAME' => $_SERVER['SCRIPT_FILENAME'],
            'DOCUMENT_ROOT' => $_SERVER['DOCUMENT_ROOT'],
            'PHP_SELF' => $_SERVER['PHP_SELF'],
            'REQUEST_URI' => $_SERVER['REQUEST_URI'],
            'HTTP_HOST' => $_SERVER['HTTP_HOST']
        ]
    ]);
    exit;
}

// Směrování na správný API endpoint
try {
    switch ($endpoint) {
        case 'auth':
            require_once __DIR__ . '/auth.php';
            break;

        case 'products':
            require_once __DIR__ . '/products.php';
            break;

        case 'inventory':
            require_once __DIR__ . '/inventory.php';
            break;

        case 'users':
            if (file_exists(__DIR__ . '/users.php')) {
                require_once __DIR__ . '/users.php';
            } else {
                throw new Exception('Soubor users.php neexistuje');
            }
            break;

        case 'reports':
            require_once __DIR__ . '/reports.php';
            break;

        default:
            http_response_code(404);
            echo json_encode(['error' => 'Endpoint nenalezen', 'endpoint' => $endpoint]);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => 'Chyba při zpracování požadavku',
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}
