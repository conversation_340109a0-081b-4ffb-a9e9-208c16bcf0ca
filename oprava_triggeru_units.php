<?php
/**
 * Skript pro opravu triggerů s chybou "Unknown column 'units' in 'field list'"
 */

// Načtení konfigurace a připojení k <PERSON>b<PERSON>zi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování
function logMessage($message, $isError = false) {
    $color = $isError ? 'red' : 'green';
    echo "<p style='color: $color;'>" . ($isError ? '✗ ' : '✓ ') . htmlspecialchars($message) . "</p>";
}

// HTML hlavička
echo '<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oprava triggerů - chyba units</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            font-size: 16px;
            margin: 10px 5px;
            cursor: pointer;
            border-radius: 4px;
            border: none;
        }
        .button.red {
            background-color: #f44336;
        }
        .button:hover {
            opacity: 0.8;
        }
        .code {
            font-family: monospace;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>Oprava triggerů - chyba "Unknown column \'units\' in \'field list\'"</h1>
';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logMessage("Připojení k databázi úspěšné.");
    
    // Kontrola existujících triggerů
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
    ");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Hledání triggerů souvisejících s ticketlines
    $ticketlinesTriggers = [];
    foreach ($triggers as $trigger) {
        if ($trigger['EVENT_OBJECT_TABLE'] === 'ticketlines') {
            $ticketlinesTriggers[] = $trigger;
        }
    }
    
    echo "<h2>Existující triggery pro tabulku ticketlines</h2>";
    
    if (empty($ticketlinesTriggers)) {
        logMessage("Nebyly nalezeny žádné triggery pro tabulku 'ticketlines'.", true);
    } else {
        logMessage("Počet triggerů pro tabulku 'ticketlines': " . count($ticketlinesTriggers));
        
        echo "<table>";
        echo "<tr><th>Název</th><th>Událost</th><th>Timing</th><th>Kód</th></tr>";
        
        foreach ($ticketlinesTriggers as $trigger) {
            echo "<tr>";
            echo "<td>{$trigger['TRIGGER_NAME']}</td>";
            echo "<td>{$trigger['EVENT_MANIPULATION']}</td>";
            echo "<td>{$trigger['ACTION_TIMING']}</td>";
            echo "<td><div class='code'>" . htmlspecialchars($trigger['ACTION_STATEMENT']) . "</div></td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    // Kontrola tabulky ticketlines
    $stmt = $pdo->query("SHOW TABLES LIKE 'ticketlines'");
    $ticketlinesExists = $stmt->rowCount() > 0;
    
    if (!$ticketlinesExists) {
        logMessage("Tabulka 'ticketlines' neexistuje!", true);
    } else {
        logMessage("Tabulka 'ticketlines' existuje.");
        
        // Získání struktury tabulky
        $stmt = $pdo->query("DESCRIBE ticketlines");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>Struktura tabulky ticketlines</h3>";
        echo "<table>";
        echo "<tr><th>Sloupec</th><th>Typ</th><th>Null</th><th>Klíč</th><th>Výchozí hodnota</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>" . (isset($column['Default']) ? $column['Default'] : '') . "</td>";
            echo "<td>" . (isset($column['Extra']) ? $column['Extra'] : '') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Hledání sloupce pro množství
        $quantityColumn = null;
        
        foreach ($columns as $column) {
            $columnName = strtolower($column['Field']);
            $columnType = strtolower($column['Type']);
            
            if ($columnName === 'quantity' || 
                $columnName === 'qty' || 
                $columnName === 'amount' || 
                strpos($columnName, 'unit') !== false || 
                strpos($columnName, 'quant') !== false) {
                $quantityColumn = $column['Field'];
                break;
            } else if (($columnType === 'decimal(10,3)' || 
                       $columnType === 'double' || 
                       $columnType === 'float' || 
                       strpos($columnType, 'decimal') !== false) && 
                       !$quantityColumn) {
                $quantityColumn = $column['Field'];
            }
        }
        
        if ($quantityColumn) {
            logMessage("Nalezen sloupec pro množství v tabulce ticketlines: $quantityColumn");
        } else {
            logMessage("Nebyl nalezen žádný vhodný sloupec pro množství v tabulce ticketlines!", true);
        }
    }
    
    // Formulář pro odstranění všech triggerů
    echo "<h2>Odstranění všech triggerů</h2>";
    
    if (isset($_POST['remove_all_triggers'])) {
        $removedTriggers = 0;
        
        foreach ($triggers as $trigger) {
            $triggerName = $trigger['TRIGGER_NAME'];
            try {
                $pdo->exec("DROP TRIGGER IF EXISTS `$triggerName`");
                logMessage("Trigger '$triggerName' byl odstraněn.");
                $removedTriggers++;
            } catch (PDOException $e) {
                logMessage("Chyba při odstraňování triggeru '$triggerName': " . $e->getMessage(), true);
            }
        }
        
        logMessage("Bylo odstraněno celkem $removedTriggers triggerů.");
        
        // Obnovení stránky pro aktualizaci seznamu triggerů
        echo "<script>window.location.reload();</script>";
    }
    
    echo "<form method='post'>";
    echo "<p>Kliknutím na tlačítko níže odstraníte všechny triggery v databázi. Toto může pomoci vyřešit problém s chybou 'Unknown column units in field list'.</p>";
    echo "<button type='submit' name='remove_all_triggers' class='button red'>Odstranit všechny triggery</button>";
    echo "</form>";
    
    // Formulář pro vytvoření nových triggerů bez použití sloupce 'units'
    echo "<h2>Vytvoření nových triggerů bez použití sloupce 'units'</h2>";
    
    if (isset($_POST['create_triggers'])) {
        if (!$quantityColumn) {
            logMessage("Nebyl nalezen žádný vhodný sloupec pro množství!", true);
        } else {
            // Vytvoření nových triggerů
            try {
                // Kontrola, zda existuje tabulka inventory_stock_changes
                $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_stock_changes'");
                $stockChangesExists = $stmt->rowCount() > 0;
                
                if (!$stockChangesExists) {
                    logMessage("Tabulka 'inventory_stock_changes' neexistuje, vytvářím...");
                    
                    $pdo->exec("
                        CREATE TABLE `inventory_stock_changes` (
                          `id` INT AUTO_INCREMENT PRIMARY KEY,
                          `product_id` VARCHAR(255) NOT NULL,
                          `amount` DECIMAL(10,3) NOT NULL,
                          `change_type` ENUM('sale', 'update', 'delete', 'restock') NOT NULL,
                          `change_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                          `ticket_id` VARCHAR(255) NULL,
                          `details` TEXT NULL
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    ");
                    
                    logMessage("Tabulka 'inventory_stock_changes' byla vytvořena.");
                }
                
                // Trigger pro INSERT
                $pdo->exec("
                    CREATE TRIGGER `update_inventory_totals_after_ticketlines_insert`
                    AFTER INSERT ON `ticketlines`
                    FOR EACH ROW
                    BEGIN
                        DECLARE debug_info TEXT;
                        SET debug_info = CONCAT('Produkt: ', NEW.product, ', Množství: ', NEW.$quantityColumn);
                        
                        -- Logování operace pro diagnostiku
                        INSERT INTO inventory_stock_changes (product_id, amount, change_type, change_date, ticket_id, details)
                        VALUES (NEW.product, NEW.$quantityColumn, 'sale', NOW(), NEW.ticket, debug_info);
                        
                        -- Aktualizace zadaného množství v celkové inventuře
                        UPDATE inventory_totals
                        SET total_zadane_mnozstvi = total_zadane_mnozstvi - NEW.$quantityColumn,
                            last_updated = NOW()
                        WHERE product_id = NEW.product
                        AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                    END
                ");
                logMessage("Trigger 'update_inventory_totals_after_ticketlines_insert' byl vytvořen.");
                
                // Trigger pro UPDATE
                $pdo->exec("
                    CREATE TRIGGER `update_inventory_totals_after_ticketlines_update`
                    AFTER UPDATE ON `ticketlines`
                    FOR EACH ROW
                    BEGIN
                        DECLARE debug_info TEXT;
                        SET debug_info = CONCAT('Produkt: ', NEW.product, ', Staré množství: ', OLD.$quantityColumn, ', Nové množství: ', NEW.$quantityColumn);
                        
                        -- Logování operace pro diagnostiku
                        INSERT INTO inventory_stock_changes (product_id, amount, change_type, change_date, ticket_id, details)
                        VALUES (NEW.product, NEW.$quantityColumn - OLD.$quantityColumn, 'update', NOW(), NEW.ticket, debug_info);
                        
                        -- Aktualizace zadaného množství v celkové inventuře pouze pokud se změnilo množství
                        IF NEW.$quantityColumn != OLD.$quantityColumn THEN
                            UPDATE inventory_totals
                            SET total_zadane_mnozstvi = total_zadane_mnozstvi - (NEW.$quantityColumn - OLD.$quantityColumn),
                                last_updated = NOW()
                            WHERE product_id = NEW.product
                            AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                        END IF;
                    END
                ");
                logMessage("Trigger 'update_inventory_totals_after_ticketlines_update' byl vytvořen.");
                
                // Trigger pro DELETE
                $pdo->exec("
                    CREATE TRIGGER `update_inventory_totals_after_ticketlines_delete`
                    AFTER DELETE ON `ticketlines`
                    FOR EACH ROW
                    BEGIN
                        DECLARE debug_info TEXT;
                        SET debug_info = CONCAT('Produkt: ', OLD.product, ', Množství: ', OLD.$quantityColumn);
                        
                        -- Logování operace pro diagnostiku
                        INSERT INTO inventory_stock_changes (product_id, amount, change_type, change_date, ticket_id, details)
                        VALUES (OLD.product, OLD.$quantityColumn, 'delete', NOW(), OLD.ticket, debug_info);
                        
                        -- Aktualizace zadaného množství v celkové inventuře
                        UPDATE inventory_totals
                        SET total_zadane_mnozstvi = total_zadane_mnozstvi + OLD.$quantityColumn,
                            last_updated = NOW()
                        WHERE product_id = OLD.product
                        AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                    END
                ");
                logMessage("Trigger 'update_inventory_totals_after_ticketlines_delete' byl vytvořen.");
                
                logMessage("Triggery pro aktualizaci inventáře byly úspěšně vytvořeny.");
                
                // Obnovení stránky pro aktualizaci seznamu triggerů
                echo "<script>window.location.reload();</script>";
            } catch (PDOException $e) {
                logMessage("Chyba při vytváření triggerů: " . $e->getMessage(), true);
            }
        }
    }
    
    echo "<form method='post'>";
    echo "<p>Kliknutím na tlačítko níže vytvoříte nové triggery bez použití sloupce 'units'. Místo toho bude použit sloupec '$quantityColumn' pro množství a sloupec 'amount' v tabulce inventory_stock_changes.</p>";
    echo "<button type='submit' name='create_triggers' class='button'>Vytvořit nové triggery bez použití sloupce 'units'</button>";
    echo "</form>";
    
    // Odkaz zpět
    echo "<p><a href='index.html' class='back-link'>Zpět na hlavní stránku</a></p>";
    
    // HTML patička
    echo "</body></html>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html' class='back-link'>Zpět na hlavní stránku</a></p>";
    echo "</body></html>";
}
?>
