<?php
/**
 * Oprava triggeru pro aktualizaci inventory_totals po prodeji
 * 
 * Tento skript opraví trigger, který aktualizuje zadané množství v celkové inventuře
 * po prodeji produktů. Zajistí, že se zadané množství v celkové inventuře bude
 * aktualizovat při každé změně stavu, bez ohledu na to, zda jde o snížení nebo zvýšení stavu.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    $results = [];
    
    // Kontrola, zda existuje trigger update_inventory_entries_after_stockcurrent_update
    $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` = 'update_inventory_entries_after_stockcurrent_update'");
    $triggerExists = $stmt->rowCount() > 0;
    
    if ($triggerExists) {
        // Odstranění existujícího triggeru
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_entries_after_stockcurrent_update`");
        $results['drop_trigger'] = 'Existující trigger byl odstraněn';
    } else {
        $results['drop_trigger'] = 'Trigger neexistuje, není potřeba ho odstraňovat';
    }
    
    // Vytvoření nového triggeru pro aktualizaci inventory_totals při UPDATE v stockcurrent
    try {
        $pdo->exec("
            CREATE TRIGGER `update_inventory_entries_after_stockcurrent_update`
            AFTER UPDATE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
        ");
        $results['create_trigger'] = 'Trigger byl úspěšně vytvořen';
    } catch (PDOException $e) {
        $results['create_trigger'] = 'Chyba při vytváření triggeru: ' . $e->getMessage();
    }
    
    // Kontrola, zda byl trigger úspěšně vytvořen
    $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` = 'update_inventory_entries_after_stockcurrent_update'");
    $newTriggerExists = $stmt->rowCount() > 0;
    
    if ($newTriggerExists) {
        $trigger = $stmt->fetch(PDO::FETCH_ASSOC);
        $results['check_trigger'] = 'Trigger byl úspěšně vytvořen a existuje v databázi';
        $results['trigger_details'] = $trigger;
    } else {
        $results['check_trigger'] = 'Trigger nebyl vytvořen nebo neexistuje v databázi';
    }
    
    // Výpis výsledků
    echo "<h1>Oprava triggeru pro aktualizaci inventory_totals po prodeji</h1>";
    echo "<p>Tento skript opravil trigger, který aktualizuje zadané množství v celkové inventuře po prodeji produktů.</p>";
    echo "<p>Nyní se zadané množství v celkové inventuře bude aktualizovat při každé změně stavu, bez ohledu na to, zda jde o snížení nebo zvýšení stavu.</p>";
    
    echo "<h2>Výsledky operací</h2>";
    echo "<pre>";
    print_r($results);
    echo "</pre>";
    
    if ($newTriggerExists) {
        echo "<h2>Trigger byl úspěšně vytvořen</h2>";
        echo "<p>Trigger <strong>update_inventory_entries_after_stockcurrent_update</strong> byl úspěšně vytvořen a je aktivní v databázi.</p>";
        echo "<p>Nyní by se mělo zadané množství v celkové inventuře správně aktualizovat po prodeji produktů.</p>";
    } else {
        echo "<h2>Chyba při vytváření triggeru</h2>";
        echo "<p>Trigger <strong>update_inventory_entries_after_stockcurrent_update</strong> nebyl vytvořen nebo není aktivní v databázi.</p>";
        echo "<p>Zkontrolujte chybové hlášení a zkuste to znovu.</p>";
    }
    
    echo "<p><a href='check_all_triggers.php'>Zkontrolovat všechny triggery</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
