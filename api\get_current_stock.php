<?php
/**
 * API pro získání aktuálního stavu produktu
 * 
 * Tento skript poskytuje API pro získání aktuálního stavu produktu z tabulky stockcurrent.
 * 
 * Metoda: GET
 * Parametry:
 * - product_id: ID produktu
 * 
 * Odpověď:
 * - success: true/false
 * - message: Zpráva o výsledku
 * - current_stock: Aktuální stav produktu
 */

// Nastavení hlaviček pro CORS a JSON
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/../utils/database.php';

// Funkce pro odpověď
function sendResponse($success, $message, $data = []) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Kontrola metody
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendResponse(false, 'Neplatná metoda. Použijte GET.');
}

// Kontrola parametrů
if (empty($_GET['product_id'])) {
    sendResponse(false, 'Chybí parametr product_id.');
}

$productId = $_GET['product_id'];

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    // Získání aktuálního stavu produktu
    $stmt = $pdo->prepare("
        SELECT units 
        FROM stockcurrent 
        WHERE product = ?
    ");
    $stmt->execute([$productId]);
    $currentStock = $stmt->fetchColumn();
    
    // Pokud produkt nemá záznam v stockcurrent, vrátíme 0
    if ($currentStock === false) {
        $currentStock = 0;
    }
    
    sendResponse(true, 'Aktuální stav produktu byl úspěšně získán.', [
        'product_id' => $productId,
        'current_stock' => $currentStock
    ]);
} catch (PDOException $e) {
    sendResponse(false, 'Chyba při připojení k databázi: ' . $e->getMessage());
}
?>
