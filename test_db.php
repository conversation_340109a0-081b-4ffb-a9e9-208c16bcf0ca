<?php
/**
 * Test databázového připojení
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Test databázového připojení...\n";

try {
    // Načtení konfigurace
    $config = require __DIR__ . '/config/database.php';
    echo "Konfigurace načtena:\n";
    echo "Host: " . $config['host'] . "\n";
    echo "Database: " . $config['dbname'] . "\n";
    echo "Username: " . $config['username'] . "\n";
    echo "Password: " . (empty($config['password']) ? 'prázdné' : 'nastaveno') . "\n";
    
    // Pokus o připojení
    $dsn = "mysql:host={$config['host']};dbname={$config['dbname']};charset={$config['charset']}";
    echo "DSN: " . $dsn . "\n";
    
    $pdo = new PDO($dsn, $config['username'], $config['password'], $config['options']);
    echo "Připojení k databázi ÚSPĚŠNÉ!\n";
    
    // Test dotazu
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = '{$config['dbname']}'");
    $result = $stmt->fetch();
    echo "Počet tabulek v databázi: " . $result['count'] . "\n";
    
    // Kontrola existence tabulky inventory_users
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = '{$config['dbname']}' AND table_name = 'inventory_users'");
    $result = $stmt->fetch();
    echo "Tabulka inventory_users existuje: " . ($result['count'] > 0 ? 'ANO' : 'NE') . "\n";
    
    if ($result['count'] > 0) {
        // Pokud tabulka existuje, zkontrolujeme uživatele
        $stmt = $pdo->query("SELECT id, username, role, active FROM inventory_users");
        $users = $stmt->fetchAll();
        echo "Uživatelé v tabulce inventory_users:\n";
        foreach ($users as $user) {
            echo "- ID: {$user['id']}, Username: {$user['username']}, Role: {$user['role']}, Active: {$user['active']}\n";
        }
    }
    
} catch (PDOException $e) {
    echo "CHYBA při připojení k databázi: " . $e->getMessage() . "\n";
    echo "Kód chyby: " . $e->getCode() . "\n";
} catch (Exception $e) {
    echo "OBECNÁ CHYBA: " . $e->getMessage() . "\n";
}

echo "Test dokončen.\n";
?>
