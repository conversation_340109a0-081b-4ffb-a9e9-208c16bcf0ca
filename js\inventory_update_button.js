/**
 * Skript pro přidání tlačítka "Aktualizovat zadané množství podle aktuálního stavu" do str<PERSON>ky s celkovou inventurou
 */

document.addEventListener('DOMContentLoaded', function() {
    // Přidání posluchače události pro zobrazení stránky s celkovou inventurou
    const totalInventoryLink = document.querySelector('a[data-page="total-inventory"]');
    if (totalInventoryLink) {
        totalInventoryLink.addEventListener('click', function() {
            // Počkáme, až se stránka načte
            setTimeout(addUpdateButton, 500);
        });
    }

    // Kontrola, zda jsme na stránce s celkovou inventurou
    if (document.querySelector('#total-inventory-page:not(.d-none)')) {
        addUpdateButton();
    }
});

// Funkce pro přidání tlačítka
function addUpdateButton() {
    // Kontrola, zda jsme na stránce s celkovou inventurou
    const totalInventoryPage = document.querySelector('#total-inventory-page:not(.d-none)');
    if (!totalInventoryPage) {
        return;
    }

    // Kontrola, zda již existuje tlačítko pro aktualizaci
    if (document.querySelector('.update-inventory-button')) {
        return;
    }

    // Vytvoření tlačítka
    const updateButton = document.createElement('button');
    updateButton.className = 'update-inventory-button btn btn-warning';
    updateButton.innerHTML = '<i class="bi bi-arrow-repeat"></i> Aktualizovat zadané množství podle aktuálního stavu';

    // Přidání tlačítka do stránky
    const btnGroup = document.querySelector('.btn-group');
    if (btnGroup) {
        // Přidání tlačítka vedle existujících tlačítek
        btnGroup.parentNode.appendChild(updateButton);
    } else {
        // Pokud neexistuje .btn-group, přidáme tlačítko na začátek stránky
        const totalInventoryPage = document.querySelector('#total-inventory-page');
        if (totalInventoryPage) {
            // Vytvoření kontejneru pro tlačítko
            const actionsContainer = document.createElement('div');
            actionsContainer.className = 'inventory-actions mb-3';
            actionsContainer.appendChild(updateButton);

            // Přidání kontejneru na začátek stránky
            totalInventoryPage.insertBefore(actionsContainer, totalInventoryPage.firstChild);
        }
    }

    // Přidání posluchače události pro tlačítko
    updateButton.addEventListener('click', function(event) {
        event.preventDefault();

        // Zobrazení potvrzovacího dialogu
        if (!confirm('Opravdu chcete aktualizovat zadané množství pro všechny produkty podle aktuálního stavu?')) {
            return;
        }

        // Získání všech řádků tabulky
        const rows = inventoryTable.querySelectorAll('tbody tr');

        // Počítadla pro sledování průběhu
        let successCount = 0;
        let totalCount = rows.length;
        let currentIndex = 0;

        // Zobrazení indikátoru průběhu
        const progressContainer = document.createElement('div');
        progressContainer.className = 'progress-container';
        progressContainer.innerHTML = `
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <div class="progress-text">Aktualizace: 0 / ${totalCount}</div>
        `;
        inventoryTable.parentNode.insertBefore(progressContainer, inventoryTable);

        // Funkce pro aktualizaci indikátoru průběhu
        function updateProgress() {
            const percent = Math.round((currentIndex / totalCount) * 100);
            const progressBar = progressContainer.querySelector('.progress-bar');
            progressBar.style.width = percent + '%';
            progressBar.setAttribute('aria-valuenow', percent);
            progressBar.textContent = percent + '%';
            progressContainer.querySelector('.progress-text').textContent = `Aktualizace: ${currentIndex} / ${totalCount}`;
        }

        // Funkce pro aktualizaci jednoho produktu
        function updateNextProduct() {
            if (currentIndex >= totalCount) {
                // Všechny produkty byly aktualizovány
                progressContainer.querySelector('.progress-text').textContent = `Aktualizace dokončena. Úspěšně aktualizováno ${successCount} z ${totalCount} produktů.`;

                // Obnovení stránky po 3 sekundách
                setTimeout(function() {
                    location.reload();
                }, 3000);

                return;
            }

            const row = rows[currentIndex];
            const productId = row.getAttribute('data-product-id') || row.cells[0].textContent.trim();

            // Získání aktuálního stavu produktu
            fetch(`api/get_current_stock.php?product_id=${encodeURIComponent(productId)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const currentStock = data.data.current_stock;

                        // Aktualizace zadaného množství
                        return fetch('api/update_inventory_quantity.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                product_id: productId,
                                quantity: currentStock
                            })
                        });
                    } else {
                        throw new Error(data.message);
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        successCount++;
                    }

                    // Pokračování na další produkt
                    currentIndex++;
                    updateProgress();
                    updateNextProduct();
                })
                .catch(error => {
                    console.error('Chyba:', error);

                    // Pokračování na další produkt i v případě chyby
                    currentIndex++;
                    updateProgress();
                    updateNextProduct();
                });
        }

        // Spuštění aktualizace
        updateNextProduct();
    });
});
