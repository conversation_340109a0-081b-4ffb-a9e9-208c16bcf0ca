<?php
/**
 * Skript pro odstranění všech triggerů v databázi
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

try {
    // Připojení k databázi
    $pdo = getDbConnection();
    
    echo "<h1>Odstranění všech triggerů v databázi</h1>";
    
    // Získání seznamu všech triggerů
    $stmt = $pdo->query("SHOW TRIGGERS");
    $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($triggers)) {
        echo "<p>V databázi nejsou žádné triggery.</p>";
    } else {
        echo "<p>Nalezeno " . count($triggers) . " triggerů:</p>";
        
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>Trigger</th><th>Table</th><th>Event</th><th>Statement</th></tr>";
        
        foreach ($triggers as $trigger) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($trigger['Trigger']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['Table']) . "</td>";
            echo "<td>" . htmlspecialchars($trigger['Event']) . "</td>";
            echo "<td><pre style='max-width: 500px; overflow-x: auto;'>" . htmlspecialchars($trigger['Statement']) . "</pre></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        // Nabídka odstranění všech triggerů
        if (!isset($_POST['confirm_remove'])) {
            echo "<h2>Odstranit všechny triggery</h2>";
            echo "<p style='color: red;'><strong>VAROVÁNÍ:</strong> Tato akce odstraní všechny triggery v databázi. Tuto akci nelze vrátit!</p>";
            echo "<form method='post'>";
            echo "<p><input type='checkbox' name='create_backup' checked> Vytvořit zálohu triggerů před odstraněním</p>";
            echo "<p><input type='submit' name='confirm_remove' value='Odstranit všechny triggery'></p>";
            echo "</form>";
        }
    }
    
    // Zpracování formuláře pro odstranění všech triggerů
    if (isset($_POST['confirm_remove'])) {
        echo "<h2>Odstraňování triggerů</h2>";
        
        // Vytvoření zálohy triggerů
        if (isset($_POST['create_backup'])) {
            $backupFile = __DIR__ . '/backup_triggers_' . date('Y-m-d_H-i-s') . '.sql';
            $backupContent = "-- Záloha triggerů\n-- Vytvořeno: " . date('Y-m-d H:i:s') . "\n\n";
            
            foreach ($triggers as $trigger) {
                $backupContent .= "DELIMITER //\n";
                $backupContent .= "DROP TRIGGER IF EXISTS `" . $trigger['Trigger'] . "`//\n";
                $backupContent .= "CREATE TRIGGER `" . $trigger['Trigger'] . "`\n";
                $backupContent .= $trigger['Timing'] . " " . $trigger['Event'] . " ON `" . $trigger['Table'] . "`\n";
                $backupContent .= "FOR EACH ROW\n";
                $backupContent .= $trigger['Statement'] . "//\n";
                $backupContent .= "DELIMITER ;\n\n";
            }
            
            file_put_contents($backupFile, $backupContent);
            
            echo "<p>Záloha triggerů byla vytvořena: " . htmlspecialchars($backupFile) . "</p>";
        }
        
        // Odstranění všech triggerů
        $removedCount = 0;
        $errorCount = 0;
        
        foreach ($triggers as $trigger) {
            try {
                $pdo->exec("DROP TRIGGER IF EXISTS `" . $trigger['Trigger'] . "`");
                echo "<p style='color: green;'>✓ Trigger " . htmlspecialchars($trigger['Trigger']) . " byl odstraněn.</p>";
                $removedCount++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Chyba při odstraňování triggeru " . htmlspecialchars($trigger['Trigger']) . ": " . $e->getMessage() . "</p>";
                $errorCount++;
            }
        }
        
        echo "<h2>Výsledek</h2>";
        echo "<p>Odstraněno triggerů: " . $removedCount . "</p>";
        
        if ($errorCount > 0) {
            echo "<p style='color: red;'>Chyby při odstraňování: " . $errorCount . "</p>";
        } else {
            echo "<p style='color: green;'>Všechny triggery byly úspěšně odstraněny.</p>";
        }
        
        // Kontrola, zda byly všechny triggery odstraněny
        $stmt = $pdo->query("SHOW TRIGGERS");
        $remainingTriggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($remainingTriggers)) {
            echo "<p style='color: green;'>✓ V databázi nejsou žádné triggery.</p>";
        } else {
            echo "<p style='color: red;'>✗ V databázi stále existuje " . count($remainingTriggers) . " triggerů!</p>";
            
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>Trigger</th><th>Table</th><th>Event</th><th>Statement</th></tr>";
            
            foreach ($remainingTriggers as $trigger) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($trigger['Trigger']) . "</td>";
                echo "<td>" . htmlspecialchars($trigger['Table']) . "</td>";
                echo "<td>" . htmlspecialchars($trigger['Event']) . "</td>";
                echo "<td><pre style='max-width: 500px; overflow-x: auto;'>" . htmlspecialchars($trigger['Statement']) . "</pre></td>";
                echo "</tr>";
            }
            
            echo "</table>";
            
            // Nabídka odstranění zbývajících triggerů
            echo "<h3>Odstranit zbývající triggery</h3>";
            echo "<form method='post'>";
            echo "<input type='hidden' name='force_remove' value='1'>";
            echo "<p><input type='submit' name='confirm_remove' value='Odstranit zbývající triggery'></p>";
            echo "</form>";
        }
    }
    
    // Zpracování formuláře pro odstranění zbývajících triggerů
    if (isset($_POST['force_remove'])) {
        echo "<h2>Odstraňování zbývajících triggerů</h2>";
        
        // Získání seznamu všech triggerů
        $stmt = $pdo->query("SHOW TRIGGERS");
        $triggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Odstranění všech triggerů
        $removedCount = 0;
        $errorCount = 0;
        
        foreach ($triggers as $trigger) {
            try {
                // Použití FORCE pro odstranění triggeru
                $pdo->exec("DROP TRIGGER `" . $trigger['Trigger'] . "`");
                echo "<p style='color: green;'>✓ Trigger " . htmlspecialchars($trigger['Trigger']) . " byl odstraněn.</p>";
                $removedCount++;
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Chyba při odstraňování triggeru " . htmlspecialchars($trigger['Trigger']) . ": " . $e->getMessage() . "</p>";
                $errorCount++;
            }
        }
        
        echo "<h2>Výsledek</h2>";
        echo "<p>Odstraněno triggerů: " . $removedCount . "</p>";
        
        if ($errorCount > 0) {
            echo "<p style='color: red;'>Chyby při odstraňování: " . $errorCount . "</p>";
        } else {
            echo "<p style='color: green;'>Všechny triggery byly úspěšně odstraněny.</p>";
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
