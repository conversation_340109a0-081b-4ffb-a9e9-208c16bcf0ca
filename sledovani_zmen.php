<?php
/**
 * Sledování změn v tabulkách stockcurrent a inventory_totals
 * 
 * Tento skript sleduje změny v tabulkách stockcurrent a inventory_totals
 * a zaznamenává je do logovací tabulky pro pozdější analýzu.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Sledování změn v tabulkách stockcurrent a inventory_totals</h1>";
    
    // Kontrola, zda existuje tabulka pro sledování změn
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_changes_log'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Vytvoření tabulky pro sledování změn
        $pdo->exec("
            CREATE TABLE `inventory_changes_log` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `table_name` VARCHAR(50) NOT NULL,
                `product_id` VARCHAR(255) NOT NULL,
                `old_value` DECIMAL(10,3) NULL,
                `new_value` DECIMAL(10,3) NULL,
                `difference` DECIMAL(10,3) NULL,
                `change_type` VARCHAR(50) NOT NULL,
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");
        
        logMessage("Tabulka inventory_changes_log byla vytvořena.");
    }
    
    // Kontrola, zda existuje trigger pro sledování změn v tabulce stockcurrent
    $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` = 'log_stockcurrent_changes'");
    $triggerExists = $stmt->rowCount() > 0;
    
    if (!$triggerExists) {
        // Vytvoření triggeru pro sledování změn v tabulce stockcurrent
        $pdo->exec("
            CREATE TRIGGER `log_stockcurrent_changes`
            AFTER UPDATE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;
                
                -- Logování změny
                INSERT INTO inventory_changes_log (table_name, product_id, old_value, new_value, difference, change_type)
                VALUES ('stockcurrent', NEW.product, OLD.units, NEW.units, difference, 'UPDATE');
            END
        ");
        
        logMessage("Trigger log_stockcurrent_changes byl vytvořen.");
    }
    
    // Kontrola, zda existuje trigger pro sledování změn v tabulce inventory_totals
    $stmt = $pdo->query("SHOW TRIGGERS WHERE `Trigger` = 'log_inventory_totals_changes'");
    $triggerExists = $stmt->rowCount() > 0;
    
    if (!$triggerExists) {
        // Vytvoření triggeru pro sledování změn v tabulce inventory_totals
        $pdo->exec("
            CREATE TRIGGER `log_inventory_totals_changes`
            AFTER UPDATE ON `inventory_totals`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.total_zadane_mnozstvi - NEW.total_zadane_mnozstvi;
                
                -- Logování změny
                INSERT INTO inventory_changes_log (table_name, product_id, old_value, new_value, difference, change_type)
                VALUES ('inventory_totals', NEW.product_id, OLD.total_zadane_mnozstvi, NEW.total_zadane_mnozstvi, difference, 'UPDATE');
            END
        ");
        
        logMessage("Trigger log_inventory_totals_changes byl vytvořen.");
    }
    
    // Výpis posledních změn v tabulce inventory_changes_log
    $stmt = $pdo->query("
        SELECT * FROM inventory_changes_log
        ORDER BY created_at DESC
        LIMIT 50
    ");
    $changes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Poslední změny v tabulkách stockcurrent a inventory_totals</h2>";
    
    if (empty($changes)) {
        logMessage("Zatím nebyly zaznamenány žádné změny.");
    } else {
        echo "<table border='1' cellpadding='5' cellspacing='0'>";
        echo "<tr><th>ID</th><th>Tabulka</th><th>Produkt</th><th>Stará hodnota</th><th>Nová hodnota</th><th>Rozdíl</th><th>Typ změny</th><th>Čas změny</th></tr>";
        
        foreach ($changes as $change) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($change['id']) . "</td>";
            echo "<td>" . htmlspecialchars($change['table_name']) . "</td>";
            echo "<td>" . htmlspecialchars($change['product_id']) . "</td>";
            echo "<td>" . htmlspecialchars($change['old_value']) . "</td>";
            echo "<td>" . htmlspecialchars($change['new_value']) . "</td>";
            echo "<td>" . htmlspecialchars($change['difference']) . "</td>";
            echo "<td>" . htmlspecialchars($change['change_type']) . "</td>";
            echo "<td>" . htmlspecialchars($change['created_at']) . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<p><a href='sledovani_zmen.php'>Obnovit stránku</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
