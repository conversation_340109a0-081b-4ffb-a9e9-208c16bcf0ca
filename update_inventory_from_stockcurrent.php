<?php
/**
 * Aktualizace zadaného množství v celkové inventuře podle změn v tabulce stockcurrent
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování do souboru
function logToFile($message) {
    $logFile = __DIR__ . '/update_inventory_from_stockcurrent.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logToFile("Připojení k databázi úspěšné");
    logMessage("Připojení k databázi úspěšné");
    
    // Nalezení aktivní inventurní relace
    $stmt = $pdo->query("
        SELECT id
        FROM inventory_sessions
        WHERE status = 'active'
        LIMIT 1
    ");
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        logToFile("Nebyla nalezena žádná aktivní inventurní relace");
        logMessage("Nebyla nalezena žádná aktivní inventurní relace", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    $sessionId = $session['id'];
    logToFile("Nalezena aktivní inventurní relace s ID: " . $sessionId);
    logMessage("Nalezena aktivní inventurní relace s ID: " . $sessionId);
    
    // Zjištění aktuálního stavu produktů v tabulce stockcurrent
    $stmt = $pdo->query("
        SELECT sc.product, sc.units, p.name
        FROM stockcurrent sc
        JOIN products p ON sc.product = p.id
    ");
    $currentStock = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    logToFile("Načteno " . count($currentStock) . " produktů z tabulky stockcurrent");
    logMessage("Načteno " . count($currentStock) . " produktů z tabulky stockcurrent");
    
    // Zjištění předchozího stavu produktů z tabulky previous_stock
    $stmt = $pdo->query("
        SELECT product_id, units
        FROM previous_stock
    ");
    $previousStock = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    if (empty($previousStock)) {
        logToFile("Tabulka previous_stock je prázdná, inicializuji ji");
        logMessage("Tabulka previous_stock je prázdná, inicializuji ji");
        
        // Inicializace tabulky previous_stock
        foreach ($currentStock as $product) {
            $stmt = $pdo->prepare("
                INSERT INTO previous_stock (product_id, units, last_updated)
                VALUES (?, ?, NOW())
            ");
            $stmt->execute([$product['product'], $product['units']]);
        }
        
        logToFile("Tabulka previous_stock byla inicializována");
        logMessage("Tabulka previous_stock byla inicializována");
        
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    logToFile("Načteno " . count($previousStock) . " produktů z tabulky previous_stock");
    logMessage("Načteno " . count($previousStock) . " produktů z tabulky previous_stock");
    
    // Porovnání aktuálního a předchozího stavu a aktualizace zadaného množství v celkové inventuře
    $updatedProducts = 0;
    
    foreach ($currentStock as $product) {
        $productId = $product['product'];
        $currentUnits = $product['units'];
        $previousUnits = isset($previousStock[$productId]) ? $previousStock[$productId] : $currentUnits;
        
        // Výpočet rozdílu
        $difference = $previousUnits - $currentUnits;
        
        // Pokud se množství změnilo, aktualizujeme zadané množství v celkové inventuře
        if ($difference != 0) {
            logToFile("Produkt " . $product['name'] . " (ID: " . $productId . "): Předchozí množství: " . $previousUnits . ", Aktuální množství: " . $currentUnits . ", Rozdíl: " . $difference);
            logMessage("Produkt " . $product['name'] . " (ID: " . $productId . "): Předchozí množství: " . $previousUnits . ", Aktuální množství: " . $currentUnits . ", Rozdíl: " . $difference);
            
            // Zjištění aktuálního zadaného množství v celkové inventuře
            $stmt = $pdo->prepare("
                SELECT id, total_zadane_mnozstvi
                FROM inventory_totals
                WHERE product_id = ? AND session_id = ?
            ");
            $stmt->execute([$productId, $sessionId]);
            $total = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$total) {
                // Vytvoření nového záznamu v celkové inventuře
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_totals (product_id, session_id, total_zadane_mnozstvi, created_at, last_updated)
                    VALUES (?, ?, 0, NOW(), NOW())
                ");
                $stmt->execute([$productId, $sessionId]);
                
                // Zjištění ID nově vytvořeného záznamu
                $stmt = $pdo->prepare("
                    SELECT id, total_zadane_mnozstvi
                    FROM inventory_totals
                    WHERE product_id = ? AND session_id = ?
                ");
                $stmt->execute([$productId, $sessionId]);
                $total = $stmt->fetch(PDO::FETCH_ASSOC);
            }
            
            $totalId = $total['id'];
            $currentTotal = $total['total_zadane_mnozstvi'];
            $newTotal = $currentTotal - $difference;
            
            logToFile("Aktuální zadané množství v celkové inventuře: " . $currentTotal . ", Nové zadané množství: " . $newTotal);
            logMessage("Aktuální zadané množství v celkové inventuře: " . $currentTotal . ", Nové zadané množství: " . $newTotal);
            
            // Aktualizace zadaného množství v celkové inventuře
            $stmt = $pdo->prepare("
                UPDATE inventory_totals
                SET total_zadane_mnozstvi = ?,
                    last_updated = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$newTotal, $totalId]);
            
            // Aktualizace předchozího stavu v tabulce previous_stock
            $stmt = $pdo->prepare("
                UPDATE previous_stock
                SET units = ?,
                    last_updated = NOW()
                WHERE product_id = ?
            ");
            $stmt->execute([$currentUnits, $productId]);
            
            $updatedProducts++;
        }
    }
    
    logToFile("Aktualizováno " . $updatedProducts . " produktů");
    logMessage("Aktualizováno " . $updatedProducts . " produktů");
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logToFile("Chyba při připojení k databázi: " . $e->getMessage());
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
