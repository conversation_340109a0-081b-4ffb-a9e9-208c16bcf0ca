/**
 * Utility functions
 */

/**
 * Checks if a user has a specific role.
 *
 * @param {object} user The user object.
 * @param {string|number} role The role to check for.
 * @returns {boolean} True if the user has the role, false otherwise.
 */
export function hasRole(user, role) {
    if (!user || !user.role) {
        return false;
    }

    const userRole = user.role;

    return (
        userRole === role ||
        (typeof userRole === 'string' && userRole.toLowerCase() === role.toLowerCase()) ||
        userRole === Number(role) ||
        userRole === String(role)
    );
}

/**
 * Checks if a user is an admin.
 *
 * @param {object} user The user object.
 * @returns {boolean} True if the user is an admin, false otherwise.
 */
export function isAdmin(user) {
    return hasRole(user, 'admin');
}

/**
 * Checks if a user is a manager.
 *
 * @param {object} user The user object.
 * @returns {boolean} True if the user is a manager, false otherwise.
 */
export function isManager(user) {
    return hasRole(user, 'manager');
}

/**
 * Checks if a user is an admin or a manager.
 *
 * @param {object} user The user object.
 * @returns {boolean} True if the user is an admin or a manager, false otherwise.
 */
export function isAdminOrManager(user) {
    return isAdmin(user) || isManager(user);
}
