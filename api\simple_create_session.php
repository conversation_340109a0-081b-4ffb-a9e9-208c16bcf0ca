<?php
session_start();
header("Content-Type: application/json");

require_once __DIR__ . "/../utils/database.php";
require_once __DIR__ . "/../utils/auth.php";

try {
    if (!isLoggedIn()) {
        http_response_code(401);
        echo json_encode(["error" => "Not logged in"]);
        exit;
    }
    
    $user = getCurrentUser();
    $pdo = getDbConnection();
    
    // Kontrola existence tabulky
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_sessions'");
    if ($stmt->rowCount() == 0) {
        // Vytvoření tabulky
        $createTableSQL = "
            CREATE TABLE `inventory_sessions` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `user_id` int(11) NOT NULL,
                `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `end_time` timestamp NULL DEFAULT NULL,
                `status` enum('active','completed','cancelled') NOT NULL DEFAULT 'active',
                `session_name` varchar(255) DEFAULT NULL,
                PRIMARY KEY (`id`),
                KEY `idx_user_id` (`user_id`),
                KEY `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ";
        $pdo->exec($createTableSQL);
    }
    
    // Vytvoření inventury
    $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (:user_id)");
    $stmt->execute(["user_id" => $user["id"]]);
    
    $sessionId = $pdo->lastInsertId();
    
    echo json_encode([
        "success" => true,
        "session_id" => $sessionId,
        "message" => "Inventura byla úspěšně vytvořena"
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(["error" => $e->getMessage()]);
}
?>