<?php
echo "<h1>🔄 Rychlá aktualizace JavaScript</h1>";

// Aktualizace JavaScript
$jsFile = __DIR__ . '/js/utils.js';

if (file_exists($jsFile)) {
    $jsContent = file_get_contents($jsFile);
    
    // Nahrazení API endpointu
    $jsContent = str_replace(
        'fetch(`api/emergency_create.php`,',
        'fetch(`api/session_safe_create.php`,',
        $jsContent
    );
    
    $jsContent = str_replace(
        'fetch(`api/simple_create_session.php`,',
        'fetch(`api/session_safe_create.php`,',
        $jsContent
    );
    
    file_put_contents($jsFile, $jsContent);
    echo "<p style='color: green; font-size: 18px;'>✅ JavaScript úspěšně aktualizován!</p>";
    echo "<p>API endpoint změněn na: <code>api/session_safe_create.php</code></p>";
} else {
    echo "<p style='color: red;'>❌ Soubor js/utils.js nenalezen</p>";
}

echo "<h2>🔗 Co dělat dál:</h2>";
echo "<ol>";
echo "<li><a href='index.html' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Jít do hlavní aplikace</a></li>";
echo "<li>Kliknout na 'Nová inventura'</li>";
echo "<li>Mělo by to nyní fungovat!</li>";
echo "</ol>";
?>
