<?php
/**
 * Products API
 *
 * This file handles product-related API endpoints.
 */

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../utils/validation.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get request path
$path = $_SERVER['PATH_INFO'] ?? '/';

// Get action from query string (support both 'action' and 'endpoint' for backward compatibility)
$action = $_GET['action'] ?? $_GET['endpoint'] ?? '';

// Check if user is logged in
if (!isLoggedIn()) {
    sendResponse(['error' => 'Unauthorized'], 401);
    exit;
}

// Handle different endpoints
if ($action === 'search') {
    handleSearch();
} elseif ($action === 'details') {
    handleDetails();
} else {
    // Fallback to PATH_INFO if action is not specified
    switch ($path) {
        case '/search':
            handleSearch();
            break;

        case '/details':
            handleDetails();
            break;

        default:
            sendResponse(['error' => 'Endpoint not found'], 404);
            break;
    }
}

/**
 * Handle product search request
 */
function handleSearch() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Method not allowed'], 405);
        return;
    }

    // Get query parameter
    $ean = $_GET['ean'] ?? null;

    if (!$ean) {
        sendResponse(['error' => 'EAN code is required'], 400);
        return;
    }

    // Validate EAN
    $ean = validateEAN($ean);

    if (!$ean) {
        sendResponse(['error' => 'Invalid EAN code'], 400);
        return;
    }

    // Search for product
    $pdo = getDbConnection();

    // Nejprve zjistíme, které sloupce existují v tabulce products
    try {
        $columnsStmt = $pdo->prepare("
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'products'
        ");
        $columnsStmt->execute();
        $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);

        // Základní sloupce, které vždy potřebujeme
        $selectColumns = "
            p.id,
            p.code AS ean_code,
            p.name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock
        ";

        // Přidáme další sloupce, pokud existují
        if (in_array('priceretail', $columns)) {
            $selectColumns .= ", p.priceretail";
        }
        if (in_array('pricesellincludingtax', $columns)) {
            $selectColumns .= ", p.pricesellincludingtax";
        }

        $stmt = $pdo->prepare("
            SELECT
                $selectColumns
            FROM
                products p
            LEFT JOIN
                categories c ON p.category = c.id
            LEFT JOIN
                taxes t ON p.taxcat = t.id
            LEFT JOIN
                stockcurrent s ON p.id = s.product
            WHERE
                p.code = :ean_code
        ");
    } catch (Exception $e) {
        // Pokud dojde k chybě, použijeme základní dotaz
        $stmt = $pdo->prepare("
            SELECT
                p.id,
                p.code AS ean_code,
                p.name,
                c.name AS category,
                p.pricebuy,
                t.rate AS tax_rate,
                p.pricesell,
                COALESCE(s.units, 0) AS current_stock
            FROM
                products p
            LEFT JOIN
                categories c ON p.category = c.id
            LEFT JOIN
                taxes t ON p.taxcat = t.id
            LEFT JOIN
                stockcurrent s ON p.id = s.product
            WHERE
                p.code = :ean_code
        ");
    }

    $stmt->execute(['ean_code' => $ean]);

    $product = $stmt->fetch();

    if (!$product) {
        sendResponse(['error' => 'Product not found'], 404);
        return;
    }

    // Calculate price with tax
    $product['price_with_tax'] = $product['pricesell'] * (1 + ($product['tax_rate'] / 100));

    // Try to get rabat information if the column exists
    try {
        // First check if the rabat column exists
        $checkStmt = $pdo->prepare("
            SELECT
                COLUMN_NAME
            FROM
                INFORMATION_SCHEMA.COLUMNS
            WHERE
                TABLE_NAME = 'products'
                AND COLUMN_NAME = 'rabat'
        ");

        $checkStmt->execute();
        $columnExists = $checkStmt->fetch();

        if ($columnExists) {
            // If the column exists, get the rabat value
            $stmt = $pdo->prepare("
                SELECT
                    rabat
                FROM
                    products
                WHERE
                    id = :product_id
            ");

            $stmt->execute(['product_id' => $product['id']]);
            $rabatInfo = $stmt->fetch();

            // Add rabat information to product
            $product['rabat'] = $rabatInfo['rabat'] ?? 0;
        } else {
            // If the column doesn't exist, set rabat to 0
            $product['rabat'] = 0;
        }
    } catch (Exception $e) {
        // If there's any error, set rabat to 0
        $product['rabat'] = 0;
    }

    // Calculate price with tax and rabat
    $product['price_with_tax_and_rabat'] = $product['price_with_tax'] * (1 - ($product['rabat'] / 100));

    sendResponse(['product' => $product]);
}

/**
 * Handle product details request
 */
function handleDetails() {
    global $method;

    if ($method !== 'GET') {
        sendResponse(['error' => 'Method not allowed'], 405);
        return;
    }

    // Get product ID
    $productId = $_GET['id'] ?? null;

    if (!$productId) {
        sendResponse(['error' => 'Product ID is required'], 400);
        return;
    }

    // Validate product ID
    $productId = validateString($productId);

    if (!$productId) {
        sendResponse(['error' => 'Invalid product ID'], 400);
        return;
    }

    // Get product details
    $pdo = getDbConnection();

    // Nejprve zjistíme, které sloupce existují v tabulce products
    try {
        $columnsStmt = $pdo->prepare("
            SELECT COLUMN_NAME
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'products'
        ");
        $columnsStmt->execute();
        $columns = $columnsStmt->fetchAll(PDO::FETCH_COLUMN);

        // Základní sloupce, které vždy potřebujeme
        $selectColumns = "
            p.id,
            p.code AS ean_code,
            p.name,
            c.name AS category,
            p.pricebuy,
            t.rate AS tax_rate,
            p.pricesell,
            COALESCE(s.units, 0) AS current_stock
        ";

        // Přidáme další sloupce, pokud existují
        if (in_array('priceretail', $columns)) {
            $selectColumns .= ", p.priceretail";
        }
        if (in_array('pricesellincludingtax', $columns)) {
            $selectColumns .= ", p.pricesellincludingtax";
        }

        $stmt = $pdo->prepare("
            SELECT
                $selectColumns
            FROM
                products p
            LEFT JOIN
                categories c ON p.category = c.id
            LEFT JOIN
                taxes t ON p.taxcat = t.id
            LEFT JOIN
                stockcurrent s ON p.id = s.product
            WHERE
                p.id = :product_id
        ");
    } catch (Exception $e) {
        // Pokud dojde k chybě, použijeme základní dotaz
        $stmt = $pdo->prepare("
            SELECT
                p.id,
                p.code AS ean_code,
                p.name,
                c.name AS category,
                p.pricebuy,
                t.rate AS tax_rate,
                p.pricesell,
                COALESCE(s.units, 0) AS current_stock
            FROM
                products p
            LEFT JOIN
                categories c ON p.category = c.id
            LEFT JOIN
                taxes t ON p.taxcat = t.id
            LEFT JOIN
                stockcurrent s ON p.id = s.product
            WHERE
                p.id = :product_id
        ");
    }

    $stmt->execute(['product_id' => $productId]);

    $product = $stmt->fetch();

    if (!$product) {
        sendResponse(['error' => 'Product not found'], 404);
        return;
    }

    // Calculate price with tax
    $product['price_with_tax'] = $product['pricesell'] * (1 + ($product['tax_rate'] / 100));

    // Try to get rabat information if the column exists
    try {
        // First check if the rabat column exists
        $checkStmt = $pdo->prepare("
            SELECT
                COLUMN_NAME
            FROM
                INFORMATION_SCHEMA.COLUMNS
            WHERE
                TABLE_NAME = 'products'
                AND COLUMN_NAME = 'rabat'
        ");

        $checkStmt->execute();
        $columnExists = $checkStmt->fetch();

        if ($columnExists) {
            // If the column exists, get the rabat value
            $stmt = $pdo->prepare("
                SELECT
                    rabat
                FROM
                    products
                WHERE
                    id = :product_id
            ");

            $stmt->execute(['product_id' => $product['id']]);
            $rabatInfo = $stmt->fetch();

            // Add rabat information to product
            $product['rabat'] = $rabatInfo['rabat'] ?? 0;
        } else {
            // If the column doesn't exist, set rabat to 0
            $product['rabat'] = 0;
        }
    } catch (Exception $e) {
        // If there's any error, set rabat to 0
        $product['rabat'] = 0;
    }

    // Calculate price with tax and rabat
    $product['price_with_tax_and_rabat'] = $product['price_with_tax'] * (1 - ($product['rabat'] / 100));

    sendResponse(['product' => $product]);
}

/**
 * Validate and sanitize a string
 *
 * @param string $input The input string
 * @return string|false The sanitized string or false if invalid
 */
function validateString($input) {
    $input = trim($input);

    if (empty($input)) {
        return false;
    }

    return sanitizeString($input);
}

/**
 * Send JSON response
 *
 * @param mixed $data The response data
 * @param int $statusCode HTTP status code
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}
