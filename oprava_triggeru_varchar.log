[2025-05-22 21:32:57] Připojení k <PERSON>b<PERSON>zi úspěšn<PERSON>
[2025-05-22 21:32:58] Trigger update_inventory_entries_after_stockcurrent_update existuje
[2025-05-22 21:32:58] Kód triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku (s použitím sloupce change_type)
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference, change_type)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference, 1);

                -- <PERSON><PERSON><PERSON> je r<PERSON><PERSON><PERSON> (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jak<PERSON>koliv změně stavu
                    -- <PERSON><PERSON><PERSON> pro<PERSON> (difference > 0) odečítáme zadané množství
                    -- <PERSON><PERSON><PERSON> na<PERSON> (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 21:32:58] Existující trigger byl odstraněn
[2025-05-22 21:32:58] Struktura tabulky stockcurrent_log:
[2025-05-22 21:32:58]   id (int(11))
[2025-05-22 21:32:58]   product_id (varchar(50))
[2025-05-22 21:32:58]   old_units (double)
[2025-05-22 21:32:58]   new_units (double)
[2025-05-22 21:32:58]   difference (double)
[2025-05-22 21:32:58]   original_difference (double)
[2025-05-22 21:32:58]   created (timestamp)
[2025-05-22 21:32:58]   change_type (varchar(50))
[2025-05-22 21:32:58] Sloupec change_type existuje v tabulce stockcurrent_log a je typu varchar(50)
[2025-05-22 21:32:59] Nový trigger byl úspěšně vytvořen
[2025-05-22 21:33:00] Trigger update_inventory_entries_after_stockcurrent_update byl úspěšně vytvořen
[2025-05-22 21:33:00] Kód nového triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku (s použitím sloupce change_type jako varchar)
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference, change_type)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference, 'sale');

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 21:33:00] Oprava dokončena
