<?php
/**
 * Test stavu autentifikace pro diagnostiku API problémů
 */

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🔐 Test stavu autentifikace</h1>";

// Spuštění session
if (!isset($_SESSION)) {
    session_start();
}

echo "<h2>📋 Informace o session</h2>";
echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
echo "<tr><th>Parametr</th><th>Hodnota</th></tr>";
echo "<tr><td>Session ID</td><td>" . htmlspecialchars(session_id()) . "</td></tr>";
echo "<tr><td>Session status</td><td>" . htmlspecialchars(session_status() == PHP_SESSION_ACTIVE ? 'Aktivní' : 'Neaktivní') . "</td></tr>";
echo "<tr><td>Session data</td><td><pre>" . htmlspecialchars(print_r($_SESSION, true)) . "</pre></td></tr>";
echo "</table>";

echo "<h2>🍪 Cookies</h2>";
if (!empty($_COOKIE)) {
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
    echo "<tr><th>Název</th><th>Hodnota</th></tr>";
    foreach ($_COOKIE as $name => $value) {
        echo "<tr><td>" . htmlspecialchars($name) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
} else {
    logMessage("⚠️ Žádné cookies nejsou nastavené");
}

echo "<h2>👤 Stav přihlášení</h2>";

try {
    if (isLoggedIn()) {
        logMessage("✅ Uživatel je přihlášen");
        
        $user = getCurrentUser();
        if ($user) {
            echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
            echo "<tr><th>Parametr</th><th>Hodnota</th></tr>";
            echo "<tr><td>ID</td><td>" . htmlspecialchars($user['id']) . "</td></tr>";
            echo "<tr><td>Username</td><td>" . htmlspecialchars($user['username']) . "</td></tr>";
            echo "<tr><td>Role</td><td>" . htmlspecialchars($user['role']) . "</td></tr>";
            echo "<tr><td>Status</td><td>" . htmlspecialchars($user['status']) . "</td></tr>";
            echo "</table>";
            
            if (isAdminOrManager()) {
                logMessage("✅ Uživatel má oprávnění administrátora/manažera");
            } else {
                logMessage("ℹ️ Uživatel má základní oprávnění");
            }
        } else {
            logMessage("❌ Chyba při získávání informací o uživateli", true);
        }
    } else {
        logMessage("❌ Uživatel NENÍ přihlášen", true);
        
        echo "<h3>🔧 Pokus o automatické přihlášení</h3>";
        
        // Pokusíme se najít nějakého uživatele a přihlásit ho
        $pdo = getDbConnection();
        $stmt = $pdo->query("SELECT * FROM inventory_users WHERE status = 'active' ORDER BY role DESC LIMIT 1");
        $testUser = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testUser) {
            $_SESSION['user_id'] = $testUser['id'];
            $_SESSION['username'] = $testUser['username'];
            $_SESSION['role'] = $testUser['role'];
            
            logMessage("✅ Automaticky přihlášen uživatel: " . $testUser['username'] . " (role: " . $testUser['role'] . ")");
            
            echo "<p><a href='test_auth_status.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Obnovit stránku</a></p>";
        } else {
            logMessage("❌ Žádný aktivní uživatel v databázi!", true);
        }
    }
} catch (Exception $e) {
    logMessage("❌ Chyba při kontrole přihlášení: " . $e->getMessage(), true);
}

echo "<h2>🌐 Test API volání s autentifikací</h2>";

if (isLoggedIn()) {
    // Test API volání
    $pdo = getDbConnection();
    $stmt = $pdo->query("SELECT * FROM inventory_sessions WHERE status = 'active' LIMIT 1");
    $activeSession = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($activeSession) {
        $sessionId = $activeSession['id'];
        $apiUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=total-entries&session_id=$sessionId";
        
        echo "<p><strong>Test URL:</strong> <code>" . htmlspecialchars($apiUrl) . "</code></p>";
        
        // Vytvoření kontextu s cookies
        $cookieHeader = '';
        if (!empty($_COOKIE)) {
            $cookies = [];
            foreach ($_COOKIE as $name => $value) {
                $cookies[] = "$name=$value";
            }
            $cookieHeader = 'Cookie: ' . implode('; ', $cookies);
        }
        
        $context = stream_context_create([
            'http' => [
                'method' => 'GET',
                'header' => [
                    'Content-Type: application/json',
                    $cookieHeader
                ],
                'timeout' => 10
            ]
        ]);
        
        $response = @file_get_contents($apiUrl, false, $context);
        
        if ($response === false) {
            $error = error_get_last();
            logMessage("❌ API volání selhalo: " . ($error['message'] ?? 'Neznámá chyba'), true);
        } else {
            $data = json_decode($response, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                if (isset($data['error'])) {
                    logMessage("❌ API vrátilo chybu: " . $data['error'], true);
                } else {
                    logMessage("✅ API volání úspěšné");
                    
                    if (isset($data['entries'])) {
                        $count = count($data['entries']);
                        logMessage("📊 Vráceno $count záznamů");
                    }
                }
            } else {
                logMessage("❌ Chyba při parsování JSON odpovědi", true);
                echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>" . htmlspecialchars($response) . "</pre>";
            }
        }
    } else {
        logMessage("⚠️ Žádná aktivní inventurní relace pro test");
    }
} else {
    logMessage("⚠️ Nelze testovat API - uživatel není přihlášen");
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='test_api_endpoints.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test API endpointů</a>";
echo "<a href='test_fixed_total_inventory.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test opravené inventury</a>";
echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
echo "</p>";
?>
