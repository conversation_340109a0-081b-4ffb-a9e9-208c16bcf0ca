<?php
/**
 * Zobrazení logů ticketlines
 *
 * Tento skript zobrazuje záznamy z tabulky ticketlines_log, která obsahuje
 * informace o změnách v tabulce ticketlines.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    // Získání parametrů pro filtrování
    $productId = $_GET['product_id'] ?? '';
    $limit = isset($_GET['limit']) ? intval($_GET['limit']) : 100;
    
    // Omezení limitu na rozumnou hodnotu
    if ($limit <= 0 || $limit > 1000) {
        $limit = 100;
    }
    
    // Sestavení SQL dotazu
    $sql = "
        SELECT * FROM ticketlines_log
        WHERE 1=1
    ";
    
    $params = [];
    
    if (!empty($productId)) {
        $sql .= " AND product = :product_id";
        $params['product_id'] = $productId;
    }
    
    $sql .= " ORDER BY id DESC LIMIT :limit";
    $params['limit'] = $limit;
    
    // Provedení dotazu
    $stmt = $pdo->prepare($sql);
    
    // Bindování parametrů
    foreach ($params as $key => $value) {
        if ($key === 'limit') {
            $stmt->bindValue(':' . $key, $value, PDO::PARAM_INT);
        } else {
            $stmt->bindValue(':' . $key, $value);
        }
    }
    
    $stmt->execute();
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Získání seznamu produktů pro filtr
    $productStmt = $pdo->query("
        SELECT DISTINCT product
        FROM ticketlines_log
        ORDER BY product
    ");
    $products = $productStmt->fetchAll(PDO::FETCH_COLUMN);
    
    // Zobrazení HTML
    echo "<!DOCTYPE html>
<html lang='cs'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Logy ticketlines</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #333; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f2f2f2; }
        .filter-form { margin: 20px 0; padding: 15px; background-color: #f8f8f8; border-radius: 5px; }
        .filter-form select, .filter-form input { padding: 5px; margin-right: 10px; }
        .filter-form button { padding: 5px 10px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }
        .filter-form button:hover { background-color: #45a049; }
        .actions { margin: 20px 0; }
        .actions a { display: inline-block; margin-right: 10px; padding: 8px 16px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 4px; }
        .actions a:hover { background-color: #45a049; }
        .pagination { margin-top: 20px; }
        .pagination a { display: inline-block; padding: 5px 10px; margin-right: 5px; background-color: #f2f2f2; text-decoration: none; color: #333; }
        .pagination a:hover { background-color: #ddd; }
        .pagination .active { background-color: #4CAF50; color: white; }
    </style>
</head>
<body>
    <h1>Logy ticketlines</h1>
    
    <div class='actions'>
        <a href='index.html'>Zpět na hlavní stránku</a>
        <a href='fix_units_triggers.php'>Opravit triggery</a>
        <a href='test_stockcurrent_update.php'>Testovat aktualizaci stockcurrent</a>
    </div>
    
    <div class='filter-form'>
        <form method='get'>
            <label for='product_id'>Produkt:</label>
            <select name='product_id' id='product_id'>
                <option value=''>-- Všechny produkty --</option>";
    
    foreach ($products as $product) {
        $selected = ($product === $productId) ? 'selected' : '';
        echo "<option value='" . htmlspecialchars($product) . "' $selected>" . htmlspecialchars($product) . "</option>";
    }
    
    echo "</select>
            
            <label for='limit'>Počet záznamů:</label>
            <input type='number' name='limit' id='limit' value='" . $limit . "' min='1' max='1000'>
            
            <button type='submit'>Filtrovat</button>
            <button type='button' onclick='window.location.href=\"view_ticketlines_logs.php\"'>Zrušit filtr</button>
        </form>
    </div>
    
    <p>Celkem nalezeno záznamů: " . count($logs) . "</p>
    
    <table>
        <thead>
            <tr>
                <th>ID</th>
                <th>Ticket ID</th>
                <th>Řádek</th>
                <th>Produkt</th>
                <th>Množství</th>
                <th>Čas</th>
            </tr>
        </thead>
        <tbody>";
    
    foreach ($logs as $log) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($log['id']) . "</td>";
        echo "<td>" . htmlspecialchars($log['ticket_id']) . "</td>";
        echo "<td>" . htmlspecialchars($log['line']) . "</td>";
        echo "<td>" . htmlspecialchars($log['product']) . "</td>";
        echo "<td>" . htmlspecialchars($log['units']) . "</td>";
        echo "<td>" . htmlspecialchars($log['timestamp']) . "</td>";
        echo "</tr>";
    }
    
    echo "</tbody>
    </table>
</body>
</html>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
