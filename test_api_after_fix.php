<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API po opravě</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
        .response { background: #f8f9fa; border: 1px solid #dee2e6; padding: 10px; margin: 10px 0; border-radius: 3px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🧪 Test API po opravě tabulek</h1>
    
    <div class="test-section info">
        <h2>📋 Informace</h2>
        <p>Tento test ověří, zda API funguje po opravě tabulky inventory_sessions.</p>
        <p><strong>URL:</strong> <code>api/inventory.php?action=sessions</code></p>
    </div>

    <div class="test-section">
        <h2>🔍 Test GET požadavku</h2>
        <button onclick="testGet()">Test GET /api/inventory.php?action=sessions</button>
        <div id="get-result" class="response"></div>
    </div>

    <div class="test-section">
        <h2>📝 Test POST požadavku</h2>
        <button onclick="testPost()">Test POST /api/inventory.php?action=sessions</button>
        <div id="post-result" class="response"></div>
    </div>

    <div class="test-section">
        <h2>🔗 Navigace</h2>
        <a href="fix_inventory_sessions_table.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Oprava tabulky</a>
        <a href="check_database_tables.php" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;">Kontrola tabulek</a>
        <a href="index.html" style="background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Hlavní stránka</a>
    </div>

    <script>
        function testGet() {
            const resultDiv = document.getElementById('get-result');
            resultDiv.textContent = 'Odesílám GET požadavek...';
            
            fetch('api/inventory.php?action=sessions', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                const status = response.status;
                const statusText = response.statusText;
                
                return response.text().then(text => {
                    let result = `Status: ${status} ${statusText}\n\n`;
                    
                    try {
                        const json = JSON.parse(text);
                        result += 'JSON Response:\n' + JSON.stringify(json, null, 2);
                    } catch (e) {
                        result += 'Raw Response:\n' + text;
                    }
                    
                    resultDiv.textContent = result;
                    
                    // Změna stylu podle výsledku
                    const section = resultDiv.closest('.test-section');
                    section.className = 'test-section ' + (status === 200 ? 'success' : 'error');
                });
            })
            .catch(error => {
                resultDiv.textContent = 'Chyba: ' + error.message;
                const section = resultDiv.closest('.test-section');
                section.className = 'test-section error';
            });
        }

        function testPost() {
            const resultDiv = document.getElementById('post-result');
            resultDiv.textContent = 'Odesílám POST požadavek...';
            
            fetch('api/inventory.php?action=sessions', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => {
                const status = response.status;
                const statusText = response.statusText;
                
                return response.text().then(text => {
                    let result = `Status: ${status} ${statusText}\n\n`;
                    
                    try {
                        const json = JSON.parse(text);
                        result += 'JSON Response:\n' + JSON.stringify(json, null, 2);
                    } catch (e) {
                        result += 'Raw Response:\n' + text;
                    }
                    
                    resultDiv.textContent = result;
                    
                    // Změna stylu podle výsledku
                    const section = resultDiv.closest('.test-section');
                    section.className = 'test-section ' + (status === 200 ? 'success' : 'error');
                });
            })
            .catch(error => {
                resultDiv.textContent = 'Chyba: ' + error.message;
                const section = resultDiv.closest('.test-section');
                section.className = 'test-section error';
            });
        }

        // Automatický test při načtení stránky
        window.onload = function() {
            setTimeout(testGet, 500);
        };
    </script>
</body>
</html>
