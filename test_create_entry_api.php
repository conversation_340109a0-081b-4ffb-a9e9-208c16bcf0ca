<?php
/**
 * Test API endpointu api/inventory.php?action=entries metodou POST
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test API endpointu api/inventory.php?action=entries metodou POST</h1>";
    
    // Kontrola, zda je uživatel přihlášen
    if (!isLoggedIn()) {
        echo "<p style='color: red;'>✗ Uživatel není přihlášen!</p>";
        echo "<p>Pro použití tohoto skriptu se musíte nejprve přihlásit.</p>";
        echo "<p><a href='index.html'>Přejít na přihlašovací stránku</a></p>";
        exit;
    }
    
    $user = getCurrentUser();
    echo "<p style='color: green;'>✓ Uživatel je přihlášen jako: " . htmlspecialchars($user['username']) . " (ID: " . htmlspecialchars($user['id']) . ")</p>";
    
    // Kontrola, zda existuje aktivní inventurní relace
    $stmt = $pdo->prepare("
        SELECT * 
        FROM inventory_sessions 
        WHERE status = 'active'
        ORDER BY start_time DESC
        LIMIT 1
    ");
    $stmt->execute();
    $activeSession = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$activeSession) {
        echo "<p style='color: red;'>✗ Neexistuje žádná aktivní inventurní relace!</p>";
        
        // Vytvoření nové inventurní relace
        if (isset($_POST['create_session'])) {
            $stmt = $pdo->prepare("
                INSERT INTO inventory_sessions (user_id)
                VALUES (:user_id)
            ");
            $stmt->execute(['user_id' => $user['id']]);
            
            $sessionId = $pdo->lastInsertId();
            
            echo "<p style='color: green;'>✓ Byla vytvořena nová inventurní relace s ID: " . htmlspecialchars($sessionId) . "</p>";
            
            // Obnovení stránky
            echo "<script>window.location.reload();</script>";
            exit;
        }
        
        echo "<form method='post'>";
        echo "<p><input type='submit' name='create_session' value='Vytvořit novou inventurní relaci'></p>";
        echo "</form>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ Existuje aktivní inventurní relace s ID: " . htmlspecialchars($activeSession['id']) . "</p>";
    
    // Získání seznamu produktů
    $stmt = $pdo->query("
        SELECT p.id, p.code, p.name, COALESCE(s.units, 0) AS current_stock
        FROM products p
        LEFT JOIN stockcurrent s ON p.id = s.product
        ORDER BY p.name
        LIMIT 100
    ");
    
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "<p style='color: red;'>✗ Nebyly nalezeny žádné produkty!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✓ Bylo nalezeno " . count($products) . " produktů.</p>";
    
    // Testování API endpointu
    if (isset($_POST['test_api'])) {
        $productId = $_POST['product_id'];
        $zadaneMnozstvi = $_POST['zadane_mnozstvi'];
        
        // Získání produktu
        $stmt = $pdo->prepare("
            SELECT p.id, p.code, p.name, COALESCE(s.units, 0) AS current_stock
            FROM products p
            LEFT JOIN stockcurrent s ON p.id = s.product
            WHERE p.id = :id
        ");
        
        $stmt->execute(['id' => $productId]);
        
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            echo "<p style='color: red;'>✗ Produkt s ID " . htmlspecialchars($productId) . " nebyl nalezen!</p>";
            exit;
        }
        
        echo "<p style='color: green;'>✓ Produkt s ID " . htmlspecialchars($productId) . " byl nalezen:</p>";
        echo "<ul>";
        echo "<li>EAN kód: " . htmlspecialchars($product['code']) . "</li>";
        echo "<li>Název: " . htmlspecialchars($product['name']) . "</li>";
        echo "<li>Aktuální stav: " . htmlspecialchars($product['current_stock']) . "</li>";
        echo "</ul>";
        
        // Vytvoření dat pro požadavek
        $data = [
            'session_id' => $activeSession['id'],
            'ean_code' => $product['code'],
            'zadane_mnozstvi' => $zadaneMnozstvi
        ];
        
        echo "<p>Data pro požadavek:</p>";
        echo "<pre>" . htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT)) . "</pre>";
        
        // Vytvoření cURL požadavku
        $ch = curl_init();
        
        // Nastavení URL a dalších parametrů
        curl_setopt($ch, CURLOPT_URL, "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=entries");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Cookie: ' . $_SERVER['HTTP_COOKIE'] // Předání cookies pro autentizaci
        ]);
        
        // Povolení sledování hlaviček
        curl_setopt($ch, CURLOPT_HEADER, 1);
        
        // Povolení sledování chyb
        curl_setopt($ch, CURLOPT_VERBOSE, 1);
        
        // Vytvoření dočasného souboru pro výstup verbose
        $verbose = fopen('php://temp', 'w+');
        curl_setopt($ch, CURLOPT_STDERR, $verbose);
        
        // Provedení požadavku
        $response = curl_exec($ch);
        
        // Získání informací o požadavku
        $info = curl_getinfo($ch);
        
        // Získání chyby, pokud nastala
        $error = curl_error($ch);
        
        // Ukončení cURL
        curl_close($ch);
        
        // Výpis informací o požadavku
        echo "<h2>Informace o požadavku</h2>";
        echo "<ul>";
        echo "<li>URL: " . htmlspecialchars($info['url']) . "</li>";
        echo "<li>HTTP kód: " . htmlspecialchars($info['http_code']) . "</li>";
        echo "<li>Doba trvání: " . htmlspecialchars($info['total_time']) . " sekund</li>";
        echo "</ul>";
        
        // Výpis chyby, pokud nastala
        if ($error) {
            echo "<h2>Chyba</h2>";
            echo "<p style='color: red;'>" . htmlspecialchars($error) . "</p>";
        }
        
        // Výpis verbose logu
        rewind($verbose);
        $verboseLog = stream_get_contents($verbose);
        
        echo "<h2>Verbose log</h2>";
        echo "<pre>" . htmlspecialchars($verboseLog) . "</pre>";
        
        // Rozdělení odpovědi na hlavičky a tělo
        list($headers, $body) = explode("\r\n\r\n", $response, 2);
        
        // Výpis hlaviček
        echo "<h2>Hlavičky odpovědi</h2>";
        echo "<pre>" . htmlspecialchars($headers) . "</pre>";
        
        // Výpis těla odpovědi
        echo "<h2>Tělo odpovědi</h2>";
        echo "<pre>" . htmlspecialchars($body) . "</pre>";
        
        // Pokus o dekódování JSON odpovědi
        $jsonResponse = json_decode($body, true);
        
        if ($jsonResponse !== null) {
            echo "<h2>Dekódovaná JSON odpověď</h2>";
            echo "<pre>" . htmlspecialchars(json_encode($jsonResponse, JSON_PRETTY_PRINT)) . "</pre>";
        }
    }
    
    // Formulář pro testování API endpointu
    echo "<h2>Testování API endpointu</h2>";
    echo "<form method='post'>";
    echo "<p>Produkt: <select name='product_id' required>";
    
    foreach ($products as $product) {
        echo "<option value='" . htmlspecialchars($product['id']) . "'>" . htmlspecialchars($product['name']) . " (" . htmlspecialchars($product['code']) . ") - Stav: " . htmlspecialchars($product['current_stock']) . "</option>";
    }
    
    echo "</select></p>";
    echo "<p>Zadané množství: <input type='number' name='zadane_mnozstvi' step='0.001' min='0' value='0' required></p>";
    echo "<p><input type='submit' name='test_api' value='Otestovat API'></p>";
    echo "</form>";
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
