<?php
/**
 * Simulace změny v tabulce stockcurrent
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Simulace změny v tabulce stockcurrent</h1>";
    
    // Výběr náhodného produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logMessage("Nebyl nalezen žádný produkt s kladným množstvím na skladě", true);
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }
    
    logMessage("Vybrán produkt pro test: " . $product['name'] . " (ID: " . $product['id'] . ", Množství na skladě: " . $product['units'] . ")");
    
    // Simulace prodeje produktu (snížení množství na skladě)
    $unitsToSell = 1;
    $newUnits = $product['units'] - $unitsToSell;
    
    logMessage("Simuluji prodej " . $unitsToSell . " kusů produktu " . $product['name']);
    
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = ?
        WHERE product = ?
    ");
    $stmt->execute([$newUnits, $product['id']]);
    
    logMessage("Množství na skladě bylo aktualizováno na " . $newUnits);
    
    echo "<p><a href='update_inventory_from_stockcurrent.php'>Aktualizovat zadané množství v celkové inventuře</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
