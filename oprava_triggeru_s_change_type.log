[2025-05-22 21:26:53] Připojení k <PERSON>b<PERSON>zi úspěšn<PERSON>
[2025-05-22 21:27:02] Trigger update_inventory_entries_after_stockcurrent_update existuje
[2025-05-22 21:27:02] Kód triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku (bez použití sloupce change_type)
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                -- <PERSON>kud je roz<PERSON><PERSON><PERSON> (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- <PERSON><PERSON><PERSON> pro<PERSON> (difference > 0) ode<PERSON><PERSON><PERSON><PERSON>me zadané množství
                    -- <PERSON><PERSON><PERSON> na<PERSON> (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 21:27:06] Existující trigger byl odstraněn
[2025-05-22 21:27:06] Struktura tabulky stockcurrent_log:
[2025-05-22 21:27:06]   id (int(11))
[2025-05-22 21:27:06]   product_id (varchar(50))
[2025-05-22 21:27:06]   old_units (double)
[2025-05-22 21:27:06]   new_units (double)
[2025-05-22 21:27:06]   difference (double)
[2025-05-22 21:27:06]   original_difference (double)
[2025-05-22 21:27:06]   created (timestamp)
[2025-05-22 21:27:06]   change_type (varchar(50))
[2025-05-22 21:27:06] Sloupec change_type již existuje v tabulce stockcurrent_log
[2025-05-22 21:27:07] Nový trigger byl úspěšně vytvořen
[2025-05-22 21:27:09] Trigger update_inventory_entries_after_stockcurrent_update byl úspěšně vytvořen
[2025-05-22 21:27:09] Kód nového triggeru: BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku (s použitím sloupce change_type)
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference, change_type)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference, 1);

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
[2025-05-22 21:27:09] Oprava dokončena
