<?php
/**
 * Debug inventury API
 */

// Spuštění session pouze pokud ještě není spuš<PERSON>
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Debug inventury API</h1>";

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

try {
    echo "<h2>1. Kontrola přihlášení</h2>";
    
    // Přihlášení jako admin
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p style='color: green;'>✓ Přihlášení úspěšné</p>";
        echo "<p>Uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
        echo "<p>Session ID: " . session_id() . "</p>";
        echo "<p>Session data: <pre>" . print_r($_SESSION, true) . "</pre></p>";
    } else {
        echo "<p style='color: red;'>✗ Přihlášení selhalo</p>";
        exit;
    }
    
    echo "<h2>2. Kontrola databáze</h2>";
    
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
    
    // Kontrola struktury inventory_sessions
    $stmt = $pdo->query("DESCRIBE inventory_sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Struktura inventory_sessions:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. Test přímého INSERT</h2>";
    
    try {
        // Zkusíme různé varianty INSERT
        echo "<h3>3a. INSERT pouze s user_id:</h3>";
        $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (?)");
        $result = $stmt->execute([$user['id']]);
        
        if ($result) {
            $sessionId = $pdo->lastInsertId();
            echo "<p style='color: green;'>✓ Úspěch, ID: $sessionId</p>";
        } else {
            echo "<p style='color: red;'>✗ Neúspěch</p>";
            echo "<p>Chyba: " . print_r($stmt->errorInfo(), true) . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
        
        // Zkusíme s explicitními hodnotami
        echo "<h3>3b. INSERT s explicitními hodnotami:</h3>";
        try {
            $stmt = $pdo->prepare("
                INSERT INTO inventory_sessions (user_id, start_time, status) 
                VALUES (?, NOW(), 'active')
            ");
            $result = $stmt->execute([$user['id']]);
            
            if ($result) {
                $sessionId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✓ Úspěch s explicitními hodnotami, ID: $sessionId</p>";
            } else {
                echo "<p style='color: red;'>✗ Neúspěch i s explicitními hodnotami</p>";
                echo "<p>Chyba: " . print_r($stmt->errorInfo(), true) . "</p>";
            }
        } catch (Exception $e2) {
            echo "<p style='color: red;'>✗ Chyba i s explicitními hodnotami: " . $e2->getMessage() . "</p>";
        }
    }
    
    echo "<h2>4. Test API volání</h2>";
    
    // Simulace POST požadavku na API
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET = [];
    
    // Zachytíme výstup API
    ob_start();
    
    try {
        // Simulace JSON vstupu
        $jsonInput = json_encode([]);
        file_put_contents('php://temp', $jsonInput);
        
        // Zavoláme API funkci přímo
        require_once __DIR__ . '/api/inventory.php';
        
    } catch (Exception $e) {
        echo "Chyba při volání API: " . $e->getMessage();
    }
    
    $apiOutput = ob_get_clean();
    
    echo "<h3>Výstup API:</h3>";
    echo "<pre>" . htmlspecialchars($apiOutput) . "</pre>";
    
    echo "<h2>5. Aktuální inventury</h2>";
    
    $stmt = $pdo->query("
        SELECT 
            s.id, 
            s.start_time, 
            s.status, 
            s.user_id,
            u.username 
        FROM inventory_sessions s 
        LEFT JOIN inventory_users u ON s.user_id = u.id 
        ORDER BY s.id DESC 
        LIMIT 10
    ");
    $sessions = $stmt->fetchAll();
    
    if ($sessions) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Start Time</th><th>Status</th><th>User ID</th><th>Username</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>" . $session['id'] . "</td>";
            echo "<td>" . $session['start_time'] . "</td>";
            echo "<td>" . $session['status'] . "</td>";
            echo "<td>" . $session['user_id'] . "</td>";
            echo "<td>" . ($session['username'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Žádné inventury</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
?>
