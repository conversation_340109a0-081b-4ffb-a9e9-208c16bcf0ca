<?php
/**
 * Hlavní testovací stránka pro inventurní systém
 */

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Spuštění session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    $dbConnected = true;
} catch (PDOException $e) {
    $dbConnected = false;
    $dbError = $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test inventurního systému</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <style>
        body {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-success {
            background-color: #28a745;
        }
        .status-error {
            background-color: #dc3545;
        }
        .status-warning {
            background-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Test inventurního systému</h1>
        
        <div class="test-section">
            <h2>Stav systému</h2>
            <div class="mb-3">
                <span class="status-indicator <?php echo $dbConnected ? 'status-success' : 'status-error'; ?>"></span>
                <strong>Připojení k databázi:</strong> 
                <?php if ($dbConnected): ?>
                    <span class="text-success">Připojeno</span>
                <?php else: ?>
                    <span class="text-danger">Chyba: <?php echo htmlspecialchars($dbError); ?></span>
                <?php endif; ?>
            </div>
            
            <div class="mb-3">
                <span class="status-indicator <?php echo isLoggedIn() ? 'status-success' : 'status-warning'; ?>"></span>
                <strong>Přihlášení:</strong> 
                <?php if (isLoggedIn()): ?>
                    <span class="text-success">Přihlášen jako <?php echo htmlspecialchars($_SESSION['user']['username']); ?> (role: <?php echo htmlspecialchars($_SESSION['user']['role']); ?>)</span>
                <?php else: ?>
                    <span class="text-warning">Nepřihlášen</span>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Testovací nástroje</h2>
            <div class="row">
                <div class="col-md-6">
                    <h3>Základní testy</h3>
                    <ul class="list-group mb-3">
                        <li class="list-group-item">
                            <a href="test_database.php" class="d-block">Test připojení k databázi</a>
                            <small class="text-muted">Kontrola připojení k databázi a struktury tabulek</small>
                        </li>
                        <li class="list-group-item">
                            <a href="test_auth.php" class="d-block">Test autentizace</a>
                            <small class="text-muted">Testování přihlašování a správy uživatelů</small>
                        </li>
                        <li class="list-group-item">
                            <a href="fix_updated_column.php" class="d-block">Oprava chyby 'Unknown column updated in order clause'</a>
                            <small class="text-muted">Oprava chyby při řazení podle sloupce 'updated'</small>
                        </li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h3>Testy funkcionality</h3>
                    <ul class="list-group mb-3">
                        <li class="list-group-item">
                            <a href="test_inventory.php" class="d-block">Test inventury</a>
                            <small class="text-muted">Testování vytváření a správy inventurních relací</small>
                        </li>
                        <li class="list-group-item">
                            <a href="test_total_inventory.php" class="d-block">Test celkové inventury</a>
                            <small class="text-muted">Testování celkové inventury a aktualizace množství</small>
                        </li>
                        <li class="list-group-item">
                            <a href="test_product_search.php" class="d-block">Test vyhledávání produktů</a>
                            <small class="text-muted">Testování vyhledávání produktů a přidávání do inventury</small>
                        </li>
                        <li class="list-group-item">
                            <a href="test_reports.php" class="d-block">Test reportů</a>
                            <small class="text-muted">Testování generování reportů a exportu dat</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Diagnostické nástroje</h2>
            <div class="row">
                <div class="col-md-6">
                    <ul class="list-group mb-3">
                        <li class="list-group-item">
                            <a href="check_ticketlines_structure.php" class="d-block">Kontrola struktury tabulky ticketlines</a>
                            <small class="text-muted">Kontrola struktury tabulky ticketlines a jejích sloupců</small>
                        </li>
                        <li class="list-group-item">
                            <a href="check_ticketlines_log.php" class="d-block">Kontrola logu ticketlines</a>
                            <small class="text-muted">Kontrola logu změn v tabulce ticketlines</small>
                        </li>
                        <li class="list-group-item">
                            <a href="check_updated_column.php" class="d-block">Kontrola sloupce 'updated'</a>
                            <small class="text-muted">Kontrola, které tabulky mají sloupec 'updated'</small>
                        </li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <ul class="list-group mb-3">
                        <li class="list-group-item">
                            <a href="check_inventory_entries_table.php" class="d-block">Kontrola tabulky inventory_entries</a>
                            <small class="text-muted">Kontrola struktury tabulky inventory_entries</small>
                        </li>
                        <li class="list-group-item">
                            <a href="debug_api_error.php" class="d-block">Debug API chyb</a>
                            <small class="text-muted">Podrobné debugování API endpointů</small>
                        </li>
                        <li class="list-group-item">
                            <a href="api/debug.php" class="d-block">API Debug</a>
                            <small class="text-muted">Zobrazení informací o API a souborech</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <a href="index.html" class="btn btn-primary">Zpět na hlavní stránku</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
