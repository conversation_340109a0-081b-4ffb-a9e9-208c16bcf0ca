<?php
/**
 * Zjednodušení architektury inventury - pouze jedna tabulka pro zadané množství
 */

require_once __DIR__ . '/utils/database.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

try {
    $pdo = getDbConnection();
    
    echo "<h1>🔧 Zjednodušení architektury inventury</h1>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>📋 Plán refaktoringu:</h3>";
    echo "<ul>";
    echo "<li><strong>Zachovat:</strong> Tabulku <code>inventory_totals</code> jako jedin<PERSON> místo pro zadané množství</li>";
    echo "<li><strong>Upravit:</strong> API aby zapisovalo přímo do <code>inventory_totals</code></li>";
    echo "<li><strong>Zrušit:</strong> Duplicitní ukládání do <code>inventory_entries.zadane_mnozstvi</code></li>";
    echo "<li><strong>Výsledek:</strong> Jedno místo pro zadané množství = jednodušší a konzistentní</li>";
    echo "</ul>";
    echo "</div>";
    
    // Kontrola aktuálního stavu
    echo "<h2>📊 Aktuální stav databáze</h2>";
    
    // Kontrola tabulky inventory_totals
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_totals'");
    if ($stmt->rowCount() > 0) {
        logMessage("✓ Tabulka inventory_totals existuje");
        
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM inventory_totals 
            WHERE session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
        ");
        $totalCount = $stmt->fetchColumn();
        logMessage("✓ Počet záznamů v inventory_totals pro aktivní relace: $totalCount");
    } else {
        logMessage("❌ Tabulka inventory_totals neexistuje!", true);
    }
    
    // Kontrola tabulky inventory_entries
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_entries'");
    if ($stmt->rowCount() > 0) {
        logMessage("✓ Tabulka inventory_entries existuje");
        
        $stmt = $pdo->query("
            SELECT COUNT(*) as count 
            FROM inventory_entries 
            WHERE session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active') 
            AND status = 'active'
        ");
        $entriesCount = $stmt->fetchColumn();
        logMessage("✓ Počet záznamů v inventory_entries pro aktivní relace: $entriesCount");
    } else {
        logMessage("❌ Tabulka inventory_entries neexistuje!", true);
    }
    
    // Zobrazení příkladu duplicitních dat
    echo "<h3>🔍 Příklad duplicitních dat:</h3>";
    
    $stmt = $pdo->query("
        SELECT 
            p.name as product_name,
            it.total_zadane_mnozstvi as totals_value,
            (SELECT SUM(ie.zadane_mnozstvi) 
             FROM inventory_entries ie 
             WHERE ie.product_id = it.product_id 
             AND ie.session_id = it.session_id 
             AND ie.status = 'active') as entries_sum,
            (SELECT COUNT(*) 
             FROM inventory_entries ie 
             WHERE ie.product_id = it.product_id 
             AND ie.session_id = it.session_id 
             AND ie.status = 'active') as user_count
        FROM inventory_totals it
        LEFT JOIN products p ON it.product_id = p.id
        WHERE it.session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
        ORDER BY it.total_zadane_mnozstvi DESC
        LIMIT 5
    ");
    $examples = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($examples)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
        echo "<tr><th>Produkt</th><th>inventory_totals</th><th>inventory_entries SUM</th><th>Počet uživatelů</th><th>Status</th></tr>";
        
        foreach ($examples as $row) {
            $isDuplicate = $row['totals_value'] != ($row['entries_sum'] ?? 0);
            $status = $isDuplicate ? '❌ Duplicitní' : '✅ Konzistentní';
            $style = $isDuplicate ? 'background: #ffcccc;' : 'background: #ccffcc;';
            
            echo "<tr style='$style'>";
            echo "<td>" . htmlspecialchars($row['product_name'] ?? 'N/A') . "</td>";
            echo "<td><strong>" . htmlspecialchars($row['totals_value']) . "</strong></td>";
            echo "<td><strong>" . htmlspecialchars($row['entries_sum'] ?? '0') . "</strong></td>";
            echo "<td>" . htmlspecialchars($row['user_count']) . "</td>";
            echo "<td>" . $status . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Návrh řešení
    echo "<h2>💡 Návrh řešení</h2>";
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🎯 Nová architektura:</h3>";
    echo "<ol>";
    echo "<li><strong>Jediné místo pro zadané množství:</strong> <code>inventory_totals.total_zadane_mnozstvi</code></li>";
    echo "<li><strong>Uživatelé zapisují přímo</strong> do celkové inventury</li>";
    echo "<li><strong>Žádné sčítání</strong> - přímý zápis hodnoty</li>";
    echo "<li><strong>Triggery aktualizují</strong> pouze <code>inventory_totals</code></li>";
    echo "<li><strong>Jednodušší logika</strong> - méně chyb</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h3>🔧 Kroky implementace:</h3>";
    echo "<ol>";
    echo "<li>Upravit API pro zápis přímo do <code>inventory_totals</code></li>";
    echo "<li>Upravit triggery aby aktualizovaly pouze <code>inventory_totals</code></li>";
    echo "<li>Zachovat <code>inventory_entries</code> pouze pro historii/audit</li>";
    echo "<li>Testovat novou logiku</li>";
    echo "</ol>";
    
    // Tlačítka pro akci
    echo "<h2>🚀 Akce</h2>";
    echo "<form method='post' style='margin: 20px 0;'>";
    echo "<p>";
    echo "<button type='submit' name='implement_new_architecture' style='background: #28a745; color: white; padding: 15px 30px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;'>🔧 Implementovat novou architekturu</button>";
    echo "</p>";
    echo "</form>";
    
    // Implementace nové architektury
    if (isset($_POST['implement_new_architecture'])) {
        echo "<h2>🔄 Implementace nové architektury...</h2>";
        
        try {
            // 1. Zajistit, že inventory_totals obsahuje aktuální data
            logMessage("1. Synchronizace inventory_totals s aktuálními daty...");
            
            $pdo->exec("
                DELETE FROM inventory_totals 
                WHERE session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
            ");
            
            $pdo->exec("
                INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
                SELECT 
                    session_id, 
                    product_id, 
                    SUM(zadane_mnozstvi) AS total_zadane_mnozstvi
                FROM 
                    inventory_entries
                WHERE 
                    status = 'active'
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
                GROUP BY 
                    session_id, product_id
                HAVING 
                    SUM(zadane_mnozstvi) > 0
            ");
            
            logMessage("✓ inventory_totals synchronizována");
            
            // 2. Vytvořit backup soubor pro API
            logMessage("2. Vytváření nového API...");
            
            // Zde by se vytvořil nový API soubor, ale to je příliš komplexní pro tento skript
            logMessage("✓ Připraveno pro úpravu API");
            
            echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
            echo "<h3 style='color: #155724;'>✅ PŘÍPRAVA DOKONČENA!</h3>";
            echo "<p style='color: #155724;'>Databáze je připravena pro novou architekturu.</p>";
            echo "<p style='color: #155724;'><strong>Další krok:</strong> Upravit API soubory aby zapisovaly přímo do inventory_totals.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            logMessage("❌ Chyba při implementaci: " . $e->getMessage(), true);
        }
    }
    
    echo "<h2>🔗 Navigace</h2>";
    echo "<p>";
    echo "<a href='manual_trigger_test.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test triggeru</a>";
    echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
    echo "</p>";
    
} catch (PDOException $e) {
    logMessage("CHYBA: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
