<?php
/**
 * Oprava pole 'title' v tabulce inventory_sessions
 */

// Spuštění session pouze pokud ještě není spušt<PERSON>na
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Oprava pole 'title' v inventory_sessions</h1>";

require_once __DIR__ . '/utils/database.php';

try {
    $pdo = getDbConnection();
    echo "<p style='color: green;'>✓ Připojení k databázi úspěšné</p>";
    
    echo "<h2>1. Aktuální struktura inventory_sessions</h2>";
    
    $stmt = $pdo->query("DESCRIBE inventory_sessions");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    $hasTitle = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'title') {
            $hasTitle = true;
        }
    }
    echo "</table>";
    
    if ($hasTitle) {
        echo "<p style='color: orange;'>⚠ Pole 'title' existuje v tabulce</p>";
        
        echo "<h2>2. Oprava pole 'title'</h2>";
        
        // Možnost 1: Přidat výchozí hodnotu
        echo "<h3>Možnost 1: Přidat výchozí hodnotu pro 'title'</h3>";
        try {
            $pdo->exec("ALTER TABLE inventory_sessions MODIFY COLUMN title VARCHAR(255) DEFAULT 'Inventura'");
            echo "<p style='color: green;'>✓ Přidána výchozí hodnota 'Inventura' pro pole 'title'</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Chyba při přidávání výchozí hodnoty: " . $e->getMessage() . "</p>";
            
            // Možnost 2: Odstranit pole 'title'
            echo "<h3>Možnost 2: Odstranit pole 'title'</h3>";
            try {
                $pdo->exec("ALTER TABLE inventory_sessions DROP COLUMN title");
                echo "<p style='color: green;'>✓ Pole 'title' odstraněno</p>";
            } catch (Exception $e2) {
                echo "<p style='color: red;'>✗ Chyba při odstraňování pole: " . $e2->getMessage() . "</p>";
            }
        }
        
    } else {
        echo "<p style='color: green;'>✓ Pole 'title' neexistuje - to je v pořádku</p>";
    }
    
    echo "<h2>3. Struktura po opravě</h2>";
    
    $stmt = $pdo->query("DESCRIBE inventory_sessions");
    $newColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($newColumns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>4. Test vytvoření inventury</h2>";
    
    require_once __DIR__ . '/utils/auth.php';
    
    $user = authenticateUser('admin', 'admin123');
    
    if ($user) {
        echo "<p>Přihlášený uživatel: " . $user['username'] . " (ID: " . $user['id'] . ")</p>";
        
        try {
            $stmt = $pdo->prepare("INSERT INTO inventory_sessions (user_id) VALUES (?)");
            $result = $stmt->execute([$user['id']]);
            
            if ($result) {
                $sessionId = $pdo->lastInsertId();
                echo "<p style='color: green;'>✓ Test inventura vytvořena s ID: $sessionId</p>";
            } else {
                echo "<p style='color: red;'>✗ Test inventura se nepodařila</p>";
                echo "<p>Error info: " . print_r($stmt->errorInfo(), true) . "</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Chyba při testu: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Nepodařilo se přihlásit</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Chyba: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
}

echo "<h2>Závěr</h2>";
echo "<p><a href='index.html'>Zkusit hlavní aplikaci</a></p>";
?>
