<?php
/**
 * Skript pro testování autentizace a správy uživatelů
 */

// Nastavení error reportingu
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Spuštění session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Test autentizace a správy uživatelů</h1>";
    echo "<p style='color: green;'>✓ Připojení k databázi bylo úspěšné.</p>";
    
    // Kontrola existence tabulky inventory_users
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        echo "<p style='color: red;'>✗ Tabulka inventory_users neexistuje!</p>";
        
        // Vytvoření tabulky inventory_users
        echo "<h2>Vytvoření tabulky inventory_users</h2>";
        
        try {
            $pdo->exec("
                CREATE TABLE `inventory_users` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY,
                    `username` VARCHAR(255) NOT NULL UNIQUE,
                    `password` VARCHAR(255) NOT NULL,
                    `role` ENUM('admin', 'manager', 'user') NOT NULL DEFAULT 'user',
                    `full_name` VARCHAR(255) NOT NULL,
                    `email` VARCHAR(255) NULL,
                    `active` BOOLEAN NOT NULL DEFAULT TRUE,
                    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX `idx_inventory_users_username` (`username`),
                    INDEX `idx_inventory_users_role` (`role`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            ");
            
            echo "<p style='color: green;'>✓ Tabulka inventory_users byla vytvořena.</p>";
            
            // Vytvoření výchozího admin uživatele
            $stmt = $pdo->prepare("
                INSERT INTO `inventory_users` (`username`, `password`, `role`, `full_name`)
                VALUES ('admin', :password, 'admin', 'Administrator')
            ");
            
            $password = 'admin123';
            $stmt->execute(['password' => $password]);
            
            echo "<p style='color: green;'>✓ Výchozí admin uživatel byl vytvořen.</p>";
            echo "<p>Uživatelské jméno: admin</p>";
            echo "<p>Heslo: admin123</p>";
            
            $tableExists = true;
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ Chyba při vytváření tabulky inventory_users: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: green;'>✓ Tabulka inventory_users existuje.</p>";
    }
    
    if ($tableExists) {
        // Získání seznamu uživatelů
        $stmt = $pdo->query("SELECT id, username, role, full_name, email, active FROM inventory_users");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h2>Seznam uživatelů</h2>";
        
        if (empty($users)) {
            echo "<p>Žádní uživatelé nebyli nalezeni.</p>";
            
            // Vytvoření výchozího admin uživatele
            echo "<h3>Vytvoření výchozího admin uživatele</h3>";
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO `inventory_users` (`username`, `password`, `role`, `full_name`)
                    VALUES ('admin', :password, 'admin', 'Administrator')
                ");
                
                $password = 'admin123';
                $stmt->execute(['password' => $password]);
                
                echo "<p style='color: green;'>✓ Výchozí admin uživatel byl vytvořen.</p>";
                echo "<p>Uživatelské jméno: admin</p>";
                echo "<p>Heslo: admin123</p>";
                
                // Aktualizace seznamu uživatelů
                $stmt = $pdo->query("SELECT id, username, role, full_name, email, active FROM inventory_users");
                $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ Chyba při vytváření výchozího admin uživatele: " . $e->getMessage() . "</p>";
            }
        }
        
        if (!empty($users)) {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Uživatelské jméno</th><th>Role</th><th>Celé jméno</th><th>Email</th><th>Aktivní</th></tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($user['id']) . "</td>";
                echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td>" . htmlspecialchars($user['role']) . "</td>";
                echo "<td>" . htmlspecialchars($user['full_name']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email'] ?? '') . "</td>";
                echo "<td>" . ($user['active'] ? "Ano" : "Ne") . "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        }
        
        // Test přihlášení
        echo "<h2>Test přihlášení</h2>";
        
        if (isset($_POST['login'])) {
            $username = $_POST['username'];
            $password = $_POST['password'];
            
            echo "<p>Pokus o přihlášení s uživatelským jménem: " . htmlspecialchars($username) . "</p>";
            
            $user = authenticateUser($username, $password);
            
            if ($user) {
                echo "<p style='color: green;'>✓ Přihlášení bylo úspěšné.</p>";
                echo "<p>Přihlášený uživatel: " . htmlspecialchars($user['username']) . " (role: " . htmlspecialchars($user['role']) . ")</p>";
                
                // Výpis obsahu session
                echo "<h3>Obsah session</h3>";
                echo "<pre>" . htmlspecialchars(print_r($_SESSION, true)) . "</pre>";
            } else {
                echo "<p style='color: red;'>✗ Přihlášení selhalo. Neplatné uživatelské jméno nebo heslo.</p>";
            }
        }
        
        // Formulář pro přihlášení
        echo "<h3>Přihlašovací formulář</h3>";
        echo "<form method='post'>";
        echo "<p><label>Uživatelské jméno: <input type='text' name='username' value='admin'></label></p>";
        echo "<p><label>Heslo: <input type='password' name='password' value='admin123'></label></p>";
        echo "<p><input type='submit' name='login' value='Přihlásit'></p>";
        echo "</form>";
        
        // Test kontroly přihlášení
        echo "<h2>Test kontroly přihlášení</h2>";
        
        $isLoggedIn = isLoggedIn();
        echo "<p>Uživatel je přihlášen: " . ($isLoggedIn ? "Ano" : "Ne") . "</p>";
        
        if ($isLoggedIn) {
            echo "<p>Přihlášený uživatel: " . htmlspecialchars($_SESSION['user']['username']) . " (role: " . htmlspecialchars($_SESSION['user']['role']) . ")</p>";
            
            // Test kontroly role
            echo "<h3>Test kontroly role</h3>";
            
            echo "<p>Uživatel je admin: " . (isAdmin() ? "Ano" : "Ne") . "</p>";
            echo "<p>Uživatel je manager: " . (isManager() ? "Ano" : "Ne") . "</p>";
            echo "<p>Uživatel je admin nebo manager: " . (isAdminOrManager() ? "Ano" : "Ne") . "</p>";
            
            // Formulář pro odhlášení
            echo "<h3>Odhlášení</h3>";
            echo "<form method='post'>";
            echo "<p><input type='submit' name='logout' value='Odhlásit'></p>";
            echo "</form>";
        }
        
        // Zpracování odhlášení
        if (isset($_POST['logout'])) {
            // Zničení session
            session_unset();
            session_destroy();
            
            echo "<p style='color: green;'>✓ Uživatel byl odhlášen.</p>";
            echo "<p><a href='" . $_SERVER['PHP_SELF'] . "'>Obnovit stránku</a></p>";
        }
    }
    
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
