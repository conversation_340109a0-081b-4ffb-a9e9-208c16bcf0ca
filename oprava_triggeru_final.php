<?php
/**
 * Oprava triggeru pro aktualizaci inventory_totals po prodeji
 * 
 * Tento skript opraví trigger, který aktualizuje zadané množství v celkové inventuře
 * po prodeji produktů. Zajistí, že se zadané množství v celkové inventuře bude
 * aktualizovat při každé změně stavu, bez ohledu na to, zda jde o snížení nebo zvýšení stavu.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro výpis zprávy
function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    
    echo "<h1>Oprava triggeru pro aktualizaci inventory_totals po prodeji</h1>";
    
    // Kontrola, zda existuje trigger update_inventory_entries_after_stockcurrent_update
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND TRIGGER_NAME = 'update_inventory_entries_after_stockcurrent_update'
    ");
    $trigger = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($trigger) {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update existuje");
        logMessage("Kód triggeru: " . $trigger['ACTION_STATEMENT']);
        
        // Odstranění existujícího triggeru
        $pdo->exec("DROP TRIGGER IF EXISTS `update_inventory_entries_after_stockcurrent_update`");
        logMessage("Existující trigger byl odstraněn");
    } else {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update neexistuje");
    }
    
    // Kontrola, zda existuje tabulka stockcurrent_log
    $stmt = $pdo->query("SHOW TABLES LIKE 'stockcurrent_log'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        // Vytvoření tabulky stockcurrent_log
        $pdo->exec("
            CREATE TABLE `stockcurrent_log` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `product_id` VARCHAR(255) NOT NULL,
                `old_units` DECIMAL(10,3) NULL,
                `new_units` DECIMAL(10,3) NULL,
                `difference` DECIMAL(10,3) NULL,
                `original_difference` DECIMAL(10,3) NULL,
                `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");
        
        logMessage("Tabulka stockcurrent_log byla vytvořena");
    }
    
    // Vytvoření nového triggeru pro aktualizaci inventory_totals při UPDATE v stockcurrent
    try {
        $pdo->exec("
            CREATE TRIGGER `update_inventory_entries_after_stockcurrent_update`
            AFTER UPDATE ON `stockcurrent`
            FOR EACH ROW
            BEGIN
                -- Výpočet rozdílu mezi starou a novou hodnotou
                DECLARE difference DECIMAL(10,3);
                SET difference = OLD.units - NEW.units;

                -- Logování změny pro diagnostiku
                INSERT INTO stockcurrent_log (product_id, old_units, new_units, difference, original_difference)
                VALUES (NEW.product, OLD.units, NEW.units, difference, difference);

                -- Pokud je rozdíl nenulový (změna stavu), aktualizujeme pouze celkové součty
                IF difference != 0 THEN
                    -- Aktualizace celkových součtů v inventory_totals při jakékoliv změně stavu
                    -- Při prodeji (difference > 0) odečítáme zadané množství
                    -- Při naskladnění (difference < 0) přičítáme zadané množství
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = total_zadane_mnozstvi - difference
                    WHERE product_id = NEW.product
                    AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                END IF;
            END
        ");
        
        logMessage("Nový trigger byl úspěšně vytvořen");
    } catch (PDOException $e) {
        logMessage("Chyba při vytváření triggeru: " . $e->getMessage(), true);
    }
    
    // Kontrola, zda byl trigger úspěšně vytvořen
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND TRIGGER_NAME = 'update_inventory_entries_after_stockcurrent_update'
    ");
    $newTrigger = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($newTrigger) {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update byl úspěšně vytvořen");
        logMessage("Kód nového triggeru: " . $newTrigger['ACTION_STATEMENT']);
    } else {
        logMessage("Trigger update_inventory_entries_after_stockcurrent_update nebyl vytvořen", true);
    }
    
    // Kontrola, zda existují jiné triggery pro tabulku stockcurrent
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND EVENT_OBJECT_TABLE = 'stockcurrent'
        AND TRIGGER_NAME != 'update_inventory_entries_after_stockcurrent_update'
    ");
    $otherTriggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($otherTriggers)) {
        logMessage("Existují i jiné triggery pro tabulku stockcurrent:");
        foreach ($otherTriggers as $otherTrigger) {
            logMessage("Trigger: " . $otherTrigger['TRIGGER_NAME'] . " (" . $otherTrigger['ACTION_TIMING'] . " " . $otherTrigger['EVENT_MANIPULATION'] . ")");
        }
    } else {
        logMessage("Neexistují žádné jiné triggery pro tabulku stockcurrent");
    }
    
    // Kontrola, zda existují triggery pro tabulku ticketlines
    $stmt = $pdo->query("
        SELECT TRIGGER_NAME, EVENT_MANIPULATION, EVENT_OBJECT_TABLE, ACTION_STATEMENT, ACTION_TIMING
        FROM information_schema.TRIGGERS
        WHERE TRIGGER_SCHEMA = DATABASE()
        AND EVENT_OBJECT_TABLE = 'ticketlines'
    ");
    $ticketlinesTriggers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($ticketlinesTriggers)) {
        logMessage("Existují triggery pro tabulku ticketlines:");
        foreach ($ticketlinesTriggers as $ticketlinesTrigger) {
            logMessage("Trigger: " . $ticketlinesTrigger['TRIGGER_NAME'] . " (" . $ticketlinesTrigger['ACTION_TIMING'] . " " . $ticketlinesTrigger['EVENT_MANIPULATION'] . ")");
        }
    } else {
        logMessage("Neexistují žádné triggery pro tabulku ticketlines");
    }
    
    echo "<h2>Oprava byla dokončena</h2>";
    echo "<p>Trigger pro aktualizaci zadaného množství v celkové inventuře po prodeji byl opraven.</p>";
    echo "<p>Nyní by se mělo zadané množství v celkové inventuře správně aktualizovat po prodeji produktů.</p>";
    
    echo "<p><a href='test_inventory_update.php'>Otestovat aktualizaci inventury</a></p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
    
} catch (PDOException $e) {
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
?>
