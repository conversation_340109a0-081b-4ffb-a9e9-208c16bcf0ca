<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API uživatelů</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test API uživatelů</h1>
    
    <div class="test-section">
        <h2>1. Test přímého API volání</h2>
        <button onclick="testDirectApi()">Test api/users.php</button>
        <div id="direct-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test přes buildApiUrl</h2>
        <button onclick="testBuildApiUrl()">Test buildApiUrl('users')</button>
        <div id="build-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test po přihlášení</h2>
        <button onclick="loginAndTest()">Přihlásit a testovat</button>
        <div id="login-test-result"></div>
    </div>

    <script>
        // Kopie buildApiUrl funkce z app.js
        const API_URL = 'api';
        
        function buildApiUrl(endpoint, params = {}) {
            console.log('buildApiUrl - endpoint:', endpoint);
            console.log('buildApiUrl - params:', params);

            // Upravíme endpoint pro API
            if (endpoint === 'simple_auth') {
                endpoint = 'simple_auth.php';
            } else {
                if (endpoint.includes('/')) {
                    const parts = endpoint.split('/');
                    const file = parts[0];
                    const action = parts[1];

                    if (!isNaN(action)) {
                        endpoint = file + '.php/' + action;
                    } else {
                        endpoint = endpoint + '.php';
                    }
                } else {
                    endpoint = endpoint + '.php';
                }
            }

            console.log('buildApiUrl - upravený endpoint:', endpoint);

            let baseUrl = API_URL;
            if (baseUrl.endsWith('/')) {
                baseUrl = baseUrl.slice(0, -1);
            }

            let url = baseUrl + '/' + endpoint;
            console.log('buildApiUrl - základní URL:', url);

            const queryParams = new URLSearchParams();
            for (const key in params) {
                if (params[key] !== null && params[key] !== undefined) {
                    queryParams.append(key, params[key]);
                }
            }

            const queryString = queryParams.toString();
            if (queryString) {
                url += `?${queryString}`;
            }

            console.log('buildApiUrl - výsledná URL:', url);
            return url;
        }
        
        async function testDirectApi() {
            const resultDiv = document.getElementById('direct-result');
            resultDiv.innerHTML = '<div class="info">Testování přímého API volání...</div>';
            
            try {
                const response = await fetch('api/users.php');
                console.log('Direct API response:', response);
                
                const text = await response.text();
                console.log('Direct API text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Odpověď není platný JSON: ' + text);
                }
                
                if (data.users) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ API funguje</div>
                        <p>Počet uživatelů: ${data.users.length}</p>
                        <table>
                            <tr><th>ID</th><th>Username</th><th>Role</th><th>Active</th></tr>
                            ${data.users.map(user => `
                                <tr>
                                    <td>${user.id}</td>
                                    <td>${user.username}</td>
                                    <td>${user.role}</td>
                                    <td>${user.active ? 'Ano' : 'Ne'}</td>
                                </tr>
                            `).join('')}
                        </table>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ Neočekávaná odpověď</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function testBuildApiUrl() {
            const resultDiv = document.getElementById('build-result');
            resultDiv.innerHTML = '<div class="info">Testování přes buildApiUrl...</div>';
            
            try {
                const url = buildApiUrl('users');
                console.log('Built URL:', url);
                
                const response = await fetch(url);
                console.log('BuildApiUrl response:', response);
                
                const text = await response.text();
                console.log('BuildApiUrl text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Odpověď není platný JSON: ' + text);
                }
                
                resultDiv.innerHTML = `
                    <div class="success">✓ buildApiUrl funguje</div>
                    <p>URL: ${url}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function loginAndTest() {
            const resultDiv = document.getElementById('login-test-result');
            resultDiv.innerHTML = '<div class="info">Přihlašování a testování...</div>';
            
            try {
                // Nejdříve se přihlásíme
                const loginResponse = await fetch(buildApiUrl('simple_auth', { action: 'login' }), {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const loginData = await loginResponse.json();
                
                if (!loginData.success) {
                    throw new Error('Přihlášení selhalo: ' + loginData.error);
                }
                
                // Nyní testujeme API uživatelů
                const usersResponse = await fetch(buildApiUrl('users'));
                const usersData = await usersResponse.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Přihlášení a API test úspěšný</div>
                    <p>Přihlášený uživatel: ${loginData.user.username}</p>
                    <p>Počet uživatelů: ${usersData.users ? usersData.users.length : 'N/A'}</p>
                    <pre>${JSON.stringify(usersData, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        // Automatický test při načtení stránky
        window.onload = function() {
            testDirectApi();
        };
    </script>
</body>
</html>
