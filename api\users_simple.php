<?php
/**
 * Jednoduchý API endpoint pro uživatele
 *
 * Tento soubor slouží jako jednoduchý API endpoint pro uživatele.
 */

// Zapnutí zobrazování chyb pro ladění
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Načtení utility pro práci s databází
require_once __DIR__ . '/../utils/database.php';

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Kontrola a vytvoření potřebných tabulek a výchozího administrátorského účtu
try {
    ensureTablesExist();
} catch (PDOException $e) {
    // Pokud se nepodaří vytvořit tabulky, vrátíme chybu
    http_response_code(500);
    echo json_encode(['error' => 'Nepodařilo se vytvořit potřebné tabulky: ' . $e->getMessage()]);
    exit;
}

// Načtení uživatelů z databáze
try {
    $pdo = getDbConnection();

    $stmt = $pdo->prepare("
        SELECT
            id,
            username,
            role,
            full_name,
            email,
            active,
            created_at,
            updated_at
        FROM
            inventory_users
        WHERE
            active = TRUE
        ORDER BY
            username
    ");

    $stmt->execute();

    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Pokud se nepodaří načíst uživatele, použijeme simulovaná data
    error_log("Chyba při načítání uživatelů z databáze: " . $e->getMessage());

    // Simulovaní uživatelé jako záložní řešení
    $users = [
        [
            'id' => 1,
            'username' => 'admin',
            'role' => 'admin',
            'full_name' => 'Administrátor',
            'email' => null,
            'active' => true,
            'created_at' => '2023-01-01 00:00:00',
            'updated_at' => '2023-01-01 00:00:00'
        ],
        [
            'id' => 2,
            'username' => 'manager',
            'role' => 'manager',
            'full_name' => 'Manažer',
            'email' => null,
            'active' => true,
            'created_at' => '2023-01-01 00:00:00',
            'updated_at' => '2023-01-01 00:00:00'
        ],
        [
            'id' => 3,
            'username' => 'user',
            'role' => 'user',
            'full_name' => 'Uživatel',
            'email' => null,
            'active' => true,
            'created_at' => '2023-01-01 00:00:00',
            'updated_at' => '2023-01-01 00:00:00'
        ]
    ];
}

// Získání ID uživatele z query stringu
$userId = $_GET['id'] ?? null;

// Pokud je zadáno ID, vrátíme detail uživatele
if ($userId) {
    $userId = (int) $userId;

    $user = null;
    foreach ($users as $u) {
        if ($u['id'] === $userId) {
            $user = $u;
            break;
        }
    }

    if ($user) {
        echo json_encode(['user' => $user]);
    } else {
        http_response_code(404);
        echo json_encode(['error' => 'Uživatel nenalezen']);
    }
} else {
    // Jinak vrátíme seznam uživatelů
    echo json_encode(['users' => $users]);
}
