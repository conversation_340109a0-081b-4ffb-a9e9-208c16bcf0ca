<?php
/**
 * Test aktualizace zadaného množství pouze v celkové inventuře
 * 
 * Tento skript testuje, zda se zadané množství správně aktualizuje pouze v celkové inventuře,
 * ale ne v jednotlivých záznamech uživatelů.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování do souboru
function logToFile($message) {
    $logFile = __DIR__ . '/test_only_totals.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logToFile("Připojení k databázi úspěšné");
    
    // Výběr náhodného produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logToFile("Nebyl nalezen žádný produkt s kladným množstvím na skladě");
        echo "Test selhal. Zkontrolujte logovací soubor: " . __DIR__ . '/test_only_totals.log';
        exit;
    }
    
    logToFile("Vybrán produkt pro test: " . $product['name'] . " (ID: " . $product['id'] . ", Množství na skladě: " . $product['units'] . ")");
    
    // Nalezení aktivní inventurní relace
    $stmt = $pdo->query("
        SELECT id
        FROM inventory_sessions
        WHERE status = 'active'
        LIMIT 1
    ");
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        logToFile("Nebyla nalezena žádná aktivní inventurní relace");
        echo "Test selhal. Zkontrolujte logovací soubor: " . __DIR__ . '/test_only_totals.log';
        exit;
    }
    
    logToFile("Nalezena aktivní inventurní relace s ID: " . $session['id']);
    
    // Zjištění aktuálního zadaného množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $total = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $currentTotal = $total ? $total['total_zadane_mnozstvi'] : 0;
    logToFile("Aktuální zadané množství v celkové inventuře: " . $currentTotal);
    
    // Zjištění aktuálního zadaného množství v jednotlivých inventurních záznamech
    $stmt = $pdo->prepare("
        SELECT id, user_id, zadane_mnozstvi
        FROM inventory_entries
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    logToFile("Aktuální zadané množství v jednotlivých inventurních záznamech:");
    foreach ($entries as $entry) {
        logToFile("  Záznam ID: " . $entry['id'] . ", Uživatel ID: " . $entry['user_id'] . ", Zadané množství: " . $entry['zadane_mnozstvi']);
    }
    
    // Simulace prodeje produktu (snížení množství na skladě)
    $unitsToSell = 1;
    $newUnits = $product['units'] - $unitsToSell;
    
    logToFile("Simuluji prodej " . $unitsToSell . " kusů produktu " . $product['name']);
    
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = ?
        WHERE product = ?
    ");
    $stmt->execute([$newUnits, $product['id']]);
    
    // Kontrola, zda se změnilo zadané množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $newTotal = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $newTotalValue = $newTotal ? $newTotal['total_zadane_mnozstvi'] : 0;
    logToFile("Nové zadané množství v celkové inventuře: " . $newTotalValue);
    
    // Kontrola, zda se zadané množství změnilo o správnou hodnotu
    $expectedTotal = $currentTotal - $unitsToSell;
    if ($newTotalValue == $expectedTotal) {
        logToFile("Test celkové inventury úspěšný! Zadané množství se změnilo o správnou hodnotu.");
        logToFile("Očekávaná hodnota: " . $expectedTotal . ", Skutečná hodnota: " . $newTotalValue);
    } else {
        logToFile("Test celkové inventury selhal! Zadané množství se nezměnilo o správnou hodnotu.");
        logToFile("Očekávaná hodnota: " . $expectedTotal . ", Skutečná hodnota: " . $newTotalValue);
    }
    
    // Kontrola, zda se nezměnilo zadané množství v jednotlivých inventurních záznamech
    $stmt = $pdo->prepare("
        SELECT id, user_id, zadane_mnozstvi
        FROM inventory_entries
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $newEntries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    logToFile("Nové zadané množství v jednotlivých inventurních záznamech:");
    $allEntriesUnchanged = true;
    foreach ($newEntries as $index => $newEntry) {
        $oldEntry = $entries[$index] ?? null;
        if ($oldEntry) {
            logToFile("  Záznam ID: " . $newEntry['id'] . ", Uživatel ID: " . $newEntry['user_id'] . ", Zadané množství: " . $newEntry['zadane_mnozstvi']);
            logToFile("  Původní hodnota: " . $oldEntry['zadane_mnozstvi'] . ", Nová hodnota: " . $newEntry['zadane_mnozstvi']);
            
            if ($newEntry['zadane_mnozstvi'] != $oldEntry['zadane_mnozstvi']) {
                $allEntriesUnchanged = false;
            }
        }
    }
    
    if ($allEntriesUnchanged) {
        logToFile("Test jednotlivých záznamů úspěšný! Zadané množství se nezměnilo v žádném záznamu.");
    } else {
        logToFile("Test jednotlivých záznamů selhal! Zadané množství se změnilo v některém záznamu.");
    }
    
    logToFile("Test dokončen");
    
    // Výpis cesty k logovacímu souboru
    echo "Test byl dokončen. Logovací soubor: " . __DIR__ . '/test_only_totals.log';
    
} catch (PDOException $e) {
    logToFile("Chyba při připojení k databázi: " . $e->getMessage());
    echo "Došlo k chybě. Zkontrolujte logovací soubor: " . __DIR__ . '/test_only_totals.log';
}
?>
