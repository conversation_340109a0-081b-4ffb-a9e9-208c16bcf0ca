<?php
/**
 * API pro aktualizaci zadaného množství v celkové inventuře
 *
 * Tento skript poskytuje API pro aktualizaci zadaného množství v celkové inventuře.
 *
 * Metoda: POST
 * Parametry:
 * - product_id: ID produktu
 * - quantity: Nové zadané množství
 *
 * Odpověď:
 * - success: true/false
 * - message: Zpráva o výsledku
 * - updated_count: Počet aktualizovaných záznamů
 */

// Nastavení hlaviček pro CORS a JSON
header('Access-Control-Allow-Origin: *');
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/../utils/database.php';

// Funkce pro odpověď
function sendResponse($success, $message, $data = []) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Kontrola metody
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Neplatná metoda. Použijte POST.');
}

// Získání dat z požadavku
$data = json_decode(file_get_contents('php://input'), true);

// Pokud data nejsou v JSON formátu, zkusíme je získat z $_POST
if (empty($data)) {
    $data = $_POST;
}

// Kontrola parametrů
if (empty($data['product_id'])) {
    sendResponse(false, 'Chybí parametr product_id.');
}

if (!isset($data['quantity'])) {
    sendResponse(false, 'Chybí parametr quantity.');
}

$productId = $data['product_id'];
$quantity = $data['quantity'];

// Připojení k databázi
try {
    $pdo = getDbConnection();

    // Kontrola, zda existují aktivní inventory_sessions
    $stmt = $pdo->query("SELECT COUNT(*) FROM inventory_sessions WHERE status = 'active'");
    $activeSessionsCount = $stmt->fetchColumn();

    if ($activeSessionsCount == 0) {
        sendResponse(false, 'Nejsou žádné aktivní inventory_sessions.');
    }

    // Kontrola, zda produkt existuje v inventory_totals pro aktivní sessions
    $stmt = $pdo->prepare("
        SELECT COUNT(*)
        FROM inventory_totals
        WHERE product_id = ?
        AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
    ");
    $stmt->execute([$productId]);
    $existsInInventory = $stmt->fetchColumn() > 0;

    if (!$existsInInventory) {
        sendResponse(false, 'Produkt nebyl nalezen v inventory_totals pro aktivní inventory_sessions.');
    }

    // Aktualizace zadaného množství pro aktivní sessions
    $stmt = $pdo->prepare("
        UPDATE inventory_totals
        SET total_zadane_mnozstvi = ?,
            last_updated = NOW()
        WHERE product_id = ?
        AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active')
    ");
    $stmt->execute([$quantity, $productId]);
    $updatedCount = $stmt->rowCount();

    if ($updatedCount > 0) {
        sendResponse(true, 'Zadané množství bylo úspěšně aktualizováno.', [
            'updated_count' => $updatedCount,
            'product_id' => $productId,
            'quantity' => $quantity
        ]);
    } else {
        sendResponse(false, 'Zadané množství nebylo aktualizováno.', [
            'product_id' => $productId,
            'quantity' => $quantity
        ]);
    }
} catch (PDOException $e) {
    sendResponse(false, 'Chyba při připojení k databázi: ' . $e->getMessage());
}
?>
