<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- <PERSON><PERSON><PERSON><PERSON>ě vypnuto pro testování
        <rewrite>
            <rules>
                <rule name="API Auth Rewrite" stopProcessing="true">
                    <match url="^api/auth/(.*)$" />
                    <action type="Rewrite" url="api/auth.php?endpoint={R:1}" appendQueryString="true" />
                </rule>

                <rule name="API Inventory Rewrite" stopProcessing="true">
                    <match url="^api/inventory/(.*)$" />
                    <action type="Rewrite" url="api/inventory.php?path=/{R:1}" appendQueryString="true" />
                </rule>

                <rule name="API Users Rewrite" stopProcessing="true">
                    <match url="^api/users/(.*)$" />
                    <action type="Rewrite" url="api/users.php?id={R:1}" appendQueryString="true" />
                </rule>
            </rules>
        </rewrite>
        -->
    </system.webServer>
</configuration>
