<?php
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> test přihlašování bez frontend
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🔐 Přímý test přihlašování</h1>";

try {
    // Nejprve zajistíme, že tabulky existují
    echo "<h2>🗄️ Kontrola a vytvoření tabulek</h2>";
    
    $pdo = getDbConnection();
    logMessage("✅ Databázové připojení ú<PERSON>né");
    
    // Kontrola existence tabulky inventory_users
    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_users'");
    $tableExists = $stmt->rowCount() > 0;
    
    if (!$tableExists) {
        logMessage("⚠️ Tabulka inventory_users neexistuje, vytvářím...");
        
        $createTableSQL = "
            CREATE TABLE `inventory_users` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `username` varchar(255) NOT NULL,
                `password` varchar(255) NOT NULL,
                `role` enum('admin','manager','user') NOT NULL DEFAULT 'user',
                `status` enum('active','inactive') NOT NULL DEFAULT 'active',
                `active` tinyint(1) NOT NULL DEFAULT 1,
                `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`),
                UNIQUE KEY `username` (`username`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ";
        
        $pdo->exec($createTableSQL);
        logMessage("✅ Tabulka inventory_users vytvořena");
    } else {
        logMessage("✅ Tabulka inventory_users existuje");
    }
    
    // Kontrola existence admin uživatele
    $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
    $stmt->execute();
    $adminUser = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$adminUser) {
        logMessage("⚠️ Admin uživatel neexistuje, vytvářím...");
        
        $stmt = $pdo->prepare("
            INSERT INTO inventory_users (username, password, role, status, active)
            VALUES ('admin', 'admin123', 'admin', 'active', 1)
        ");
        $stmt->execute();
        
        logMessage("✅ Admin uživatel vytvořen (admin/admin123)");
        
        // Znovu načteme admin uživatele
        $stmt = $pdo->prepare("SELECT * FROM inventory_users WHERE username = 'admin'");
        $stmt->execute();
        $adminUser = $stmt->fetch(PDO::FETCH_ASSOC);
    } else {
        logMessage("✅ Admin uživatel existuje");
    }
    
    echo "<h2>👤 Informace o admin uživateli</h2>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Pole</th><th>Hodnota</th></tr>";
    foreach ($adminUser as $key => $value) {
        echo "<tr><td>" . htmlspecialchars($key) . "</td><td>" . htmlspecialchars($value) . "</td></tr>";
    }
    echo "</table>";
    
    echo "<h2>🧪 Test autentifikační funkce</h2>";
    
    // Test 1: Přímé volání authenticateUser
    logMessage("Test 1: Přímé volání authenticateUser('admin', 'admin123')");
    
    $result = authenticateUser('admin', 'admin123');
    
    if ($result) {
        logMessage("✅ Autentifikace úspěšná!");
        echo "<h3>📋 Výsledek autentifikace:</h3>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        print_r($result);
        echo "</pre>";
    } else {
        logMessage("❌ Autentifikace selhala!", true);
    }
    
    // Test 2: Kontrola session
    echo "<h2>🔄 Test session</h2>";
    
    if (isLoggedIn()) {
        logMessage("✅ Uživatel je přihlášen");
        $currentUser = getCurrentUser();
        echo "<h3>📋 Aktuální uživatel:</h3>";
        echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
        print_r($currentUser);
        echo "</pre>";
    } else {
        logMessage("⚠️ Uživatel není přihlášen");
    }
    
    // Test 3: Simulace API volání
    echo "<h2>🌐 Test API volání</h2>";
    
    $testData = [
        'username' => 'admin',
        'password' => 'admin123'
    ];
    
    logMessage("Simuluji POST požadavek na api/auth.php s daty:");
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
    echo json_encode($testData, JSON_PRETTY_PRINT);
    echo "</pre>";
    
    // Simulace API volání pomocí cURL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/PU/INVENTURA%20X/INVX1.5/api/auth.php?action=login');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($testData))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    echo "<h3>📡 Odpověď API:</h3>";
    echo "<p><strong>HTTP kód:</strong> " . $httpCode . "</p>";
    
    echo "<h4>📋 Headers:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px; font-size: 12px;'>";
    echo htmlspecialchars($headers);
    echo "</pre>";
    
    echo "<h4>📄 Body:</h4>";
    echo "<pre style='background: " . ($httpCode == 200 ? '#d4edda' : '#f8d7da') . "; padding: 10px; border-radius: 5px;'>";
    echo htmlspecialchars($body);
    echo "</pre>";
    
    // Pokus o parsování JSON odpovědi
    $jsonResponse = json_decode($body, true);
    if ($jsonResponse) {
        echo "<h4>🔍 Parsovaná JSON odpověď:</h4>";
        echo "<pre style='background: #e2e3e5; padding: 10px; border-radius: 5px;'>";
        print_r($jsonResponse);
        echo "</pre>";
    }
    
    // Test 4: Alternativní přihlašovací údaje
    echo "<h2>🔄 Test alternativních přihlašovacích údajů</h2>";
    
    $alternativeCredentials = [
        ['admin', 'admin'],
        ['admin', 'password'],
        ['admin', ''],
        ['testuser', 'test123']
    ];
    
    foreach ($alternativeCredentials as $creds) {
        $username = $creds[0];
        $password = $creds[1];
        
        logMessage("Test: $username / " . ($password ? $password : '(prázdné heslo)'));
        
        $result = authenticateUser($username, $password);
        if ($result) {
            logMessage("✅ Úspěch pro $username");
        } else {
            logMessage("❌ Neúspěch pro $username", true);
        }
    }
    
} catch (Exception $e) {
    logMessage("❌ Chyba: " . $e->getMessage(), true);
    echo "<pre style='background: #f8d7da; padding: 10px;'>";
    echo "Soubor: " . $e->getFile() . "\n";
    echo "Řádek: " . $e->getLine() . "\n";
    echo "Chyba: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
    echo "</pre>";
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='debug_login_issue.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Debug přihlašování</a>";
echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
echo "</p>";
?>
