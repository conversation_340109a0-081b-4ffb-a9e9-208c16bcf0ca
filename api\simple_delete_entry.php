<?php
/**
 * <PERSON><PERSON>du<PERSON>é ma<PERSON>í inventurního z<PERSON>
 *
 * Tento soubor zpracovává požadavky na mazání inventurních záznamů bez složitých SQL dotazů.
 */

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../utils/validation.php';

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Kontrola, zda je uživatel přihlášen
if (!isLoggedIn()) {
    sendResponse(['error' => 'Neautorizovaný přístup'], 401);
    exit;
}

// Získání ID záznamu z GET parametru
$entryId = $_GET['id'] ?? null;

// Logování pro diagnostiku
error_log("simple_delete_entry.php - začátek skriptu");
error_log("simple_delete_entry.php - GET parametry: " . print_r($_GET, true));
error_log("simple_delete_entry.php - ID záznamu: " . $entryId);

if (!$entryId) {
    error_log("simple_delete_entry.php - chybí ID záznamu");
    sendResponse(['error' => 'ID záznamu je povinné'], 400);
    exit;
}

// Validace ID záznamu
$entryId = intval($entryId);

if ($entryId <= 0) {
    error_log("simple_delete_entry.php - neplatné ID záznamu");
    sendResponse(['error' => 'Neplatné ID záznamu'], 400);
    exit;
}

try {
    $pdo = getDbConnection();

    // Nejprve zjistíme, zda je uživatel admin nebo manager
    $user = getCurrentUser();
    $isAdminOrManager = ($user['role'] === 'admin' || $user['role'] === 'manager');

    // Získáme informace o záznamu, který chceme smazat
    $stmt = $pdo->prepare("SELECT product_id, session_id FROM inventory_entries WHERE id = ?");
    $stmt->execute([$entryId]);
    $entry = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$entry) {
        error_log("simple_delete_entry.php - záznam s ID $entryId nebyl nalezen");
        sendResponse(['error' => 'Záznam nebyl nalezen'], 404);
        exit;
    }

    $productId = $entry['product_id'];
    $sessionId = $entry['session_id'];

    // Pokud je uživatel admin nebo manager, smažeme všechny záznamy pro daný produkt v dané relaci
    if ($isAdminOrManager) {
        $sql = "DELETE FROM inventory_entries WHERE product_id = ? AND session_id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$productId, $sessionId]);
        $rowCount = $stmt->rowCount();
        error_log("simple_delete_entry.php - počet smazaných řádků (admin/manager): " . $rowCount);
    } else {
        // Běžný uživatel může smazat pouze svůj záznam
        $sql = "DELETE FROM inventory_entries WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$entryId]);
        $rowCount = $stmt->rowCount();
        error_log("simple_delete_entry.php - počet smazaných řádků (běžný uživatel): " . $rowCount);
    }

    if ($rowCount > 0) {
        sendResponse([
            'success' => true,
            'message' => 'Inventurní záznam byl úspěšně smazán'
        ]);
    } else {
        error_log("simple_delete_entry.php - záznam s ID $entryId nebyl nalezen nebo nemohl být smazán");
        sendResponse(['error' => 'Záznam nebyl nalezen nebo nemohl být smazán'], 404);
    }
} catch (Exception $e) {
    error_log("simple_delete_entry.php - chyba při mazání inventurního záznamu: " . $e->getMessage());
    sendResponse(['error' => 'Došlo k chybě při mazání inventurního záznamu: ' . $e->getMessage()], 500);
}

/**
 * Odeslání JSON odpovědi
 *
 * @param mixed $data Data odpovědi
 * @param int $statusCode HTTP stavový kód
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}
