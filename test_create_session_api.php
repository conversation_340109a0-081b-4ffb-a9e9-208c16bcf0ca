<?php
/**
 * Test vytváření inventurní relace přes API
 */

// Zapnutí zobrazování chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

function logMessage($message, $isError = false) {
    $style = $isError ? 'color: red;' : 'color: green;';
    echo "<p style='$style'>" . htmlspecialchars($message) . "</p>";
}

echo "<h1>🧪 Test vytváření inventurní relace</h1>";

// Spuštění session
if (!isset($_SESSION)) {
    session_start();
}

try {
    $pdo = getDbConnection();
    
    echo "<h2>🔐 Kontrola přihlášení</h2>";
    
    if (!isLoggedIn()) {
        logMessage("❌ Uživatel není přihlášen - pokusím se přihlásit", true);
        
        // Najdeme nějakého uživatele
        $stmt = $pdo->query("SELECT * FROM inventory_users WHERE status = 'active' ORDER BY role DESC LIMIT 1");
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['role'] = $user['role'];
            
            logMessage("✅ Automaticky přihlášen uživatel: " . $user['username'] . " (role: " . $user['role'] . ")");
        } else {
            logMessage("❌ Žádný aktivní uživatel v databázi!", true);
            exit;
        }
    } else {
        $user = getCurrentUser();
        logMessage("✅ Uživatel je přihlášen: " . $user['username'] . " (role: " . $user['role'] . ")");
    }
    
    echo "<h2>🗄️ Test přímého vytvoření relace v databázi</h2>";
    
    try {
        $user = getCurrentUser();
        
        $stmt = $pdo->prepare("
            INSERT INTO inventory_sessions (user_id)
            VALUES (:user_id)
        ");
        
        $stmt->execute(['user_id' => $user['id']]);
        $sessionId = $pdo->lastInsertId();
        
        logMessage("✅ Relace vytvořena přímo v databázi s ID: $sessionId");
        
        // Ověříme, že relace existuje
        $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = ?");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($session) {
            logMessage("✅ Relace ověřena v databázi");
            echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
            echo "<tr><th>Parametr</th><th>Hodnota</th></tr>";
            foreach ($session as $key => $value) {
                echo "<tr><td>" . htmlspecialchars($key) . "</td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
            }
            echo "</table>";
        } else {
            logMessage("❌ Relace nebyla nalezena v databázi!", true);
        }
        
    } catch (Exception $e) {
        logMessage("❌ Chyba při přímém vytváření relace: " . $e->getMessage(), true);
    }
    
    echo "<h2>📡 Test API funkce createSession()</h2>";
    
    try {
        // Simulace POST požadavku
        $_SERVER['REQUEST_METHOD'] = 'POST';
        $_SERVER['PATH_INFO'] = '/';
        $_GET['action'] = 'sessions';
        
        // Simulace JSON vstupu
        $jsonInput = json_encode([]);
        
        // Zachytíme výstup
        ob_start();
        
        // Přepíšeme file_get_contents pro php://input
        $GLOBALS['test_input'] = $jsonInput;
        
        // Definujeme testovací funkci createSession
        function testCreateSession() {
            global $pdo;
            
            // Získání JSON vstupu
            $input = json_decode($GLOBALS['test_input'], true);
            
            // Kontrola, zda je vstup platný JSON
            if (json_last_error() !== JSON_ERROR_NONE) {
                return ['error' => 'Neplatný JSON: ' . json_last_error_msg()];
            }
            
            $user = getCurrentUser();
            
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO inventory_sessions (user_id)
                    VALUES (:user_id)
                ");
                
                $stmt->execute(['user_id' => $user['id']]);
                
                $sessionId = $pdo->lastInsertId();
                
                return [
                    'success' => true,
                    'session_id' => $sessionId,
                    'message' => 'Inventurní relace byla úspěšně vytvořena'
                ];
            } catch (Exception $e) {
                error_log("Chyba při vytváření inventurní relace: " . $e->getMessage());
                return ['error' => 'Došlo k chybě při vytváření inventurní relace'];
            }
        }
        
        $result = testCreateSession();
        
        ob_end_clean();
        
        if (isset($result['error'])) {
            logMessage("❌ API funkce vrátila chybu: " . $result['error'], true);
        } else {
            logMessage("✅ API funkce úspěšná");
            logMessage("📊 Vytvořena relace s ID: " . $result['session_id']);
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        logMessage("❌ Chyba při testování API funkce: " . $e->getMessage(), true);
    }
    
    echo "<h2>🌐 Test HTTP volání API</h2>";
    
    $apiUrl = "http://localhost/PU/INVENTURA%20X/INVX1.5/api/inventory.php?action=sessions";
    
    // Vytvoření kontextu s cookies
    $cookieHeader = '';
    if (!empty($_COOKIE)) {
        $cookies = [];
        foreach ($_COOKIE as $name => $value) {
            $cookies[] = "$name=$value";
        }
        $cookieHeader = 'Cookie: ' . implode('; ', $cookies);
    }
    
    $postData = json_encode([]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Content-Length: ' . strlen($postData),
                $cookieHeader
            ],
            'content' => $postData,
            'timeout' => 10
        ]
    ]);
    
    echo "<p><strong>URL:</strong> <code>" . htmlspecialchars($apiUrl) . "</code></p>";
    echo "<p><strong>POST data:</strong> <code>" . htmlspecialchars($postData) . "</code></p>";
    echo "<p><strong>Headers:</strong> <code>" . htmlspecialchars($cookieHeader) . "</code></p>";
    
    $response = @file_get_contents($apiUrl, false, $context);
    
    if ($response === false) {
        $error = error_get_last();
        logMessage("❌ HTTP volání selhalo: " . ($error['message'] ?? 'Neznámá chyba'), true);
        
        // Zkusíme získat HTTP response headers
        if (isset($http_response_header)) {
            echo "<h4>HTTP Response Headers:</h4>";
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            foreach ($http_response_header as $header) {
                echo htmlspecialchars($header) . "\n";
            }
            echo "</pre>";
        }
    } else {
        $data = json_decode($response, true);
        
        if (json_last_error() === JSON_ERROR_NONE) {
            if (isset($data['error'])) {
                logMessage("❌ API vrátilo chybu: " . $data['error'], true);
            } else {
                logMessage("✅ HTTP API volání úspěšné");
                
                if (isset($data['session_id'])) {
                    logMessage("📊 Vytvořena relace s ID: " . $data['session_id']);
                }
            }
            
            echo "<h4>Odpověď API:</h4>";
            echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo "</pre>";
        } else {
            logMessage("❌ Chyba při parsování JSON odpovědi", true);
            echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
            echo htmlspecialchars($response);
            echo "</pre>";
        }
    }
    
    echo "<h2>📊 Aktuální stav inventurních relací</h2>";
    
    $stmt = $pdo->query("
        SELECT 
            s.*,
            u.username
        FROM inventory_sessions s
        LEFT JOIN inventory_users u ON s.user_id = u.id
        ORDER BY s.start_time DESC
        LIMIT 5
    ");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sessions)) {
        logMessage("⚠️ Žádné inventurní relace v databázi");
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Uživatel</th><th>Status</th><th>Začátek</th><th>Konec</th></tr>";
        
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($session['id']) . "</td>";
            echo "<td>" . htmlspecialchars($session['username'] ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($session['status']) . "</td>";
            echo "<td>" . htmlspecialchars($session['start_time']) . "</td>";
            echo "<td>" . htmlspecialchars($session['end_time'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        logMessage("✅ Nalezeno " . count($sessions) . " inventurních relací");
    }
    
} catch (Exception $e) {
    logMessage("❌ Celková chyba: " . $e->getMessage(), true);
    echo "<pre style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
    echo "Soubor: " . $e->getFile() . "\n";
    echo "Řádek: " . $e->getLine() . "\n";
    echo "Chyba: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString();
    echo "</pre>";
}

echo "<h2>🔗 Navigace</h2>";
echo "<p>";
echo "<a href='debug_500_error.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Debug 500 chyby</a>";
echo "<a href='test_auth_status.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Test autentifikace</a>";
echo "<a href='index.html' style='background: #ffc107; color: #212529; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Hlavní stránka</a>";
echo "</p>";
?>
