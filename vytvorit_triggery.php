<?php
/**
 * Jednoduchý skript pro vytvoření triggerů bez použití sloupce 'units'
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování
function logMessage($message, $isError = false) {
    $color = $isError ? 'red' : 'green';
    echo "<p style='color: $color;'>" . ($isError ? '✗ ' : '✓ ') . htmlspecialchars($message) . "</p>";
}

// HTML hlavička
echo '<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vytvoření triggerů</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        h1, h2, h3 {
            color: #333;
        }
        .back-link {
            display: inline-block;
            margin-top: 20px;
        }
        .button {
            display: inline-block;
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            font-size: 16px;
            margin: 10px 5px;
            cursor: pointer;
            border-radius: 4px;
            border: none;
        }
        .button:hover {
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <h1>Vytvoření triggerů bez použití sloupce \'units\'</h1>
';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logMessage("Připojení k databázi úspěšné.");
    
    // Kontrola tabulky ticketlines
    $stmt = $pdo->query("SHOW TABLES LIKE 'ticketlines'");
    $ticketlinesExists = $stmt->rowCount() > 0;
    
    if (!$ticketlinesExists) {
        logMessage("Tabulka 'ticketlines' neexistuje!", true);
    } else {
        logMessage("Tabulka 'ticketlines' existuje.");
        
        // Získání struktury tabulky
        $stmt = $pdo->query("DESCRIBE ticketlines");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Výpis sloupců tabulky ticketlines
        echo "<h2>Sloupce tabulky ticketlines</h2>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li><strong>{$column['Field']}</strong> ({$column['Type']})</li>";
        }
        echo "</ul>";
        
        // Formulář pro vytvoření triggerů
        echo "<h2>Vytvoření triggerů</h2>";
        
        if (isset($_POST['create_triggers'])) {
            $quantityColumn = $_POST['quantity_column'];
            
            if (!$quantityColumn) {
                logMessage("Nebyl vybrán žádný sloupec pro množství!", true);
            } else {
                // Vytvoření nových triggerů
                try {
                    // Kontrola, zda existuje tabulka inventory_stock_changes
                    $stmt = $pdo->query("SHOW TABLES LIKE 'inventory_stock_changes'");
                    $stockChangesExists = $stmt->rowCount() > 0;
                    
                    if (!$stockChangesExists) {
                        logMessage("Tabulka 'inventory_stock_changes' neexistuje, vytvářím...");
                        
                        $pdo->exec("
                            CREATE TABLE `inventory_stock_changes` (
                              `id` INT AUTO_INCREMENT PRIMARY KEY,
                              `product_id` VARCHAR(255) NOT NULL,
                              `amount` DECIMAL(10,3) NOT NULL,
                              `change_type` ENUM('sale', 'update', 'delete', 'restock') NOT NULL,
                              `change_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                              `ticket_id` VARCHAR(255) NULL,
                              `details` TEXT NULL
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                        ");
                        
                        logMessage("Tabulka 'inventory_stock_changes' byla vytvořena.");
                    }
                    
                    // Trigger pro INSERT
                    $pdo->exec("
                        CREATE TRIGGER `update_inventory_totals_after_ticketlines_insert`
                        AFTER INSERT ON `ticketlines`
                        FOR EACH ROW
                        BEGIN
                            -- Aktualizace zadaného množství v celkové inventuře
                            UPDATE inventory_totals
                            SET total_zadane_mnozstvi = total_zadane_mnozstvi - NEW.$quantityColumn,
                                last_updated = NOW()
                            WHERE product_id = NEW.product
                            AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                        END
                    ");
                    logMessage("Trigger 'update_inventory_totals_after_ticketlines_insert' byl vytvořen.");
                    
                    // Trigger pro UPDATE
                    $pdo->exec("
                        CREATE TRIGGER `update_inventory_totals_after_ticketlines_update`
                        AFTER UPDATE ON `ticketlines`
                        FOR EACH ROW
                        BEGIN
                            -- Aktualizace zadaného množství v celkové inventuře pouze pokud se změnilo množství
                            IF NEW.$quantityColumn != OLD.$quantityColumn THEN
                                UPDATE inventory_totals
                                SET total_zadane_mnozstvi = total_zadane_mnozstvi - (NEW.$quantityColumn - OLD.$quantityColumn),
                                    last_updated = NOW()
                                WHERE product_id = NEW.product
                                AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                            END IF;
                        END
                    ");
                    logMessage("Trigger 'update_inventory_totals_after_ticketlines_update' byl vytvořen.");
                    
                    // Trigger pro DELETE
                    $pdo->exec("
                        CREATE TRIGGER `update_inventory_totals_after_ticketlines_delete`
                        AFTER DELETE ON `ticketlines`
                        FOR EACH ROW
                        BEGIN
                            -- Aktualizace zadaného množství v celkové inventuře
                            UPDATE inventory_totals
                            SET total_zadane_mnozstvi = total_zadane_mnozstvi + OLD.$quantityColumn,
                                last_updated = NOW()
                            WHERE product_id = OLD.product
                            AND session_id IN (SELECT id FROM inventory_sessions WHERE status = 'active');
                        END
                    ");
                    logMessage("Trigger 'update_inventory_totals_after_ticketlines_delete' byl vytvořen.");
                    
                    logMessage("Triggery pro aktualizaci inventáře byly úspěšně vytvořeny.");
                } catch (PDOException $e) {
                    logMessage("Chyba při vytváření triggerů: " . $e->getMessage(), true);
                }
            }
        }
        
        echo "<form method='post'>";
        echo "<p>Vyberte sloupec, který obsahuje množství v tabulce ticketlines:</p>";
        echo "<select name='quantity_column' required>";
        
        foreach ($columns as $column) {
            $selected = (strtolower($column['Field']) === 'quantity') ? 'selected' : '';
            echo "<option value='{$column['Field']}' $selected>{$column['Field']} ({$column['Type']})</option>";
        }
        
        echo "</select>";
        echo "<p><button type='submit' name='create_triggers' class='button'>Vytvořit triggery</button></p>";
        echo "</form>";
    }
    
    // Odkaz zpět
    echo "<p><a href='index.html' class='back-link'>Zpět na hlavní stránku</a></p>";
    
    // HTML patička
    echo "</body></html>";
    
} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    logMessage("Chyba při připojení k databázi: " . $e->getMessage(), true);
    echo "<p><a href='index.html' class='back-link'>Zpět na hlavní stránku</a></p>";
    echo "</body></html>";
}
?>
