<?php
/**
 * Test triggeru pro aktualizaci inventory_totals po prodeji
 * 
 * Tento skript testuje, zda trigger správně aktualizuje zadané množství v celkové inventuře
 * po prodeji produktů.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';

// Funkce pro logování do souboru
function logToFile($message) {
    $logFile = __DIR__ . '/test_trigger_varchar.log';
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message" . PHP_EOL, FILE_APPEND);
}

// Připojení k databázi
try {
    $pdo = getDbConnection();
    logToFile("Připojení k databázi úspěšné");
    
    // Výběr náhodného produktu pro test
    $stmt = $pdo->query("
        SELECT p.id, p.name, sc.units
        FROM products p
        JOIN stockcurrent sc ON p.id = sc.product
        WHERE sc.units > 0
        LIMIT 1
    ");
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        logToFile("Nebyl nalezen žádný produkt s kladným množstvím na skladě");
        echo "Test selhal. Zkontrolujte logovací soubor: " . __DIR__ . '/test_trigger_varchar.log';
        exit;
    }
    
    logToFile("Vybrán produkt pro test: " . $product['name'] . " (ID: " . $product['id'] . ", Množství na skladě: " . $product['units'] . ")");
    
    // Nalezení aktivní inventurní relace
    $stmt = $pdo->query("
        SELECT id
        FROM inventory_sessions
        WHERE status = 'active'
        LIMIT 1
    ");
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        logToFile("Nebyla nalezena žádná aktivní inventurní relace");
        echo "Test selhal. Zkontrolujte logovací soubor: " . __DIR__ . '/test_trigger_varchar.log';
        exit;
    }
    
    logToFile("Nalezena aktivní inventurní relace s ID: " . $session['id']);
    
    // Zjištění aktuálního zadaného množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $total = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $currentTotal = $total ? $total['total_zadane_mnozstvi'] : 0;
    logToFile("Aktuální zadané množství v celkové inventuře: " . $currentTotal);
    
    // Simulace prodeje produktu (snížení množství na skladě)
    $unitsToSell = 1;
    $newUnits = $product['units'] - $unitsToSell;
    
    logToFile("Simuluji prodej " . $unitsToSell . " kusů produktu " . $product['name']);
    
    $stmt = $pdo->prepare("
        UPDATE stockcurrent
        SET units = ?
        WHERE product = ?
    ");
    $stmt->execute([$newUnits, $product['id']]);
    
    // Kontrola, zda se změnilo zadané množství v celkové inventuře
    $stmt = $pdo->prepare("
        SELECT total_zadane_mnozstvi
        FROM inventory_totals
        WHERE product_id = ? AND session_id = ?
    ");
    $stmt->execute([$product['id'], $session['id']]);
    $newTotal = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $newTotalValue = $newTotal ? $newTotal['total_zadane_mnozstvi'] : 0;
    logToFile("Nové zadané množství v celkové inventuře: " . $newTotalValue);
    
    // Kontrola, zda se zadané množství změnilo o správnou hodnotu
    $expectedTotal = $currentTotal - $unitsToSell;
    if ($newTotalValue == $expectedTotal) {
        logToFile("Test úspěšný! Zadané množství se změnilo o správnou hodnotu.");
        logToFile("Očekávaná hodnota: " . $expectedTotal . ", Skutečná hodnota: " . $newTotalValue);
    } else {
        logToFile("Test selhal! Zadané množství se nezměnilo o správnou hodnotu.");
        logToFile("Očekávaná hodnota: " . $expectedTotal . ", Skutečná hodnota: " . $newTotalValue);
    }
    
    // Kontrola, zda byl vytvořen záznam v tabulce stockcurrent_log
    $stmt = $pdo->prepare("
        SELECT *
        FROM stockcurrent_log
        WHERE product_id = ?
        ORDER BY created DESC
        LIMIT 1
    ");
    $stmt->execute([$product['id']]);
    $log = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($log) {
        logToFile("Záznam v tabulce stockcurrent_log byl vytvořen:");
        logToFile("  ID: " . $log['id']);
        logToFile("  Produkt: " . $log['product_id']);
        logToFile("  Stará hodnota: " . $log['old_units']);
        logToFile("  Nová hodnota: " . $log['new_units']);
        logToFile("  Rozdíl: " . $log['difference']);
        logToFile("  Typ změny: " . ($log['change_type'] ?? 'N/A'));
        logToFile("  Čas: " . $log['created']);
    } else {
        logToFile("Záznam v tabulce stockcurrent_log nebyl vytvořen!");
    }
    
    logToFile("Test dokončen");
    
    // Výpis cesty k logovacímu souboru
    echo "Test byl dokončen. Logovací soubor: " . __DIR__ . '/test_trigger_varchar.log';
    
} catch (PDOException $e) {
    logToFile("Chyba při připojení k databázi: " . $e->getMessage());
    echo "Došlo k chybě. Zkontrolujte logovací soubor: " . __DIR__ . '/test_trigger_varchar.log';
}
?>
