<?php
/**
 * Aktualizace JavaScript kódu pro použití nového API endpointu
 */

// Kontrola, zda existuje soubor js/utils.js
$jsPath = __DIR__ . '/js/utils.js';

if (!file_exists($jsPath)) {
    echo "<p style='color: red;'>✗ Soubor js/utils.js neexistuje!</p>";
    exit;
}

echo "<p style='color: green;'>✓ Soubor js/utils.js existuje.</p>";

// Načtení obsahu souboru
$jsContent = file_get_contents($jsPath);

// Kontrola, zda existuje funkce handleAddInventoryEntry
if (strpos($jsContent, 'function handleAddInventoryEntry') === false) {
    echo "<p style='color: red;'>✗ Funkce handleAddInventoryEntry nebyla nalezena v souboru js/utils.js!</p>";
    exit;
}

echo "<p style='color: green;'>✓ Funkce handleAddInventoryEntry byla nalezena v souboru js/utils.js.</p>";

// Vytvoření zálohy souboru
$backupPath = __DIR__ . '/js/utils.js.bak.' . date('YmdHis');
file_put_contents($backupPath, $jsContent);

echo "<p style='color: green;'>✓ Byla vytvořena záloha souboru js/utils.js: " . basename($backupPath) . "</p>";

// Aktualizace funkce handleAddInventoryEntry
$oldCode = 'function handleAddInventoryEntry(e) {
    e.preventDefault();

    // Kontrola, zda je vybrána aktivní relace
    if (!activeSession) {
        showNotification(\'Nejprve vyberte nebo vytvořte inventurní relaci\', \'error\');
        return;
    }

    const eanCode = document.getElementById(\'product-ean\').textContent;
    const zadaneMnozstvi = parseFloat(document.getElementById(\'zadane-mnozstvi\').value);

    fetch(`api/inventory.php?action=entries`, {
        method: \'POST\',
        headers: {
            \'Content-Type\': \'application/json\'
        },
        body: JSON.stringify({
            session_id: activeSession.id,
            ean_code: eanCode,
            zadane_mnozstvi: zadaneMnozstvi
        })
    })';

$newCode = 'function handleAddInventoryEntry(e) {
    e.preventDefault();

    // Kontrola, zda je vybrána aktivní relace
    if (!activeSession) {
        showNotification(\'Nejprve vyberte nebo vytvořte inventurní relaci\', \'error\');
        return;
    }

    const eanCode = document.getElementById(\'product-ean\').textContent;
    const zadaneMnozstvi = parseFloat(document.getElementById(\'zadane-mnozstvi\').value);

    console.log("Odesílání požadavku na přidání inventurního záznamu:");
    console.log("- EAN kód:", eanCode);
    console.log("- Zadané množství:", zadaneMnozstvi);
    console.log("- Session ID:", activeSession.id);

    // Použití nového API endpointu
    fetch(`api/inventory_entry_create.php`, {
        method: \'POST\',
        headers: {
            \'Content-Type\': \'application/json\'
        },
        body: JSON.stringify({
            session_id: activeSession.id,
            ean_code: eanCode,
            zadane_mnozstvi: zadaneMnozstvi
        })
    })';

// Nahrazení kódu v souboru
$jsContent = str_replace($oldCode, $newCode, $jsContent);

// Uložení změněného souboru
file_put_contents($jsPath, $jsContent);

echo "<p style='color: green;'>✓ Funkce handleAddInventoryEntry byla úspěšně aktualizována.</p>";

echo "<p>Nyní zkuste znovu přidat produkt do inventury. JavaScript kód bude používat nový API endpoint <code>api/inventory_entry_create.php</code> místo původního <code>api/inventory.php?action=entries</code>.</p>";

echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
?>
