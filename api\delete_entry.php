<?php
/**
 * <PERSON><PERSON><PERSON><PERSON> inventurního záznamu
 *
 * Tento soubor zpracovává požadavky na mazání inventurních záznamů.
 */

require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';
require_once __DIR__ . '/../utils/validation.php';

// Nastavení typu obsahu na JSON
header('Content-Type: application/json');

// Kontrola, zda je uživatel přihlášen
if (!isLoggedIn()) {
    sendResponse(['error' => 'Neautorizovaný přístup'], 401);
    exit;
}

// Získání ID záznamu z GET parametru
$entryId = $_GET['id'] ?? null;

// Logování pro diagnostiku
error_log("delete_entry.php - začátek skriptu");
error_log("delete_entry.php - GET parametry: " . print_r($_GET, true));
error_log("delete_entry.php - ID záznamu: " . $entryId);

if (!$entryId) {
    error_log("delete_entry.php - chybí ID záznamu");
    sendResponse(['error' => 'ID záznamu je povinné'], 400);
    exit;
}

// Validace ID záznamu
$entryId = validateInt($entryId);

if (!$entryId) {
    error_log("delete_entry.php - neplatné ID záznamu");
    sendResponse(['error' => 'Neplatné ID záznamu'], 400);
    exit;
}

$pdo = getDbConnection();

// Kontrola, zda záznam existuje a získání potřebných informací
$stmt = $pdo->prepare("
    SELECT
        ie.id,
        ie.product_id,
        ie.session_id,
        ie.zadane_mnozstvi,
        ie.status,
        ie.user_id,
        s.status AS session_status
    FROM
        inventory_entries ie
    JOIN
        inventory_sessions s ON ie.session_id = s.id
    WHERE
        ie.id = :id
");

error_log("delete_entry.php - SQL dotaz pro získání záznamu: " . $stmt->queryString);
error_log("delete_entry.php - parametry: id=" . $entryId);

try {
    $stmt->execute(['id' => $entryId]);
    error_log("delete_entry.php - SQL dotaz proveden úspěšně");
} catch (Exception $e) {
    error_log("delete_entry.php - chyba při získávání záznamu: " . $e->getMessage());
    throw $e;
}

$entry = $stmt->fetch();
error_log("delete_entry.php - záznam získán: " . ($entry ? 'ano' : 'ne'));

if (!$entry) {
    error_log("delete_entry.php - záznam s ID $entryId nebyl nalezen");
    sendResponse(['error' => 'Inventurní záznam nenalezen'], 404);
    exit;
}

error_log("delete_entry.php - záznam nalezen, session_status: " . $entry['session_status']);

// Kontrola, zda je relace aktivní
if ($entry['session_status'] !== 'active') {
    error_log("delete_entry.php - relace není aktivní, status: " . $entry['session_status']);
    sendResponse(['error' => 'Inventurní relace není aktivní'], 400);
    exit;
}

error_log("delete_entry.php - relace je aktivní, pokračuji");

// Získání aktuálního uživatele
$user = getCurrentUser();

// Kontrola oprávnění - běžný uživatel může mazat pouze své záznamy
if (!isAdminOrManager() && $entry['user_id'] != $user['id']) {
    error_log("delete_entry.php - nedostatečná oprávnění, user_id: " . $user['id'] . ", entry user_id: " . $entry['user_id']);
    sendResponse(['error' => 'Nedostatečná oprávnění'], 403);
    exit;
}

try {
    // Začátek transakce
    $pdo->beginTransaction();

    // Fyzické smazání záznamu z databáze
    $stmt = $pdo->prepare("
        DELETE FROM inventory_entries
        WHERE id = :id
    ");

    error_log("delete_entry.php - SQL dotaz pro smazání záznamu: " . $stmt->queryString);
    error_log("delete_entry.php - parametry: id=" . $entryId);

    try {
        $stmt->execute(['id' => $entryId]);
        error_log("delete_entry.php - SQL dotaz proveden úspěšně, počet smazaných řádků: " . $stmt->rowCount());
    } catch (Exception $e) {
        error_log("delete_entry.php - chyba při mazání záznamu: " . $e->getMessage());
        throw $e;
    }

    // Kontrola, zda byl záznam skutečně smazán
    if ($stmt->rowCount() > 0) {
        // Kontrola, zda tabulka inventory_totals existuje
        $checkTableStmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'inventory_totals'
        ");
        $checkTableStmt->execute();
        $tableExists = $checkTableStmt->fetchColumn() > 0;

        error_log("delete_entry.php - tabulka inventory_totals existuje: " . ($tableExists ? 'ano' : 'ne'));

        if (!$tableExists) {
            error_log("delete_entry.php - tabulka inventory_totals neexistuje, přeskakuji aktualizaci celkových součtů");
            $pdo->commit();
            sendResponse([
                'success' => true,
                'message' => 'Inventurní záznam byl úspěšně smazán'
            ]);
            exit;
        }

        // Aktualizace celkových součtů v tabulce inventory_totals
        $updateTotalsStmt = $pdo->prepare("
            UPDATE inventory_totals
            SET total_zadane_mnozstvi = (
                SELECT COALESCE(SUM(zadane_mnozstvi), 0)
                FROM inventory_entries
                WHERE product_id = :product_id
                AND session_id = :session_id
                AND status = 'active'
            )
            WHERE product_id = :product_id
            AND session_id = :session_id
        ");

        error_log("delete_entry.php - aktualizace celkových součtů, product_id: " . $entry['product_id'] . ", session_id: " . $entry['session_id']);
        error_log("delete_entry.php - obsah záznamu: " . print_r($entry, true));

        // Kontrola, zda existují potřebné klíče v poli $entry
        if (!isset($entry['product_id']) || !isset($entry['session_id'])) {
            error_log("delete_entry.php - chybí potřebné klíče v poli \$entry");
            throw new Exception("Chybí potřebné klíče v poli \$entry");
        }

        try {
            $updateTotalsStmt->execute([
                'product_id' => $entry['product_id'],
                'session_id' => $entry['session_id']
            ]);
            error_log("delete_entry.php - aktualizace celkových součtů proběhla úspěšně");
        } catch (Exception $e) {
            error_log("delete_entry.php - chyba při aktualizaci celkových součtů: " . $e->getMessage());
            error_log("delete_entry.php - SQL dotaz: " . $updateTotalsStmt->queryString);
            error_log("delete_entry.php - parametry: product_id=" . $entry['product_id'] . ", session_id=" . $entry['session_id']);
            throw $e;
        }

        // Pokud není žádný záznam, odstraníme záznam z inventory_totals
        $deleteTotalsStmt = $pdo->prepare("
            DELETE FROM inventory_totals
            WHERE product_id = :product_id
            AND session_id = :session_id
            AND total_zadane_mnozstvi = 0
        ");

        error_log("delete_entry.php - odstranění prázdných záznamů z inventory_totals, product_id: " . $entry['product_id'] . ", session_id: " . $entry['session_id']);

        try {
            $deleteTotalsStmt->execute([
                'product_id' => $entry['product_id'],
                'session_id' => $entry['session_id']
            ]);
            error_log("delete_entry.php - odstranění prázdných záznamů proběhlo úspěšně");
        } catch (Exception $e) {
            error_log("delete_entry.php - chyba při odstraňování prázdných záznamů: " . $e->getMessage());
            error_log("delete_entry.php - SQL dotaz: " . $deleteTotalsStmt->queryString);
            error_log("delete_entry.php - parametry: product_id=" . $entry['product_id'] . ", session_id=" . $entry['session_id']);
            throw $e;
        }

        // Commit transakce
        $pdo->commit();

        sendResponse([
            'success' => true,
            'message' => 'Inventurní záznam byl úspěšně smazán'
        ]);
    } else {
        $pdo->rollBack();
        error_log("delete_entry.php - záznam s ID $entryId nemohl být smazán");
        sendResponse(['error' => 'Záznam nemohl být smazán'], 500);
    }
} catch (Exception $e) {
    // Rollback transakce v případě chyby
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    error_log("delete_entry.php - chyba při mazání inventurního záznamu: " . $e->getMessage());
    sendResponse(['error' => 'Došlo k chybě při mazání inventurního záznamu: ' . $e->getMessage()], 500);
}

/**
 * Odeslání JSON odpovědi
 *
 * @param mixed $data Data odpovědi
 * @param int $statusCode HTTP stavový kód
 */
function sendResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}
