<?php
/**
 * Synchronizace zadaného množství pro konkrétní produkt
 *
 * Tento skript synchronizuje zadané množství (zadane_mnozstvi) v tabulce inventory_entries
 * s aktuálním stavem (units) v tabulce stockcurrent pro konkrétní produkt.
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/utils/database.php';
require_once __DIR__ . '/utils/auth.php';

// Připojení k databázi
try {
    $pdo = getDbConnection();
    $results = [];

    // Kontrola, zda je uživatel přihlášen a má oprávnění
    if (!isLoggedIn()) {
        echo "<h1>Neautorizovaný přístup</h1>";
        echo "<p>Pro přístup k této stránce musíte být přihlášeni.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    $user = getCurrentUser();
    if (!isAdminOrManager()) {
        echo "<h1>Nedostatečná oprávnění</h1>";
        echo "<p>Pro přístup k této stránce musíte mít oprávnění administrátora nebo manažera.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    // Získání seznamu aktivních inventurních relací
    $stmt = $pdo->query("
        SELECT id, CONCAT('Inventura #', id) AS name, status
        FROM inventory_sessions
        WHERE status = 'active'
    ");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($sessions)) {
        echo "<h1>Žádné aktivní inventurní relace</h1>";
        echo "<p>Nebyly nalezeny žádné aktivní inventurní relace.</p>";
        echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
        exit;
    }

    // Zpracování formuláře pro synchronizaci
    $syncResults = [];
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['sync'])) {
        $sessionId = $_POST['session_id'] ?? null;
        $productId = $_POST['product_id'] ?? null;
        
        if (!$sessionId) {
            $syncResults['error'] = 'Nebyla vybrána žádná inventurní relace.';
        } elseif (!$productId) {
            $syncResults['error'] = 'Nebylo zadáno ID produktu.';
        } else {
            // Kontrola, zda vybraná relace existuje a je aktivní
            $stmt = $pdo->prepare("
                SELECT id, CONCAT('Inventura #', id) AS name, status
                FROM inventory_sessions
                WHERE id = ? AND status = 'active'
            ");
            $stmt->execute([$sessionId]);
            $session = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$session) {
                $syncResults['error'] = 'Vybraná inventurní relace neexistuje nebo není aktivní.';
            } else {
                // Začátek transakce
                $pdo->beginTransaction();
                
                try {
                    // Získání aktuálního stavu z tabulky stockcurrent
                    $stmt = $pdo->prepare("
                        SELECT units
                        FROM stockcurrent
                        WHERE product = ?
                    ");
                    $stmt->execute([$productId]);
                    $stockcurrent = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    $currentUnits = $stockcurrent ? $stockcurrent['units'] : 0;
                    
                    // Aktualizace zadaného množství pro administrátory a manažery
                    $stmt = $pdo->prepare("
                        UPDATE inventory_entries ie
                        JOIN inventory_users iu ON ie.user_id = iu.id
                        SET ie.zadane_mnozstvi = ?
                        WHERE ie.product_id = ?
                        AND ie.session_id = ?
                        AND ie.status = 'active'
                        AND iu.role IN ('admin', 'manager')
                    ");
                    $stmt->execute([$currentUnits, $productId, $sessionId]);
                    
                    $updatedCount = $stmt->rowCount();
                    
                    // Commit transakce
                    $pdo->commit();
                    
                    $syncResults['success'] = true;
                    $syncResults['message'] = "Synchronizace byla úspěšně dokončena. Aktualizováno $updatedCount záznamů.";
                    $syncResults['product_id'] = $productId;
                    $syncResults['current_units'] = $currentUnits;
                    
                    // Získání informací o produktu
                    $stmt = $pdo->prepare("
                        SELECT name, code
                        FROM products
                        WHERE id = ?
                    ");
                    $stmt->execute([$productId]);
                    $product = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($product) {
                        $syncResults['product_name'] = $product['name'];
                        $syncResults['product_code'] = $product['code'];
                    }
                } catch (Exception $e) {
                    // Rollback transakce v případě chyby
                    $pdo->rollBack();
                    $syncResults['error'] = 'Došlo k chybě při synchronizaci: ' . $e->getMessage();
                }
            }
        }
    }

    // Zobrazení HTML
    echo "<!DOCTYPE html>
<html lang='cs'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Synchronizace zadaného množství pro konkrétní produkt</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input, button { padding: 8px; font-size: 16px; width: 100%; box-sizing: border-box; }
        button { background-color: #4CAF50; color: white; border: none; cursor: pointer; padding: 10px 15px; }
        button:hover { background-color: #45a049; }
        .success { color: green; padding: 10px; background-color: #f0fff0; border: 1px solid #d0e9c6; border-radius: 4px; margin: 10px 0; }
        .error { color: red; padding: 10px; background-color: #fff0f0; border: 1px solid #e9c6c6; border-radius: 4px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        tr:hover { background-color: #f1f1f1; }
        .actions { margin: 20px 0; }
        .actions a { 
            display: inline-block; 
            padding: 10px 15px; 
            background-color: #4CAF50; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
            margin-right: 10px; 
        }
        .actions a:hover { background-color: #45a049; }
        .product-info { 
            background-color: #f8f9fa; 
            padding: 15px; 
            border-radius: 4px; 
            margin-top: 20px; 
            border: 1px solid #ddd; 
        }
        .product-info h3 { margin-top: 0; }
        .product-info p { margin: 5px 0; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Synchronizace zadaného množství pro konkrétní produkt</h1>
        
        <div class='actions'>
            <a href='index.html'>Zpět na hlavní stránku</a>
            <a href='sync_inventory.php'>Synchronizace všech produktů</a>
        </div>";
        
    if (!empty($syncResults)) {
        if (isset($syncResults['success']) && $syncResults['success']) {
            echo "<div class='success'>{$syncResults['message']}</div>";
            
            if (isset($syncResults['product_name'])) {
                echo "<div class='product-info'>";
                echo "<h3>Informace o produktu</h3>";
                echo "<p><strong>ID:</strong> {$syncResults['product_id']}</p>";
                echo "<p><strong>Název:</strong> {$syncResults['product_name']}</p>";
                echo "<p><strong>Kód:</strong> {$syncResults['product_code']}</p>";
                echo "<p><strong>Aktuální stav:</strong> {$syncResults['current_units']}</p>";
                echo "</div>";
            }
        } elseif (isset($syncResults['error'])) {
            echo "<div class='error'>{$syncResults['error']}</div>";
        }
    }
        
    echo "<form method='post'>
            <div class='form-group'>
                <label for='session_id'>Vyberte inventurní relaci:</label>
                <select name='session_id' id='session_id' required>";
                
    foreach ($sessions as $session) {
        echo "<option value='{$session['id']}'>{$session['name']}</option>";
    }
                
    echo "</select>
            </div>
            
            <div class='form-group'>
                <label for='product_id'>ID produktu:</label>
                <input type='text' name='product_id' id='product_id' required>
            </div>
            
            <div class='form-group'>
                <button type='submit' name='sync'>Synchronizovat zadané množství pro tento produkt</button>
            </div>
        </form>
        
        <h2>Aktivní inventurní relace</h2>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Název</th>
                    <th>Stav</th>
                </tr>
            </thead>
            <tbody>";
                
    foreach ($sessions as $session) {
        echo "<tr>";
        echo "<td>{$session['id']}</td>";
        echo "<td>{$session['name']}</td>";
        echo "<td>{$session['status']}</td>";
        echo "</tr>";
    }
                
    echo "</tbody>
        </table>
    </div>
</body>
</html>";

} catch (PDOException $e) {
    echo "<h1>Chyba při připojení k databázi</h1>";
    echo "<p>Došlo k chybě při připojení k databázi: " . $e->getMessage() . "</p>";
    echo "<p><a href='index.html'>Zpět na hlavní stránku</a></p>";
}
