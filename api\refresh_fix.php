<?php
/**
 * API endpoint pro aktualizaci inventurní<PERSON>ů - opravená verze
 */

// Načtení konfigurace a připojení k databázi
require_once __DIR__ . '/../utils/database.php';
require_once __DIR__ . '/../utils/auth.php';

// Nastavení hlaviček pro CORS a JSON
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json');

// Zpracování OPTIONS požadavku pro CORS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Kontrola, zda je uživatel přihlášen
if (!isLoggedIn()) {
    error_log("refresh_fix.php - uživatel není p<PERSON>");
    http_response_code(401);
    echo json_encode(['error' => 'Neautorizovaný přístup']);
    exit;
}

// Kontrola, zda je uživatel admin nebo manažer
if (!isAdminOrManager()) {
    error_log("refresh_fix.php - uživatel nemá oprávnění");
    http_response_code(403);
    echo json_encode(['error' => 'Nedostatečná oprávnění']);
    exit;
}

// Získání JSON vstupu
$input = json_decode(file_get_contents('php://input'), true);
error_log("refresh_fix.php - dekódovaný JSON vstup: " . print_r($input, true));

if (!$input || !isset($input['session_id'])) {
    error_log("refresh_fix.php - neplatný JSON nebo chybějící session_id");
    http_response_code(400);
    echo json_encode(['error' => 'Neplatný JSON nebo chybějící session_id']);
    exit;
}

// Získání ID relace přímo z vstupu
$sessionId = intval($input['session_id']);
error_log("refresh_fix.php - session_id: " . $sessionId);

if ($sessionId <= 0) {
    error_log("refresh_fix.php - neplatné ID relace");
    http_response_code(400);
    echo json_encode(['error' => 'Neplatné ID relace']);
    exit;
}

// Připojení k databázi
$pdo = getDbConnection();

try {
    // Kontrola, zda relace existuje a je aktivní
    $stmt = $pdo->prepare("SELECT * FROM inventory_sessions WHERE id = ? AND status = 'active'");
    $stmt->execute([$sessionId]);
    $session = $stmt->fetch();

    if (!$session) {
        error_log("refresh_fix.php - relace nenalezena nebo není aktivní");
        http_response_code(404);
        echo json_encode(['error' => 'Inventurní relace nenalezena nebo není aktivní']);
        exit;
    }

    // Získání aktuálních stavů zásob
    $stockStmt = $pdo->prepare("SELECT product, units FROM stockcurrent");
    $stockStmt->execute();
    $currentStock = [];
    while ($row = $stockStmt->fetch()) {
        $currentStock[$row['product']] = $row['units'];
    }

    // Kontrola, zda existuje tabulka previous_stock
    $previousStockStmt = $pdo->prepare("
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'previous_stock'
    ");
    $previousStockStmt->execute();
    $previousStockTableExists = $previousStockStmt->fetchColumn() > 0;

    if (!$previousStockTableExists) {
        // Vytvoření tabulky previous_stock
        $pdo->exec("
            CREATE TABLE `previous_stock` (
              `id` INT AUTO_INCREMENT PRIMARY KEY,
              `product_id` VARCHAR(255) NOT NULL,
              `units` DECIMAL(10,3) NOT NULL DEFAULT 0,
              `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              UNIQUE KEY `uk_previous_stock_product` (`product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");

        // Naplnění tabulky previous_stock aktuálními stavy zásob
        foreach ($currentStock as $productId => $units) {
            $pdo->prepare("INSERT INTO previous_stock (product_id, units) VALUES (?, ?)")
                ->execute([$productId, $units]);
        }

        $previousStock = $currentStock;
    } else {
        // Získání předchozích stavů zásob
        $previousStockStmt = $pdo->prepare("SELECT product_id, units FROM previous_stock");
        $previousStockStmt->execute();
        $previousStock = [];
        while ($row = $previousStockStmt->fetch()) {
            $previousStock[$row['product_id']] = $row['units'];
        }
    }

    // Kontrola, zda existuje tabulka inventory_totals
    $inventoryTotalsStmt = $pdo->prepare("
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'inventory_totals'
    ");
    $inventoryTotalsStmt->execute();
    $inventoryTotalsTableExists = $inventoryTotalsStmt->fetchColumn() > 0;

    if (!$inventoryTotalsTableExists) {
        // Vytvoření tabulky inventory_totals
        $pdo->exec("
            CREATE TABLE `inventory_totals` (
              `id` INT AUTO_INCREMENT PRIMARY KEY,
              `session_id` INT NOT NULL,
              `product_id` VARCHAR(255) NOT NULL,
              `total_zadane_mnozstvi` DECIMAL(10,3) NOT NULL DEFAULT 0,
              `last_updated` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
              UNIQUE KEY `uk_inventory_totals_session_product` (`session_id`, `product_id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
    }

    // Získání produktů v inventuře
    $productsStmt = $pdo->prepare("
        SELECT DISTINCT product_id
        FROM inventory_entries
        WHERE session_id = ? AND status = 'active'
    ");
    $productsStmt->execute([$sessionId]);
    $products = $productsStmt->fetchAll(PDO::FETCH_COLUMN);

    // Aktualizace zadaného množství pro každý produkt
    $updatedCount = 0;
    foreach ($products as $productId) {
        // Získání aktuálního stavu zásob
        $currentStockValue = isset($currentStock[$productId]) ? $currentStock[$productId] : 0;

        // Získání předchozího stavu zásob
        $previousStockValue = isset($previousStock[$productId]) ? $previousStock[$productId] : $currentStockValue;

        // Výpočet rozdílu mezi předchozím a aktuálním stavem zásob
        $stockDifference = $previousStockValue - $currentStockValue;

        // Aktualizujeme zadané množství pouze pokud došlo ke změně stavu zásob
        if ($stockDifference != 0) {
            // Kontrola, zda existuje záznam v inventory_totals
            $totalsStmt = $pdo->prepare("
                SELECT id, total_zadane_mnozstvi
                FROM inventory_totals
                WHERE product_id = ? AND session_id = ?
            ");
            $totalsStmt->execute([$productId, $sessionId]);
            $total = $totalsStmt->fetch();

            if ($total) {
                // Aktualizace existujícího záznamu
                $newTotal = $total['total_zadane_mnozstvi'] - $stockDifference;
                
                // Zajistíme, že zadané množství nebude záporné
                if ($newTotal < 0) {
                    $newTotal = 0;
                }
                
                $updateStmt = $pdo->prepare("
                    UPDATE inventory_totals
                    SET total_zadane_mnozstvi = ?
                    WHERE id = ?
                ");
                $updateStmt->execute([$newTotal, $total['id']]);
                $updatedCount++;
            } else {
                // Získání zadaného množství z inventory_entries
                $entriesStmt = $pdo->prepare("
                    SELECT SUM(zadane_mnozstvi) as total
                    FROM inventory_entries
                    WHERE product_id = ? AND session_id = ? AND status = 'active'
                ");
                $entriesStmt->execute([$productId, $sessionId]);
                $entriesTotal = $entriesStmt->fetchColumn();
                
                if ($entriesTotal === false) {
                    $entriesTotal = 0;
                }
                
                // Vytvoření nového záznamu
                $newTotal = $entriesTotal - $stockDifference;
                
                // Zajistíme, že zadané množství nebude záporné
                if ($newTotal < 0) {
                    $newTotal = 0;
                }
                
                $insertStmt = $pdo->prepare("
                    INSERT INTO inventory_totals (session_id, product_id, total_zadane_mnozstvi)
                    VALUES (?, ?, ?)
                ");
                $insertStmt->execute([$sessionId, $productId, $newTotal]);
                $updatedCount++;
            }
        }

        // Aktualizace předchozího stavu zásob
        $updatePreviousStockStmt = $pdo->prepare("
            INSERT INTO previous_stock (product_id, units)
            VALUES (?, ?)
            ON DUPLICATE KEY UPDATE units = ?
        ");
        $updatePreviousStockStmt->execute([$productId, $currentStockValue, $currentStockValue]);
    }

    error_log("refresh_fix.php - celkem aktualizováno záznamů: $updatedCount");
    http_response_code(200);
    echo json_encode(['success' => true, 'message' => 'Inventurní záznamy byly úspěšně aktualizovány']);
} catch (Exception $e) {
    error_log("Chyba při aktualizaci inventurních záznamů: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(['error' => 'Došlo k chybě při aktualizaci inventurních záznamů: ' . $e->getMessage()]);
}
?>
