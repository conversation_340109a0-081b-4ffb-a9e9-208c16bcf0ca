<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Oprava přihlášení</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; }
    </style>
</head>
<body>
    <h1>Diagnostika a oprava přihlášení</h1>
    
    <div class="test-section">
        <h2>1. Test databáze a tabulek</h2>
        <button onclick="testDatabase()">Test databáze</button>
        <div id="db-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Test API simple_auth</h2>
        <button onclick="testSimpleAuth()">Test simple_auth API</button>
        <div id="auth-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Test přihlášení</h2>
        <input type="text" id="username" placeholder="Username" value="admin">
        <input type="password" id="password" placeholder="Password" value="admin123">
        <button onclick="testLogin()">Test přihlášení</button>
        <div id="login-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Vytvoření admin uživatele</h2>
        <button onclick="createAdmin()">Vytvořit admin uživatele</button>
        <div id="create-result"></div>
    </div>

    <script>
        async function testDatabase() {
            const resultDiv = document.getElementById('db-result');
            resultDiv.innerHTML = '<div class="info">Testování databáze...</div>';
            
            try {
                const response = await fetch('debug_auth.php');
                const text = await response.text();
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Databáze testována</div>
                    <iframe src="debug_auth.php" width="100%" height="400"></iframe>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function testSimpleAuth() {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.innerHTML = '<div class="info">Testování simple_auth API...</div>';
            
            try {
                const response = await fetch('api/simple_auth.php?action=test');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Simple auth API funguje</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('login-result');
            
            resultDiv.innerHTML = '<div class="info">Testování přihlášení...</div>';
            
            try {
                console.log('Pokus o přihlášení:', username, password);
                
                const response = await fetch('api/simple_auth.php?action=login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                const text = await response.text();
                console.log('Response text:', text);
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    throw new Error('Odpověď není JSON: ' + text);
                }
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <div class="success">✓ Přihlášení úspěšné!</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">✗ Přihlášení neúspěšné: ${data.error}</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        async function createAdmin() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.innerHTML = '<div class="info">Vytváření admin uživatele...</div>';
            
            try {
                const response = await fetch('create_admin.php');
                const text = await response.text();
                
                resultDiv.innerHTML = `
                    <div class="success">✓ Admin uživatel vytvořen</div>
                    <iframe src="create_admin.php" width="100%" height="300"></iframe>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">✗ Chyba: ${error.message}</div>`;
            }
        }
        
        // Automatický test při načtení
        window.onload = function() {
            testDatabase();
            setTimeout(testSimpleAuth, 1000);
        };
    </script>
</body>
</html>
